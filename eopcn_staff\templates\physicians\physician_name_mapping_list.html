{% extends "base.html" %}

{% block title %}Physician Name Mappings{% endblock %}

{% block content %}
<style>
body {
  font-family: arial, sans-serif;
  margin: 20px;
  padding: 0;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
}

.container {
  max-width: 1200px;
  margin: auto;
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #f2f2f2;
}

th {
  background-color: #4CAF50;
  color: white;
}

.button {
  background-color: #4CAF50;
  color: white;
  padding: 10px 15px;
  text-decoration: none;
  border-radius: 4px;
  display: inline-block;
  margin: 5px;
}

.button:hover {
  background-color: #45a049;
}

.button.blue {
  background-color: #008CBA;
}

.button.blue:hover {
  background-color: #007BB5;
}

.link-button {
  background-color: #17a2b8;
  color: white;
  padding: 3px 8px;
  text-decoration: none;
  border-radius: 3px;
  font-size: 12px;
}

.link-button:hover {
  background-color: #138496;
}

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.alert-error {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}
</style>

<div class="container">
    <h2>Physician Name Mappings</h2>
    
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }}">{{ message }}</div>
        {% endfor %}
    {% endif %}
    
    <div style="margin-bottom: 20px;">
        <a href="{% url 'add_physician_name_mapping' %}" class="button">+ Add New Mapping</a>
        <a href="{% url 'physician_panel_import' %}" class="button blue">← Back to Import Panel</a>
    </div>
    
    <p><strong>Total Mappings:</strong> {{ mappings|length }}</p>
    
    <table>
        <thead>
            <tr>
                <th>Physician ID</th>
                <th>AH Full Name</th>
                <th>EOPCN Full Name</th>
                <th>Date Modified</th>
                <th>Date Created</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for mapping in mappings %}
                <tr>
                    <td>{{ mapping.physician_id|default:"Not assigned" }}</td>
                    <td>{{ mapping.ah_full_name }}</td>
                    <td>{{ mapping.eopcn_full_name }}</td>
                    <td>{{ mapping.date_modified|date:"M. d, Y H:i" }}</td>
                    <td>{{ mapping.date_created|date:"M. d, Y H:i" }}</td>
                    <td>
                        <a href="{% url 'edit_physician_name_mapping' mapping.row_id %}" class="link-button">Edit</a>
                    </td>
                </tr>
            {% empty %}
                <tr>
                    <td colspan="6" style="text-align: center; color: #666;">No mappings found. <a href="{% url 'add_physician_name_mapping' %}">Add the first mapping</a>.</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{% endblock %}
