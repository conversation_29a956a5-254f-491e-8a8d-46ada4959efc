# GitHub Actions Setup for Leave Reminders

This guide will help you set up automated leave reminders using GitHub Actions that run 24/7 without needing your computer on.

## 🚀 Setup Steps

### 1. Push Code to GitHub

First, make sure your code is in a GitHub repository:

```bash
# If you haven't already, initialize git and push to GitHub
git init
git add .
git commit -m "Add leave reminder system with GitHub Actions"
git branch -M main
git remote add origin https://github.com/yourusername/your-repo-name.git
git push -u origin main
```

### 2. Set Up GitHub Secrets

Go to your GitHub repository → Settings → Secrets and variables → Actions

Add these secrets:

#### Database Secrets:
- `DB_NAME`: `PCNQITeamSQLDB`
- `DB_USER`: `<EMAIL>`
- `DB_PASSWORD`: `Juh49280`
- `DB_HOST`: `pcnqisqlsvr.database.windows.net`
- `DB_PORT`: `1433`

#### Email Secrets:
- `EMAIL_HOST`: `smtp.office365.com`
- `EMAIL_PORT`: `587`
- `EMAIL_USE_TLS`: `True`
- `EMAIL_HOST_USER`: `<EMAIL>`
- `EMAIL_HOST_PASSWORD`: `Juh49280`

#### Django Secret:
- `DJANGO_SECRET_KEY`: Generate a new secret key for GitHub Actions

### 3. Test the Workflow

#### Manual Test:
1. Go to your GitHub repository
2. Click "Actions" tab
3. Click "Daily Leave Reminders" workflow
4. Click "Run workflow" button
5. Click "Run workflow" to start it manually

#### Check Results:
- Green checkmark = Success
- Red X = Failed (check logs)
- Click on the workflow run to see detailed logs

### 4. Schedule Configuration

The workflow is set to run:
- **Summer (MDT)**: 9:00 AM MST = 3:00 PM UTC
- **Winter (MST)**: 9:00 AM MST = 4:00 PM UTC

To change the time, edit `.github/workflows/daily-leave-reminders.yml`:

```yaml
schedule:
  - cron: '0 15 * * *'  # 9 AM MST (summer)
  - cron: '0 16 * * *'  # 9 AM MST (winter)
```

## 🔧 Troubleshooting

### Common Issues:

1. **Database Connection Failed**
   - Check that all DB_* secrets are set correctly
   - Verify the database allows connections from GitHub Actions IPs

2. **Email Sending Failed**
   - Check EMAIL_* secrets
   - Verify the email account allows SMTP access

3. **Workflow Not Running**
   - Check that the `.github/workflows/` folder is in your repository
   - Verify the YAML syntax is correct

### Viewing Logs:
1. Go to Actions tab in GitHub
2. Click on a workflow run
3. Click on "send-reminders" job
4. Expand the steps to see detailed logs

## 📊 Monitoring

### Check if it's working:
1. **GitHub Actions tab**: See if workflows are running successfully
2. **Email inbox**: Check if reminder emails are being sent
3. **Database**: Verify that `reminder_sent` flags are being updated

### Weekly Check:
- Review the Actions tab for any failed runs
- Check email delivery reports
- Verify reminders are being sent at the right times

## 🎯 Benefits

✅ **Runs 24/7** - No need for your computer to be on
✅ **Free** - GitHub Actions provides 2,000 minutes/month free
✅ **Reliable** - GitHub's infrastructure handles the scheduling
✅ **Logs** - Full visibility into what's happening
✅ **Manual trigger** - Can run on-demand for testing

## 🔄 Next Steps

1. **Test thoroughly** with manual workflow runs
2. **Monitor for a week** to ensure it's working correctly
3. **Set up notifications** if you want alerts when workflows fail
4. **Consider backup scheduling** using a different service if critical

## 📝 Notes

- GitHub Actions runs on UTC time, so we convert MST to UTC
- The workflow handles both summer (MDT) and winter (MST) time zones
- Secrets are encrypted and only accessible to your repository
- You can add more scheduled times if needed (e.g., twice daily)
