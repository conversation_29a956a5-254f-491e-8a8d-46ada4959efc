{% extends "base.html" %}

{% block content %}

<style>
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.column {
  float: left;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.row::after {
  content: "";
  clear: both;
  display: table;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #f2f2f2;
}

h2 {
  text-align: center;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  cursor: pointer;
  border-radius: 4px;
}

.green-button {
  background-color: #5dbea3;
}

.green-button:hover {
  background-color: darkgreen;
}

.dataTables_filter, .dataTables_length {
  padding-bottom: 10px;
}

.dataTables_filter {
  float: left !important;
  text-align: left !important;
}

.dataTables_length {
  float: right !important;
  text-align: right !important;
}

@media screen and (max-width: 650px) {
  .column {
    width: 100%;
    display: block;
  }
}
</style>

<h2>Staff Roles List</h2>
{% if messages %}
        <script>
            {% for message in messages %}
                alert("{{ message }}");
            {% endfor %}
        </script>
{% endif %}
<div class="row">
  <div class="column">
    <div class="button">
      <a href="{% url 'add_staff_role' %}">+ Add Staff Role</a>
    </div>
    <table id="staffRolesTable" class="display">
      <thead>
        <tr>
          <th>Role Name</th>
          <th>AH Role Title</th>
          <th>AH Role Category</th>
          <th>Start Date</th>
          <th>End Date</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for role in staff_roles %}
          <tr>
            <td>{{ role.role_name }}</td>
            <td>{{ role.ah_role_title }}</td>
            <td>{{ role.ah_role_category }}</td>
            <td>{{ role.start_date|default_if_none:"" }}</td>
            <td>{{ role.end_date|default_if_none:"" }}</td>
            <td>
              <a href="{% url 'edit_staff_role' role.pk %}">Edit</a>
            </td>
          </tr>
        {% endfor %}
      </tbody>    
    </table>
  </div>
</div>

<!-- Include jQuery first -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Include DataTables JS after jQuery -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>


<script>
    $(document).ready(function() {
        $('#staffRolesTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "pageLength": 50,
            "order": [[0, 'asc']],
            "dom": '<"top"fp><"clear">rt<"clear">'
        });
    });
</script>

{% endblock %}
