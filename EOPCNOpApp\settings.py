"""
Django settings for EOPCNOpApp project.

Generated by 'django-admin startproject' using Django 4.2.15.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

from pathlib import Path
import os
from django.urls import reverse_lazy


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Determine if we're running in production by checking either Azure's WEBSITE_HOSTNAME
# or our custom DJANGO_ENVIRONMENT variable
IS_PRODUCTION = os.environ.get('WEBSITE_HOSTNAME') is not None or os.environ.get('DJANGO_ENVIRONMENT') == 'production'

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
if IS_PRODUCTION:
    SECRET_KEY = os.environ.get('SECRET_KEY')
    if not SECRET_KEY:
        raise ValueError("Secret key not configured in production environment")
else:
    # Only for local development
    SECRET_KEY = 'django-insecure-&6kj1hz@1e0dl&jjeg$$j%_2=d5wq_*kdw4bixbjhvnxs_zvjv'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = not IS_PRODUCTION  # Set DEBUG to True for local development, False for production

# ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', '').split(',')
ALLOWED_HOSTS = ['eopcnoperations.azurewebsites.net', '127.0.0.1', 'localhost']


AUTHENTICATION_BACKENDS = (
    'django_auth_adfs.backend.AdfsBackend',
    'django.contrib.auth.backends.ModelBackend',
)

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',  # Humanize app for template filters
    # Your apps
    'django_auth_adfs',
    'eopcn_staff',  # Staff app
    'storages',  # Storage app for handling files
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',  # Add this after SecurityMiddleware
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django_auth_adfs.middleware.LoginRequiredMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Azure AD Configuration - Use environment variables for security
if IS_PRODUCTION:
    client_id = os.environ.get('AZURE_CLIENT_ID')
    client_secret = os.environ.get('AZURE_CLIENT_SECRET')
    tenant_id = os.environ.get('AZURE_TENANT_ID')

    # Validate required environment variables
    if not all([client_id, client_secret, tenant_id]):
        raise ValueError("Azure AD credentials not properly configured in production environment")

    redirect_uris = [
        "https://eopcnoperations.azurewebsites.net/eopcn/oauth2/callback"
    ]
else:
    # Development configuration
    client_id = "d7628e66-31af-4eee-8290-fbb45a23f7fa"
    client_secret = "****************************************"
    tenant_id = "8303f5d5-24fe-4856-9015-25d9a4ee9400"

    redirect_uris = [
        "http://localhost:8000/eopcn/oauth2/callback",
        "http://127.0.0.1:8000/eopcn/oauth2/callback"
    ]

# For django-auth-adfs
AUTH_ADFS = {
    "AUDIENCE": client_id,
    "CLIENT_ID": client_id,
    "CLIENT_SECRET": client_secret,
    "CLAIM_MAPPING": {
        "first_name": "given_name",
        "last_name": "family_name",
        "email": "email",
    },
    "GROUPS_CLAIM": "roles",
    "MIRROR_GROUPS": True,
    "USERNAME_CLAIM": "upn",
    "TENANT_ID": tenant_id,
    "RELYING_PARTY_ID": client_id,
    "REDIR_URI": redirect_uris
}
# ADFS_REDIRECT_URI = redirect_uri

LOGIN_URL = reverse_lazy('django_auth_adfs:login')
LOGIN_REDIRECT_URL = "/"
LOGOUT_REDIRECT_URL = reverse_lazy('django_auth_adfs:logout')

ROOT_URLCONF = 'EOPCNOpApp.urls'

CSRF_TRUSTED_ORIGINS = [
    'https://eopcnoperations.azurewebsites.net',
    'http://localhost:8000',
    'http://127.0.0.1:8000',
]

# Apply security settings only in production
if IS_PRODUCTION:
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    USE_X_FORWARDED_HOST = True
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    X_FRAME_OPTIONS = 'DENY'
    SECURE_HSTS_SECONDS = 31536000  # 1 year
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
else:
    # Local development settings
    SECURE_SSL_REDIRECT = False
    SESSION_COOKIE_SECURE = False
    CSRF_COOKIE_SECURE = False


TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'eopcn_staff/templates/staff'],  # Add this if necessary
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'eopcn_staff.context_processors.logo_url',  # Add this line to include the logo_url context processor
            ],
        },
    },
]

WSGI_APPLICATION = 'EOPCNOpApp.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# Database configuration - environment-specific
if IS_PRODUCTION:
    # Production database settings - try multiple authentication methods
    db_user = os.environ.get('DB_USER')
    db_password = os.environ.get('DB_PASSWORD')

    if db_user and db_password:
        # Use username/password authentication if provided
        DATABASES = {
            'default': {
                'ENGINE': 'mssql',
                'NAME': os.environ.get('DB_NAME', 'PCNQITeamSQLDB'),
                'USER': db_user,
                'PASSWORD': db_password,
                'HOST': os.environ.get('DB_HOST', 'pcnqisqlsvr.database.windows.net'),
                'PORT': os.environ.get('DB_PORT', '1433'),
                'OPTIONS': {
                    'driver': 'ODBC Driver 18 for SQL Server',
                    'Encrypt': 'yes',
                    'TrustServerCertificate': 'no',
                    'extra_params': "Authentication=ActiveDirectoryPassword",
                },
            }
        }
    else:
        # Fall back to Managed Identity authentication
        DATABASES = {
            'default': {
                'ENGINE': 'mssql',
                'NAME': os.environ.get('DB_NAME', 'PCNQITeamSQLDB'),
                'HOST': os.environ.get('DB_HOST', 'pcnqisqlsvr.database.windows.net'),
                'PORT': os.environ.get('DB_PORT', '1433'),
                'OPTIONS': {
                    'driver': 'ODBC Driver 18 for SQL Server',
                    'Encrypt': 'yes',
                    'TrustServerCertificate': 'no',
                    'extra_params': "Authentication=ActiveDirectoryMsi",
                },
            }
        }
else:
    # Local development database settings
    DATABASES = {
        'default': {
            'ENGINE': 'mssql',
            'NAME': 'PCNQITeamSQLDB',
            'USER': '<EMAIL>',
            'PASSWORD': 'Juh49280',
            'HOST': 'pcnqisqlsvr.database.windows.net',
            'PORT': '1433',
            'OPTIONS': {
                'driver': 'ODBC Driver 18 for SQL Server',
                'TrustServerCertificate': 'no',
                'Encrypt': 'yes',
                'extra_params': "Authentication=ActiveDirectoryPassword",
            },
        }
    }


# DATABASES = {
#     'default': {
#         'ENGINE': 'mssql',
#         'NAME': 'PCNQITeamSQLDB',
#         'USER': '<EMAIL>',   # Your Azure AD username
#         'PASSWORD': 'Juh49280',  # Your Azure AD password
#         'HOST': 'pcnqisqlsvr.database.windows.net',
#         'PORT': '1433',
#         'OPTIONS': {
#             'driver': 'ODBC Driver 18 for SQL Server',
#             'TrustServerCertificate': 'no',
#             'Encrypt': 'yes',
#             'extra_params': "Authentication=ActiveDirectoryPassword",  # Combined element from both configurations
#         },
#     }
# }


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'  # This is where collectstatic collects files

# Add Whitenoise for serving static files in production
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'


# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.office365.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'

# Get email password from environment variable or use a default for local development
if IS_PRODUCTION:
    EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')
    if not EMAIL_HOST_PASSWORD:
        raise ValueError("Email password not configured in production environment")
else:
    # For local development only - in production, use environment variables
    EMAIL_HOST_PASSWORD = 'Juh49280'  # Consider using a development-only password

DEFAULT_FROM_EMAIL = '<EMAIL>'

# Azure Storage configuration
AZURE_ACCOUNT_NAME = 'eopcnstaffphotos'
AZURE_CONTAINER = 'staffphotos'

# Get storage key from environment variable
if IS_PRODUCTION:
    AZURE_ACCOUNT_KEY = os.environ.get('AZURE_STORAGE_KEY')
    if not AZURE_ACCOUNT_KEY:
        raise ValueError("Azure Storage Key not configured in production environment")
else:
    # For local development only
    AZURE_ACCOUNT_KEY = '****************************************************************************************'

# Storage settings
DEFAULT_FILE_STORAGE = 'storages.backends.azure_storage.AzureStorage'
AZURE_CUSTOM_DOMAIN = f'{AZURE_ACCOUNT_NAME}.blob.core.windows.net'

# URL to access media files
MEDIA_URL = f'https://{AZURE_CUSTOM_DOMAIN}/{AZURE_CONTAINER}/'

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose' if IS_PRODUCTION else 'simple',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO' if IS_PRODUCTION else 'DEBUG',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO' if IS_PRODUCTION else 'DEBUG',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'ERROR' if IS_PRODUCTION else 'DEBUG',
            'propagate': False,
        },
        'eopcn_staff': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# GitHub Actions environment detection
if os.environ.get('GITHUB_ACTIONS'):
    # Use GitHub Actions specific settings
    DEBUG = False
    ALLOWED_HOSTS = ['*']

    # Ensure required environment variables are set
    required_vars = ['DB_USER', 'DB_PASSWORD', 'EMAIL_HOST_PASSWORD', 'DJANGO_SECRET_KEY']
    missing_vars = [var for var in required_vars if not os.environ.get(var)]
    if missing_vars:
        raise ValueError(f"Missing required environment variables for GitHub Actions: {', '.join(missing_vars)}")

    # Override database settings for GitHub Actions
    db_user = os.environ.get('DB_USER')
    db_host = os.environ.get('DB_HOST', 'pcnqisqlsvr.database.windows.net')

    # Handle email-format usernames for Azure SQL Database
    if db_user and '@' in db_user and not db_user.endswith('@pcnqisqlsvr'):
        # Extract the username part before @ and append the server name
        username_part = db_user.split('@')[0]
        server_name = db_host.split('.')[0]  # Get 'pcnqisqlsvr' from 'pcnqisqlsvr.database.windows.net'
        db_user = f"{username_part}@{server_name}"

    DATABASES = {
        'default': {
            'ENGINE': 'mssql',
            'NAME': os.environ.get('DB_NAME', 'PCNQITeamSQLDB'),
            'USER': db_user,
            'PASSWORD': os.environ.get('DB_PASSWORD'),
            'HOST': db_host,
            'PORT': os.environ.get('DB_PORT', '1433'),
            'OPTIONS': {
                'driver': 'ODBC Driver 18 for SQL Server',
                'TrustServerCertificate': 'yes',  # For GitHub Actions
                'Encrypt': 'yes',
                # Use SQL Server authentication instead of Active Directory for GitHub Actions
            },
        }
    }

    # Override email settings for GitHub Actions
    EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.office365.com')
    EMAIL_PORT = int(os.environ.get('EMAIL_PORT') or '587')
    EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True').lower() == 'true'
    EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '<EMAIL>')
    EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')

    # Override secret key for GitHub Actions
    SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY')

    # Override Azure credentials for GitHub Actions
    client_id = os.environ.get('AZURE_CLIENT_ID', client_id)
    client_secret = os.environ.get('AZURE_CLIENT_SECRET', client_secret)
    tenant_id = os.environ.get('AZURE_TENANT_ID', tenant_id)

    # Update AUTH_ADFS with GitHub Actions values
    AUTH_ADFS.update({
        "CLIENT_ID": client_id,
        "CLIENT_SECRET": client_secret,
        "TENANT_ID": tenant_id,
        "AUDIENCE": client_id,
        "RELYING_PARTY_ID": client_id,
    })