// Set global DataTable defaults
$.extend(true, $.fn.dataTable.defaults, {
    paging: true,
    searching: true,
    ordering: true,
    pageLength: 50,
    order: [[0, 'asc']],
    dom: '<"top"fp><"clear">rt<"bottom"Bip><"clear">',
    buttons: [
        'copy', 'csv', 'excel', 'pdf', 'print'
    ]
});

$(document).ready(function() {
    // Check if DataTable is already initialized and destroy it first
    if ($.fn.DataTable.isDataTable('#pcndatatable')) {
        $('#pcndatatable').DataTable().destroy();
    }

    // Initialize DataTable with fixedHeader directly in configuration (same as clinic_detail.html)
    const table = $('#pcndatatable').DataTable({
      paging: true,
      searching: true,
      ordering: true,
      pageLength: 50,
      order: [[0, 'asc']],

      // Put the filter and info on top, but the export buttons at the bottom
      dom: '<"top"fp><"clear">rt<"bottom"Bip><"clear">',
      buttons: [
        'copy', 'csv', 'excel', 'pdf', 'print'
      ],

      // Enable fixed header (same approach as clinic_detail.html)
      fixedHeader: true
    });

    console.log('DataTable initialized with fixedHeader: true');

    var latestYear = $('#toggleLatest').data('latest-year');
    var latestMonth = $('#toggleLatest').data('latest-month');

    $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
      var showOnlyActive = $('#toggleActive').is(':checked');
      var showOnlyLatest = $('#toggleLatest').is(':checked');
      var row = table.row(dataIndex).node();

      if (showOnlyActive && $(row).hasClass('role-ended')) {
          return false;
      }

      if ($('#toggleLatest').length && showOnlyLatest) {
          var month = parseInt(data[2]) || 0;
          var year = parseInt(data[3]) || 0;
          if (year !== latestYear || month !== latestMonth) {
              return false;
          }
      }

      return true;
    });

    // Trigger the filter at init
    table.draw();

    // Re-filter when checkbox changes
    $('#toggleActive, #toggleLatest').on('change', function() {
      table.draw();
    });
  });