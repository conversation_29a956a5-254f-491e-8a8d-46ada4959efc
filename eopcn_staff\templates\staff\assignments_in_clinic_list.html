{% extends "base.html" %}

{% block content %}
<h2>Assignments in Clinic</h2>
{% if messages %}
        <script>
            {% for message in messages %}
                alert("{{ message }}");
            {% endfor %}
        </script>
{% endif %}
<a href="{% url 'add_assignments_in_clinic' %}" class="btn btn-secondary">+ Add New Assignment</a>
<table id="assignmentTable" class="display">
    <thead>
        <tr>
            <th>Assignment Name</th>
            <th>Date Created</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for assignment in assignments %}
        <tr>
            <td>{{ assignment.assignment_name }}</td>
            <td>{{ assignment.date_created|date:"M d, Y" }}</td>
            <td>
                <a href="{% url 'edit_assignments_in_clinic' assignment.assignment_in_clinic_id %}" class="btn btn-primary">Edit</a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- Include jQuery first -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Include DataTables JS after jQuery -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>

<script>
    $(document).ready(function() {
        $('#assignmentTable').DataTable();
    });
</script>
{% endblock %}
