{% extends "base.html" %}
{% load humanize %}

{% block title %}Home{% endblock %}

{% block content %}
<div class="centered-text">
    <h2>Welcome to the EOPCN Operational Database</h2>
    <p>Select an option from the menu above to get started.</p>
</div>

<div class="dashboard">
    <div class="dashboard-header">
        <h3>Staff Database Statistics</h3>
        <p><strong>As of {{ today|date:"F j, Y" }}</strong></p>
    </div>
    
    <!-- Staff Statistics Row -->
    <div class="stats-category">
        <h4>Staff Statistics</h4>
        <div class="stats-row">
            <a href="{% url 'list_staff' %}" style="text-decoration: none; color: inherit;">
                <div class="stat-card">
                    <div class="stat-number">{{ active_staff_count }}</div>
                    <div class="stat-label">Total Active Staff</div>
                </div>
            </a>

            <a href="{% url 'staff_leaves' %}?active_leave=true" style="text-decoration: none; color: inherit;">
                <div class="stat-card">
                    <div class="stat-number">{{ on_leave_count }}</div>
                    <div class="stat-label">Staff On Temporary Leave</div>
                </div>
            </a>

            <a href="{% url 'list_staff' %}" style="text-decoration: none; color: inherit;">
                <div class="stat-card">
                    <div class="stat-number">{{ total_fte_active_staff }}</div>
                    <div class="stat-label">Total FTE for Active Staff</div>
                </div>
            </a>

            <a href="{% url 'position_list' %}?active_vacancy_type=available_positions" style="text-decoration: none; color: inherit;">
                <div class="stat-card">
                    <div class="stat-number">{{ vacancy_count }}</div>
                    <div class="stat-label">Vacancy Count</div>
                </div>  
            </a>

            <a href="{% url 'position_list' %}?allocation_type=centralized" style="text-decoration: none; color: inherit;">
                <div class="stat-card">
                    <div class="stat-number">{{ centralized_staff_count }}</div>
                    <div class="stat-label">Centralized Staff</div>
                </div>
            </a>
            
            <a href="{% url 'position_list' %}?allocation_type=ric" style="text-decoration: none; color: inherit;">
                <div class="stat-card">
                    <div class="stat-number">{{ ric_staff_count }}</div>
                    <div class="stat-label">RIC/Decentralized Staff</div>
                </div>
            </a>
        </div>
    </div>
    
    <!-- Primary Care Provider Statistics Row -->
    <div class="stats-category">
        <h4>Primary Care Provider Statistics</h4>
        <div class="stats-row">
            <a href="{% url 'physician_list' %}" style="text-decoration: none; color: inherit;">
                <div class="stat-card">
                    <div class="stat-number">{{ active_physicians_count }}</div>
                    <div class="stat-label">Active Primary Care Providers</div>
                </div>
            </a>

            <a href="{% url 'physician_panel_details' %}" style="text-decoration: none; color: inherit;">
                <div class="stat-card">
                    <div class="stat-number">{{ total_panel_size|intcomma }}</div>
                    <div class="stat-label">Total Panel Size ({% if latest_panel_month == 1 %}Jan{% elif latest_panel_month == 2 %}Feb{% elif latest_panel_month == 3 %}Mar{% elif latest_panel_month == 4 %}Apr{% elif latest_panel_month == 5 %}May{% elif latest_panel_month == 6 %}Jun{% elif latest_panel_month == 7 %}Jul{% elif latest_panel_month == 8 %}Aug{% elif latest_panel_month == 9 %}Sep{% elif latest_panel_month == 10 %}Oct{% elif latest_panel_month == 11 %}Nov{% elif latest_panel_month == 12 %}Dec{% else %}{{ latest_panel_month }}{% endif %}/{{ latest_panel_year }})</div>
                </div>
            </a>
        </div>
    </div>
    
    <!-- Clinic Statistics Row -->
    <div class="stats-category">
        <h4>Clinic Statistics</h4>
        <div class="stats-row">
            <a href="{% url 'clinic_list' %}" style="text-decoration: none; color: inherit;">
                <div class="stat-card">
                    <div class="stat-number">{{ active_clinics_count }}</div>
                    <div class="stat-label">Active Clinics</div>
                </div>
            </a>
        </div>
    </div>
</div>

<style>
    .centered-text {
        text-align: center;
        margin-top: 50px;
    }
    .dashboard {
        text-align: center;
        margin-top: 30px;
        max-width: 1400px;
        margin-left: auto;
        margin-right: auto;
    }

    .dashboard-header {
        text-align: left;
        margin-left: 20px;
    }
    
    .dashboard h3 {
        margin-bottom: 20px;
        font-size: 24px;
        color: #333;
    }
    .stats-category {
        margin-bottom: 50px;
    }
    
    .stats-category h4 {
        text-align: left;
        margin-left: 20px; /* Adjusted to align with dashboard padding if needed, or keep as is */
        color: #555;
        margin-bottom: 20px;
        font-weight: 600;
    }
    
    .stats-row {
        display: flex;
        flex-wrap: wrap;
        gap: 30px; /* Consistent gap for horizontal and vertical spacing */
        justify-content: flex-start; /* Align cards to the left; edge spacing now primarily from padding */
        margin: 0 auto; /* Centers the row block itself */
        padding: 0 40px; /* Increased side padding for the row, creating space at edges */
    }
    
    .stat-card {
        background-color: #f5f5f5;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: 25px 20px;
        width: 100%; /* Allows card to be responsive within its allocated space */
        max-width: 225px; /* Slightly adjusted max-width for better fit */
        text-align: center;
        transition: transform 0.3s;
        /* margin-bottom: 15px; Removed to rely on 'gap' for vertical spacing on wrap */
    
    }
    .stat-card:hover {
        transform: scale(1.05);
    }
    .stat-number {
        font-size: 36px;
        font-weight: bold;
        color: #0067b1;
        margin-bottom: 10px;
    }
    .stat-label {
        font-size: 16px;
        color: #666;
        line-height: 1.3;
    }
</style>

{% endblock %}
