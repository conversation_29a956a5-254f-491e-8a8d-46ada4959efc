-- 1. Add the new columns to the table
ALTER TABLE [dbo].[mh_physicians]
ADD [first_name] NVARCHAR(100) NULL,
    [last_name] NVARCHAR(100) NULL;
GO

-- 2. Update the new columns by splitting the physician_name
-- This assumes the format is consistently "Last Name, First Name"
UPDATE [dbo].[mh_physicians]
SET 
    [last_name] = LTRIM(RTRIM(
        CASE 
            WHEN CHARINDEX(', ', physician_name) > 0 
            THEN LEFT(physician_name, CHARINDEX(', ', physician_name) - 1)
            ELSE physician_name -- In case there's no comma
        END
    )),
    [first_name] = LTRIM(RTRIM(
        CASE 
            WHEN CHARINDEX(', ', physician_name) > 0 
            THEN SUBSTRING(physician_name, CHARINDEX(', ', physician_name) + 2, <PERSON><PERSON>(physician_name))
            ELSE NULL -- If there's no comma, we can't determine first name
        END
    ));
GO

-- 3. Verify the update worked correctly 
-- Select a few records to make sure the split worked as expected
SELECT TOP 20 
    [physician_id],
    [physician_name],
    [first_name],
    [last_name]
FROM [dbo].[mh_physicians];
GO

-- 4. Once verified, you may want to set these columns as NOT NULL if appropriate
-- ALTER TABLE [dbo].[mh_physicians] ALTER COLUMN [first_name] NVARCHAR(100) NOT NULL;
-- ALTER TABLE [dbo].[mh_physicians] ALTER COLUMN [last_name] NVARCHAR(100) NOT NULL;
