$(document).ready(function() {
    const table = $('#pcndatatable').DataTable({
      paging: true,
      searching: true,
      ordering: true,
      pageLength: 50,
      order: [[0, 'asc']],
      
      // Put the filter and info on top, but the export buttons at the bottom
      dom: '<"top"fp><"clear">rt<"bottom"Bip><"clear">',
      buttons: [
        'copy', 'csv', 'excel', 'pdf', 'print'
      ]
    });

    // If you still want your custom "toggleActivePosition" logic, keep your custom search
    $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
      var showOnlyActive = $('#toggleActive').is(':checked');
      var row = table.row(dataIndex).node();

      // Exclude rows with 'role-ended' if 'showOnlyActive' is checked
      if (showOnlyActive && $(row).hasClass('role-ended')) {
          return false;
      }
      return true;
    });

    // Trigger the filter at init
    table.draw();

    // Re-filter when checkbox changes
    $('#toggleActive').on('change', function() {
      table.draw();
    });
  });