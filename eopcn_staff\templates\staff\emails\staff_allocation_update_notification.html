<!DOCTYPE html>
<html>
<head>
    <title>Allocation Updated for {{ assignment.staff.first_name }} {{ assignment.staff.last_name }}</title>
</head>
<body>
    <h2>Allocation Updated for {{ assignment.staff.first_name }} {{ assignment.staff.last_name }}</h2>

    <p>Hello,</p>

    <p>
        The allocation details for <strong>{{ assignment.staff.first_name }} {{ assignment.staff.last_name }}</strong>
        in the role assignment <strong>{{ assignment.role }}</strong> under <strong>{{ assignment.supervisor.staff.first_name }} {{ assignment.supervisor.staff.last_name }}</strong>
        with a start date of <strong>{{ assignment.start_date }}</strong> have been updated.
    </p>

    <p><strong>Allocation Details:</strong></p>
    <ul>
        {% for allocation in allocations %}
            {% if allocation.fte %}
                <li><strong{% if allocation_changes and allocation_changes.0.changes.program %} style="background-color:#fff8c6;"{% endif %}>Program:</strong>
                    {% for allocation_change in allocation_changes %}
                        {% if allocation_change.changes.program %}
                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ allocation_change.changes.program.old_value }}</span> 
                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ allocation_change.changes.program.new_value }}</span>
                        {% else %}
                            {% if allocation.program %}{{ allocation.program.program_name }}{% else %}None{% endif %}
                        {% endif %}
                    {% empty %}
                        {% if allocation.program %}{{ allocation.program.program_name }}{% else %}None{% endif %}
                    {% endfor %}
                </li>

                <li><strong{% if allocation_changes and allocation_changes.0.changes.clinic %} style="background-color:#fff8c6;"{% endif %}>Clinic:</strong>
                    {% for allocation_change in allocation_changes %}
                        {% if allocation_change.changes.clinic %}
                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ allocation_change.changes.clinic.old_value }}</span> 
                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ allocation_change.changes.clinic.new_value }}</span>
                        {% else %}
                            {% if allocation.clinic %}{{ allocation.clinic.clinic_name }}{% else %}None{% endif %}
                        {% endif %}
                    {% empty %}
                        {% if allocation.clinic %}{{ allocation.clinic.clinic_name }}{% else %}None{% endif %}
                    {% endfor %}
                </li>

                <li><strong{% if allocation_changes and allocation_changes.0.changes.fte %} style="background-color:#fff8c6;"{% endif %}>FTE:</strong>
                    {% for allocation_change in allocation_changes %}
                        {% if allocation_change.changes.fte %}
                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ allocation_change.changes.fte.old_value }}</span> 
                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ allocation_change.changes.fte.new_value }}</span>
                        {% else %}
                            {{ allocation.fte }}
                        {% endif %}
                    {% empty %}
                        {{ allocation.fte }}
                    {% endfor %}
                </li>

                <li><strong{% if allocation_changes and allocation_changes.0.changes.start_date %} style="background-color:#fff8c6;"{% endif %}>Start Date:</strong>
                    {% for allocation_change in allocation_changes %}
                        {% if allocation_change.changes.start_date %}
                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ allocation_change.changes.start_date.old_value }}</span> 
                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ allocation_change.changes.start_date.new_value }}</span>
                        {% else %}
                            {{ allocation.start_date }}
                        {% endif %}
                    {% empty %}
                        {{ allocation.start_date }}
                    {% endfor %}
                </li>

                <li><strong{% if allocation_changes and allocation_changes.0.changes.end_date %} style="background-color:#fff8c6;"{% endif %}>End Date:</strong>
                    {% for allocation_change in allocation_changes %}
                        {% if allocation_change.changes.end_date %}
                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ allocation_change.changes.end_date.old_value }}</span> 
                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ allocation_change.changes.end_date.new_value }}</span>
                        {% else %}
                            {{ allocation.end_date|default:"None" }}
                        {% endif %}
                    {% empty %}
                        {{ allocation.end_date|default:"None" }}
                    {% endfor %}
                </li>

                <li><strong{% if allocation_changes and allocation_changes.0.changes.currently_active %} style="background-color:#fff8c6;"{% endif %}>Currently Active:</strong>
                    {% for allocation_change in allocation_changes %}
                        {% if allocation_change.changes.currently_active %}
                            <span style="background-color: #ffebee; text-decoration: line-through;">{{ allocation_change.changes.currently_active.old_value }}</span> 
                            → <span style="background-color: #e8f5e8; font-weight: bold;">{{ allocation_change.changes.currently_active.new_value }}</span>
                        {% else %}
                            {{ allocation.currently_active }}
                        {% endif %}
                    {% empty %}
                        {{ allocation.currently_active }}
                    {% endfor %}
                </li>

                <li><strong>Days Assigned:</strong> 
                    {% if allocation.monday %} Mon {% endif %}
                    {% if allocation.tuesday %} Tue {% endif %}
                    {% if allocation.wednesday %} Wed {% endif %}
                    {% if allocation.thursday %} Thur {% endif %}
                    {% if allocation.friday %} Fri {% endif %}
                </li>
            {% endif %}
        {% endfor %}
    </ul>



    {% if comment %}
    <h3>Additional Comments</h3>
    <p><strong>Comment:</strong> <span style="background-color:#DDEBF7">{{ comment }}</span></p>
    {% endif %}

    {% if details_url %}
    <p>
        <a href="{{ details_url }}">View record in operational database</a>
    </p>
    {% endif %}
    <p>
        Submitted by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).
    </p>

    <p>Best regards,</p>
    <p>EOPCN Automation Admin</p>
</body>
</html>
