<!DOCTYPE html>
<html>
<head>
    <title>{% if reminder_type %}{{ reminder_type }} - {% endif %}{{ staff_member.first_name }} {{ staff_member.last_name }}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .leave-details { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .action-button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .urgent { color: #dc3545; font-weight: bold; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="header">
        <h2>
            {% if reminder_type %}
                {{ reminder_type }}: {{ staff_member.first_name }} {{ staff_member.last_name }}
            {% else %}
                Leave Reminder for {{ staff_member.first_name }} {{ staff_member.last_name }}
            {% endif %}
        </h2>
    </div>

    <p>Hello,</p>

    {% if reminder_type == "Leave Starting Soon" %}
        <p>This is a reminder that <strong>{{ staff_member.first_name }} {{ staff_member.last_name }}</strong> has a scheduled leave starting soon.</p>
        <p class="info">Please ensure all necessary arrangements are in place for their absence.</p>
    {% elif reminder_type == "Return Date Verification" %}
        <p>This is a reminder to verify the return date for <strong>{{ staff_member.first_name }} {{ staff_member.last_name }}</strong>.</p>
        <p class="info">Please confirm if the scheduled return date is still accurate.</p>
    {% elif reminder_type == "Leave Status Check" %}
        <p>This is a status check for <strong>{{ staff_member.first_name }} {{ staff_member.last_name }}</strong> who is currently on leave.</p>
        <p class="info">Please verify the leave status and expected return date.</p>
    {% elif reminder_type == "Overdue Return Check" %}
        <p class="urgent">{{ staff_member.first_name }} {{ staff_member.last_name }} was scheduled to return from leave but may not have returned as expected.</p>
        <p class="urgent">Please verify their status and update the leave record accordingly.</p>
    {% else %}
        <p>This is a reminder regarding the leave record for <strong>{{ staff_member.first_name }} {{ staff_member.last_name }}</strong>.</p>
        <p>Please review the leave details and take any necessary action.</p>
    {% endif %}

    <div class="leave-details">
        <h3>Leave Details:</h3>
        <ul>
            {% if staff_leave.leave_type %}
            <li><strong>Leave Type:</strong> {{ staff_leave.leave_type.leave_type_name }}</li>
            {% endif %}
            {% if staff_leave.leave_start_date %}
            <li><strong>Start Date:</strong> {{ staff_leave.leave_start_date|date:"F d, Y" }}</li>
            {% endif %}
            {% if staff_leave.return_date %}
            <li><strong>Scheduled Return Date:</strong> {{ staff_leave.return_date|date:"F d, Y" }}</li>
            {% endif %}
            {% if staff_leave.date_created %}
            <li><strong>Record Created:</strong> {{ staff_leave.date_created|date:"F d, Y" }} by {{ staff_leave.created_by }}</li>
            {% endif %}
        </ul>
    </div>

    {% if details_url %}
    <p>
        <a href="{{ details_url }}" class="action-button">View/Edit Leave Record</a>
    </p>
    {% endif %}

    <p>If you have any questions or need to make changes to this leave record, please access the operational database using the link above or contact the appropriate administrator.</p>

    <hr style="margin: 30px 0;">
    <p style="font-size: 0.9em; color: #666;">
        <strong>Best regards,</strong><br>
        EOPCN Automation Admin<br>
        <em>This is an automated reminder. Please do not reply to this email.</em>
    </p>
</body>
</html>
