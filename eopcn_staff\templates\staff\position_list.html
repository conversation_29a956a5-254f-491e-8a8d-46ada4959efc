{% extends "base.html" %}

{% block content %}

<style>
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.column {
  float: left;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.row::after {
  content: "";
  clear: both;
  display: table;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #f2f2f2;
}

h2 {
  text-align: center;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  cursor: pointer;
  border-radius: 4px;
}

.button1 {
    background-color: #008CBA;
}

.button:hover {
    background-color: #006e92;
}

.link-button {
    display: inline-block;
    padding: 5px 10px;
    font-size: 12px;
    background-color: #73b3f8;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    margin-right: 5px;
}

.link-button:hover {
    background-color: #0056b3;
}

.dataTables_filter, .dataTables_length {
    padding-bottom: 10px;
}

.dataTables_filter {
    float: left !important;
    text-align: left !important;
}

.dataTables_length {
    float: right !important;
    text-align: right !important;
}

.button-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.center-dropdown {
    flex: 1;
    text-align: center;
}

.left-buttons {
    flex: 0 0 auto;
}

@media screen and (max-width: 650px) {
  .column {
    width: 100%;
    display: block;
  }
  .button-container {
    flex-direction: column;
    align-items: flex-start;
  }
}

.green-button {
  background-color: #5dbea3;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;  /* Removes underline */
  display: inline-block;   /* Makes the link look like a button */
}

.green-button:hover {
    background-color: darkgreen;  /* Optional: Darken the button on hover */
}

.greyed-out {
  background-color: #f0f0f0;
  color: #888888;
}

#positionsTable th:nth-child(6), #positionsTable td:nth-child(6) {
  width: 150px; /* Adjust as needed */
  text-align: center; /* Center the text for better alignment */
}

.nowrap {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;  /* Optional: add ellipsis if text overflows */
}


</style>

<h2>Position List</h2>
{% if messages %}
<script>
    {% for message in messages %}
        alert("{{ message }}");
    {% endfor %}
</script>
{% endif %}

<div class="button-container">
    <div class="left-buttons">
        <a href="{% url 'add_position' %}" class="button green-button">+ Add New Position Number</a>
    </div>

    <label>
      <input type="checkbox" id="toggleActive" {% if toggle_active %}checked{% endif %} />
      Show only active positions
  </label>

    <div class="center-dropdown">
        <form method="get" id="staffDetailForm" style="display: inline;">
            <label for="staff_member" class="sr-only" style="font-size: 1.10em;">Jump to a Staff Member's Profile:</label>
            <select name="staff_member" id="staff_member" onchange="redirectToStaffDetail()" style="font-size: 1.05em;">
                <option value="" selected disabled>--Select Staff Member--</option>
                {% for staff in staff_members %}
                    <option value="{{ staff.pk }}">{{ staff.first_name }} {{ staff.last_name }}</option>
                {% endfor %}
            </select>
        </form>
    </div>
</div>

{% if request.GET.allocation_type %}
    <p>Filtering by allocation type: <strong>{{ request.GET.allocation_type|title }}</strong></p>
{% endif %}

{% if request.GET.active_vacancy_type %}
    <p>Filtering by: <strong>{{ request.GET.active_vacancy_type|title }}</strong></p>
{% endif %}


<div class="row">
  <div class="column">
    <table id="pcndatatable" class="display">
      <thead>
        <tr>
          <th>Position Number<span title="The EOPCN position ID approved and budgeted for by ELT"></span></th>
          <th>Staff Name</th>
          <th>Program</th>
          <th>EOPCN Team</th>
          <th>Role Name</th>
          <th class="nowrap">Role FTE</th>
          <th>Allocation Types</th>
          <th>Assignment in Clinic</th>
          <th>Availability</th>
          <th>Vacancy Type</th>
          <th>Active Vacancy Dates</th>
          <th>Active Covering Staff</th>
          <th>Active Covering Dates</th>
          <th>Position Status</th>
          <th>Assignment Status</th>
          <th>Staff Status</th>
          <th>Assignment Start Date</th>
          <th>Assignment End Date</th>
          <th>Actions</th>
        </tr>
      </thead>    
      <tbody>
        {% for position in positions %}
          <tr class="{% if not position.is_most_recent %}greyed-out role-ended{% endif %}">
            <td>{{ position.position_number }}</td>
            <td>
              {% if position.staff_id %}
                <a href="{% url 'staff_detail' position.staff_id %}">{{ position.staff_name }}</a>
              {% else %}
                {{ position.staff_name }}
              {% endif %}
            </td>
            <td>{{ position.service_name|default_if_none:"" }}</td>
            <td>{{ position.program_names|default_if_none:"" }}</td>
            <td>{{ position.role_name|default_if_none:"" }}</td>
            <td>{{ position.role_fte|default_if_none:"" }}</td>
            <td>{{ position.allocation_type|default_if_none:"" }}</td>
            <td>{{ position.assignment_in_clinic_types|default_if_none:"" }}</td>
            <td>{{ position.is_available|yesno:"Available," }}</td>
            <td>{{ position.active_vacancy_type|default_if_none:"" }}</td>
            <td>{{ position.active_vacancy_dates|default_if_none:"" }}</td>
            <td>{{ position.active_covering_staff|default_if_none:"" }}</td>
            <td>{{ position.active_covering_dates|default_if_none:"" }}</td>
            <td>{{ position.position_status }}</td>
            <td class="nowrap">{{ position.assignment_currently_active|yesno:",Inactive Assignment" }}</td>
            <td class="nowrap">{{ position.staff_currently_active|yesno:",Inactive Staff" }}</td>
            <td>{{ position.assignment_start_date|default_if_none:"" }}</td>
            <td>{{ position.assignment_end_date|default_if_none:"" }}</td>
            <td>
              <a href="{% url 'edit_position' position.position_id %}" class="link-button">Edit</a>
            </td>
          </tr>
        {% endfor %}
      </tbody>    
    </table>
  </div>
</div>


{% load static %}
<script src="{% static 'eopcn_staff/js/datatables.js' %}"></script>


{% endblock %}
