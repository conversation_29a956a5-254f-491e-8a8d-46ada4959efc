('C:\\Users\\<USER>\\Py '
 'test\\Python-testing\\EOPCNOpApp\\build\\manage\\PYZ-00.pyz',
 [('EOPCNOpApp',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\EOPCNOpApp\\__init__.py',
   'PYMODULE'),
  ('EOPCNOpApp.settings',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\EOPCNOpApp\\settings.py',
   'PYMODULE'),
  ('EOPCNOpApp.urls',
   'C:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp\\EOPCNOpApp\\urls.py',
   'PYMODULE'),
  ('EOPCNOpApp.wsgi',
   'C:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp\\EOPCNOpApp\\wsgi.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\_markupbase.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\_threading_local.py',
   'PYMODULE'),
  ('aifc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\aifc.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\argparse.py',
   'PYMODULE'),
  ('asgiref',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\asgiref\\__init__.py',
   'PYMODULE'),
  ('asgiref.current_thread_executor',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\asgiref\\current_thread_executor.py',
   'PYMODULE'),
  ('asgiref.local',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\asgiref\\local.py',
   'PYMODULE'),
  ('asgiref.sync',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\asgiref\\sync.py',
   'PYMODULE'),
  ('ast',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('azure', '-', 'PYMODULE'),
  ('azure.core',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\__init__.py',
   'PYMODULE'),
  ('azure.core._azure_clouds',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\_azure_clouds.py',
   'PYMODULE'),
  ('azure.core._enum_meta',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\_enum_meta.py',
   'PYMODULE'),
  ('azure.core._match_conditions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\_match_conditions.py',
   'PYMODULE'),
  ('azure.core._pipeline_client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\_pipeline_client.py',
   'PYMODULE'),
  ('azure.core._pipeline_client_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\_pipeline_client_async.py',
   'PYMODULE'),
  ('azure.core._version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\_version.py',
   'PYMODULE'),
  ('azure.core.configuration',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\configuration.py',
   'PYMODULE'),
  ('azure.core.credentials',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\credentials.py',
   'PYMODULE'),
  ('azure.core.credentials_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\credentials_async.py',
   'PYMODULE'),
  ('azure.core.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\exceptions.py',
   'PYMODULE'),
  ('azure.core.paging',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\paging.py',
   'PYMODULE'),
  ('azure.core.pipeline',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\__init__.py',
   'PYMODULE'),
  ('azure.core.pipeline._base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\_base.py',
   'PYMODULE'),
  ('azure.core.pipeline._base_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\_base_async.py',
   'PYMODULE'),
  ('azure.core.pipeline._tools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\_tools.py',
   'PYMODULE'),
  ('azure.core.pipeline._tools_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\_tools_async.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\__init__.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._authentication',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_authentication.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._authentication_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_authentication_async.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_base.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._base_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_base_async.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._custom_hook',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_custom_hook.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._distributed_tracing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_distributed_tracing.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._redirect',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_redirect.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._redirect_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_redirect_async.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._retry',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_retry.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._retry_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_retry_async.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._sensitive_header_cleanup_policy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_sensitive_header_cleanup_policy.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._universal',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_universal.py',
   'PYMODULE'),
  ('azure.core.pipeline.policies._utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\policies\\_utils.py',
   'PYMODULE'),
  ('azure.core.pipeline.transport',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\transport\\__init__.py',
   'PYMODULE'),
  ('azure.core.pipeline.transport._aiohttp',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\transport\\_aiohttp.py',
   'PYMODULE'),
  ('azure.core.pipeline.transport._base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\transport\\_base.py',
   'PYMODULE'),
  ('azure.core.pipeline.transport._base_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\transport\\_base_async.py',
   'PYMODULE'),
  ('azure.core.pipeline.transport._base_requests_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\transport\\_base_requests_async.py',
   'PYMODULE'),
  ('azure.core.pipeline.transport._bigger_block_size_http_adapters',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\transport\\_bigger_block_size_http_adapters.py',
   'PYMODULE'),
  ('azure.core.pipeline.transport._requests_asyncio',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\transport\\_requests_asyncio.py',
   'PYMODULE'),
  ('azure.core.pipeline.transport._requests_basic',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\transport\\_requests_basic.py',
   'PYMODULE'),
  ('azure.core.pipeline.transport._requests_trio',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\pipeline\\transport\\_requests_trio.py',
   'PYMODULE'),
  ('azure.core.rest',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\rest\\__init__.py',
   'PYMODULE'),
  ('azure.core.rest._aiohttp',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\rest\\_aiohttp.py',
   'PYMODULE'),
  ('azure.core.rest._helpers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\rest\\_helpers.py',
   'PYMODULE'),
  ('azure.core.rest._http_response_impl',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\rest\\_http_response_impl.py',
   'PYMODULE'),
  ('azure.core.rest._http_response_impl_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\rest\\_http_response_impl_async.py',
   'PYMODULE'),
  ('azure.core.rest._requests_asyncio',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\rest\\_requests_asyncio.py',
   'PYMODULE'),
  ('azure.core.rest._requests_basic',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\rest\\_requests_basic.py',
   'PYMODULE'),
  ('azure.core.rest._requests_trio',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\rest\\_requests_trio.py',
   'PYMODULE'),
  ('azure.core.rest._rest_py3',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\rest\\_rest_py3.py',
   'PYMODULE'),
  ('azure.core.serialization',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\serialization.py',
   'PYMODULE'),
  ('azure.core.settings',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\settings.py',
   'PYMODULE'),
  ('azure.core.tracing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\tracing\\__init__.py',
   'PYMODULE'),
  ('azure.core.tracing._abstract_span',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\tracing\\_abstract_span.py',
   'PYMODULE'),
  ('azure.core.tracing.common',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\tracing\\common.py',
   'PYMODULE'),
  ('azure.core.tracing.decorator',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\tracing\\decorator.py',
   'PYMODULE'),
  ('azure.core.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\utils\\__init__.py',
   'PYMODULE'),
  ('azure.core.utils._connection_string_parser',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\utils\\_connection_string_parser.py',
   'PYMODULE'),
  ('azure.core.utils._pipeline_transport_rest_shared',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\utils\\_pipeline_transport_rest_shared.py',
   'PYMODULE'),
  ('azure.core.utils._pipeline_transport_rest_shared_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\utils\\_pipeline_transport_rest_shared_async.py',
   'PYMODULE'),
  ('azure.core.utils._utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\core\\utils\\_utils.py',
   'PYMODULE'),
  ('azure.storage', '-', 'PYMODULE'),
  ('azure.storage.blob',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\__init__.py',
   'PYMODULE'),
  ('azure.storage.blob._blob_client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_blob_client.py',
   'PYMODULE'),
  ('azure.storage.blob._blob_client_helpers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_blob_client_helpers.py',
   'PYMODULE'),
  ('azure.storage.blob._blob_service_client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_blob_service_client.py',
   'PYMODULE'),
  ('azure.storage.blob._blob_service_client_helpers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_blob_service_client_helpers.py',
   'PYMODULE'),
  ('azure.storage.blob._container_client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_container_client.py',
   'PYMODULE'),
  ('azure.storage.blob._container_client_helpers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_container_client_helpers.py',
   'PYMODULE'),
  ('azure.storage.blob._deserialize',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_deserialize.py',
   'PYMODULE'),
  ('azure.storage.blob._download',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_download.py',
   'PYMODULE'),
  ('azure.storage.blob._encryption',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_encryption.py',
   'PYMODULE'),
  ('azure.storage.blob._generated',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\__init__.py',
   'PYMODULE'),
  ('azure.storage.blob._generated._azure_blob_storage',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\_azure_blob_storage.py',
   'PYMODULE'),
  ('azure.storage.blob._generated._configuration',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\_configuration.py',
   'PYMODULE'),
  ('azure.storage.blob._generated._patch',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\_patch.py',
   'PYMODULE'),
  ('azure.storage.blob._generated._serialization',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\_serialization.py',
   'PYMODULE'),
  ('azure.storage.blob._generated.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\models\\__init__.py',
   'PYMODULE'),
  ('azure.storage.blob._generated.models._azure_blob_storage_enums',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\models\\_azure_blob_storage_enums.py',
   'PYMODULE'),
  ('azure.storage.blob._generated.models._models_py3',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\models\\_models_py3.py',
   'PYMODULE'),
  ('azure.storage.blob._generated.models._patch',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\models\\_patch.py',
   'PYMODULE'),
  ('azure.storage.blob._generated.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\operations\\__init__.py',
   'PYMODULE'),
  ('azure.storage.blob._generated.operations._append_blob_operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\operations\\_append_blob_operations.py',
   'PYMODULE'),
  ('azure.storage.blob._generated.operations._blob_operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\operations\\_blob_operations.py',
   'PYMODULE'),
  ('azure.storage.blob._generated.operations._block_blob_operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\operations\\_block_blob_operations.py',
   'PYMODULE'),
  ('azure.storage.blob._generated.operations._container_operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\operations\\_container_operations.py',
   'PYMODULE'),
  ('azure.storage.blob._generated.operations._page_blob_operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\operations\\_page_blob_operations.py',
   'PYMODULE'),
  ('azure.storage.blob._generated.operations._patch',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\operations\\_patch.py',
   'PYMODULE'),
  ('azure.storage.blob._generated.operations._service_operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_generated\\operations\\_service_operations.py',
   'PYMODULE'),
  ('azure.storage.blob._lease',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_lease.py',
   'PYMODULE'),
  ('azure.storage.blob._list_blobs_helper',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_list_blobs_helper.py',
   'PYMODULE'),
  ('azure.storage.blob._models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_models.py',
   'PYMODULE'),
  ('azure.storage.blob._quick_query_helper',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_quick_query_helper.py',
   'PYMODULE'),
  ('azure.storage.blob._serialize',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_serialize.py',
   'PYMODULE'),
  ('azure.storage.blob._shared',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\__init__.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.authentication',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\authentication.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.avro',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\avro\\__init__.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.avro.avro_io',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\avro\\avro_io.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.avro.datafile',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\avro\\datafile.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.avro.schema',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\avro\\schema.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.base_client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\base_client.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.constants',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\constants.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\models.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.parser',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\parser.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.policies',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\policies.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.request_handlers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\request_handlers.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.response_handlers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\response_handlers.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.shared_access_signature',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\shared_access_signature.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.uploads',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\uploads.py',
   'PYMODULE'),
  ('azure.storage.blob._shared.uploads_async',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared\\uploads_async.py',
   'PYMODULE'),
  ('azure.storage.blob._shared_access_signature',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_shared_access_signature.py',
   'PYMODULE'),
  ('azure.storage.blob._upload_helpers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_upload_helpers.py',
   'PYMODULE'),
  ('azure.storage.blob._version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\azure\\storage\\blob\\_version.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\backports\\__init__.py',
   'PYMODULE'),
  ('backports.zoneinfo',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\backports\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('backports.zoneinfo._common',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\backports\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('backports.zoneinfo._tzpath',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\backports\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('backports.zoneinfo._version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\backports\\zoneinfo\\_version.py',
   'PYMODULE'),
  ('backports.zoneinfo._zoneinfo',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\backports\\zoneinfo\\_zoneinfo.py',
   'PYMODULE'),
  ('base64',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\cgi.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('chunk',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\chunk.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\codeop.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\copy.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\dis.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('django',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\__init__.py',
   'PYMODULE'),
  ('django.__main__',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\__main__.py',
   'PYMODULE'),
  ('django.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\apps\\__init__.py',
   'PYMODULE'),
  ('django.apps.config',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\apps\\config.py',
   'PYMODULE'),
  ('django.apps.registry',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\apps\\registry.py',
   'PYMODULE'),
  ('django.conf',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\__init__.py',
   'PYMODULE'),
  ('django.conf.global_settings',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\global_settings.py',
   'PYMODULE'),
  ('django.conf.locale',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ar',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ar\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ar.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ar\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ar_DZ',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ar_DZ\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ar_DZ.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ar_DZ\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.az',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\az\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.az.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\az\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.bg',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\bg\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.bg.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\bg\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.bn',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\bn\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.bn.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\bn\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.bs',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\bs\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.bs.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\bs\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ca',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ca\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ca.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ca\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ckb',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ckb\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ckb.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ckb\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.cs',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\cs\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.cs.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\cs\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.cy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\cy\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.cy.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\cy\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.da',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\da\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.da.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\da\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.de',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\de\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.de.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\de\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.de_CH',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\de_CH\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.de_CH.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\de_CH\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.el',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\el\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.el.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\el\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.en',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\en\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.en.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\en\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.en_AU',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\en_AU\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.en_AU.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\en_AU\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.en_GB',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\en_GB\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.en_GB.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\en_GB\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.eo',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\eo\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.eo.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\eo\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.es',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\es\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.es.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\es\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.es_AR',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\es_AR\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.es_AR.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\es_AR\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.es_CO',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\es_CO\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.es_CO.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\es_CO\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.es_MX',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\es_MX\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.es_MX.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\es_MX\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.es_NI',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\es_NI\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.es_NI.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\es_NI\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.es_PR',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\es_PR\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.es_PR.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\es_PR\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.et',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\et\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.et.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\et\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.eu',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\eu\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.eu.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\eu\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.fa',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\fa\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.fa.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\fa\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.fi',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\fi\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.fi.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\fi\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.fr',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\fr\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.fr.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\fr\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.fy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\fy\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.fy.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\fy\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ga',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ga\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ga.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ga\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.gd',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\gd\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.gd.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\gd\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.gl',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\gl\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.gl.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\gl\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.he',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\he\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.he.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\he\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.hi',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\hi\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.hi.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\hi\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.hr',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\hr\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.hr.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\hr\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.hu',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\hu\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.hu.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\hu\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.id',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\id\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.id.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\id\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ig',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ig\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ig.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ig\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.is',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\is\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.is.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\is\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.it',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\it\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.it.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\it\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ja',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ja\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ja.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ja\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ka',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ka\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ka.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ka\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.km',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\km\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.km.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\km\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.kn',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\kn\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.kn.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\kn\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ko',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ko\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ko.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ko\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ky',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ky\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ky.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ky\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.lt',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\lt\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.lt.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\lt\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.lv',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\lv\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.lv.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\lv\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.mk',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\mk\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.mk.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\mk\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ml',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ml\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ml.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ml\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.mn',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\mn\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.mn.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\mn\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ms\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ms.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ms\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.nb',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\nb\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.nb.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\nb\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.nl',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\nl\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.nl.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\nl\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.nn',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\nn\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.nn.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\nn\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.pl',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\pl\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.pl.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\pl\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.pt',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\pt\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.pt.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\pt\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.pt_BR',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\pt_BR\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.pt_BR.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\pt_BR\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ro',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ro\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ro.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ro\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ru',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ru\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ru.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ru\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.sk',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\sk\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.sk.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\sk\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.sl',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\sl\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.sl.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\sl\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.sq',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\sq\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.sq.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\sq\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.sr',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\sr\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.sr.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\sr\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.sr_Latn',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\sr_Latn\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.sr_Latn.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\sr_Latn\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.sv',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\sv\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.sv.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\sv\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ta',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ta\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ta.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\ta\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.te',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\te\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.te.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\te\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.tg',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\tg\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.tg.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\tg\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.th',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\th\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.th.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\th\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.tk',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\tk\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.tk.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\tk\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.tr',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\tr\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.tr.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\tr\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.uk',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\uk\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.uk.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\uk\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.uz',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\uz\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.uz.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\uz\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.vi',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\vi\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.vi.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\vi\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.zh_Hans',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\zh_Hans\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.zh_Hans.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\zh_Hans\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.zh_Hant',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\zh_Hant\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.zh_Hant.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\locale\\zh_Hant\\formats.py',
   'PYMODULE'),
  ('django.conf.urls',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\urls\\__init__.py',
   'PYMODULE'),
  ('django.conf.urls.i18n',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\urls\\i18n.py',
   'PYMODULE'),
  ('django.conf.urls.static',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\conf\\urls\\static.py',
   'PYMODULE'),
  ('django.contrib',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\__init__.py',
   'PYMODULE'),
  ('django.contrib.admin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\__init__.py',
   'PYMODULE'),
  ('django.contrib.admin.actions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\actions.py',
   'PYMODULE'),
  ('django.contrib.admin.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\apps.py',
   'PYMODULE'),
  ('django.contrib.admin.checks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\checks.py',
   'PYMODULE'),
  ('django.contrib.admin.decorators',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\decorators.py',
   'PYMODULE'),
  ('django.contrib.admin.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\exceptions.py',
   'PYMODULE'),
  ('django.contrib.admin.filters',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\filters.py',
   'PYMODULE'),
  ('django.contrib.admin.forms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\forms.py',
   'PYMODULE'),
  ('django.contrib.admin.helpers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\helpers.py',
   'PYMODULE'),
  ('django.contrib.admin.migrations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.admin.migrations.0001_initial',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.admin.migrations.0002_logentry_remove_auto_add',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\migrations\\0002_logentry_remove_auto_add.py',
   'PYMODULE'),
  ('django.contrib.admin.migrations.0003_logentry_add_action_flag_choices',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\migrations\\0003_logentry_add_action_flag_choices.py',
   'PYMODULE'),
  ('django.contrib.admin.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\models.py',
   'PYMODULE'),
  ('django.contrib.admin.options',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\options.py',
   'PYMODULE'),
  ('django.contrib.admin.sites',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\sites.py',
   'PYMODULE'),
  ('django.contrib.admin.templatetags',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\templatetags\\__init__.py',
   'PYMODULE'),
  ('django.contrib.admin.templatetags.admin_list',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\templatetags\\admin_list.py',
   'PYMODULE'),
  ('django.contrib.admin.templatetags.admin_modify',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\templatetags\\admin_modify.py',
   'PYMODULE'),
  ('django.contrib.admin.templatetags.admin_urls',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\templatetags\\admin_urls.py',
   'PYMODULE'),
  ('django.contrib.admin.templatetags.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\templatetags\\base.py',
   'PYMODULE'),
  ('django.contrib.admin.templatetags.log',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\templatetags\\log.py',
   'PYMODULE'),
  ('django.contrib.admin.tests',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\tests.py',
   'PYMODULE'),
  ('django.contrib.admin.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\utils.py',
   'PYMODULE'),
  ('django.contrib.admin.views',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\views\\__init__.py',
   'PYMODULE'),
  ('django.contrib.admin.views.autocomplete',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\views\\autocomplete.py',
   'PYMODULE'),
  ('django.contrib.admin.views.decorators',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\views\\decorators.py',
   'PYMODULE'),
  ('django.contrib.admin.views.main',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\views\\main.py',
   'PYMODULE'),
  ('django.contrib.admin.widgets',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admin\\widgets.py',
   'PYMODULE'),
  ('django.contrib.admindocs',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admindocs\\__init__.py',
   'PYMODULE'),
  ('django.contrib.admindocs.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admindocs\\apps.py',
   'PYMODULE'),
  ('django.contrib.admindocs.middleware',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admindocs\\middleware.py',
   'PYMODULE'),
  ('django.contrib.admindocs.urls',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admindocs\\urls.py',
   'PYMODULE'),
  ('django.contrib.admindocs.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admindocs\\utils.py',
   'PYMODULE'),
  ('django.contrib.admindocs.views',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\admindocs\\views.py',
   'PYMODULE'),
  ('django.contrib.auth',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\__init__.py',
   'PYMODULE'),
  ('django.contrib.auth.admin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\admin.py',
   'PYMODULE'),
  ('django.contrib.auth.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\apps.py',
   'PYMODULE'),
  ('django.contrib.auth.backends',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\backends.py',
   'PYMODULE'),
  ('django.contrib.auth.base_user',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\base_user.py',
   'PYMODULE'),
  ('django.contrib.auth.checks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\checks.py',
   'PYMODULE'),
  ('django.contrib.auth.context_processors',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\context_processors.py',
   'PYMODULE'),
  ('django.contrib.auth.decorators',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\decorators.py',
   'PYMODULE'),
  ('django.contrib.auth.forms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\forms.py',
   'PYMODULE'),
  ('django.contrib.auth.handlers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\handlers\\__init__.py',
   'PYMODULE'),
  ('django.contrib.auth.handlers.modwsgi',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\handlers\\modwsgi.py',
   'PYMODULE'),
  ('django.contrib.auth.hashers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\hashers.py',
   'PYMODULE'),
  ('django.contrib.auth.management',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\management\\__init__.py',
   'PYMODULE'),
  ('django.contrib.auth.management.commands',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.contrib.auth.management.commands.changepassword',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\management\\commands\\changepassword.py',
   'PYMODULE'),
  ('django.contrib.auth.management.commands.createsuperuser',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\management\\commands\\createsuperuser.py',
   'PYMODULE'),
  ('django.contrib.auth.middleware',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\middleware.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0001_initial',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0002_alter_permission_name_max_length',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\0002_alter_permission_name_max_length.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0003_alter_user_email_max_length',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\0003_alter_user_email_max_length.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0004_alter_user_username_opts',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\0004_alter_user_username_opts.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0005_alter_user_last_login_null',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\0005_alter_user_last_login_null.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0006_require_contenttypes_0002',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\0006_require_contenttypes_0002.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0007_alter_validators_add_error_messages',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\0007_alter_validators_add_error_messages.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0008_alter_user_username_max_length',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\0008_alter_user_username_max_length.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0009_alter_user_last_name_max_length',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\0009_alter_user_last_name_max_length.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0010_alter_group_name_max_length',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\0010_alter_group_name_max_length.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0011_update_proxy_permissions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\0011_update_proxy_permissions.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0012_alter_user_first_name_max_length',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\migrations\\0012_alter_user_first_name_max_length.py',
   'PYMODULE'),
  ('django.contrib.auth.mixins',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\mixins.py',
   'PYMODULE'),
  ('django.contrib.auth.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\models.py',
   'PYMODULE'),
  ('django.contrib.auth.password_validation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\password_validation.py',
   'PYMODULE'),
  ('django.contrib.auth.signals',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\signals.py',
   'PYMODULE'),
  ('django.contrib.auth.tokens',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\tokens.py',
   'PYMODULE'),
  ('django.contrib.auth.urls',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\urls.py',
   'PYMODULE'),
  ('django.contrib.auth.validators',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\validators.py',
   'PYMODULE'),
  ('django.contrib.auth.views',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\auth\\views.py',
   'PYMODULE'),
  ('django.contrib.contenttypes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\__init__.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.admin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\admin.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\apps.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.checks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\checks.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.fields',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\fields.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.forms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\forms.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.management',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\management\\__init__.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.management.commands',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.management.commands.remove_stale_contenttypes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\management\\commands\\remove_stale_contenttypes.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.migrations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.migrations.0001_initial',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.migrations.0002_remove_content_type_name',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\migrations\\0002_remove_content_type_name.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\models.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.views',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\contenttypes\\views.py',
   'PYMODULE'),
  ('django.contrib.flatpages',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\__init__.py',
   'PYMODULE'),
  ('django.contrib.flatpages.admin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\admin.py',
   'PYMODULE'),
  ('django.contrib.flatpages.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\apps.py',
   'PYMODULE'),
  ('django.contrib.flatpages.forms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\forms.py',
   'PYMODULE'),
  ('django.contrib.flatpages.middleware',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\middleware.py',
   'PYMODULE'),
  ('django.contrib.flatpages.migrations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.flatpages.migrations.0001_initial',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.flatpages.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\models.py',
   'PYMODULE'),
  ('django.contrib.flatpages.sitemaps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\sitemaps.py',
   'PYMODULE'),
  ('django.contrib.flatpages.templatetags',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\templatetags\\__init__.py',
   'PYMODULE'),
  ('django.contrib.flatpages.templatetags.flatpages',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\templatetags\\flatpages.py',
   'PYMODULE'),
  ('django.contrib.flatpages.urls',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\urls.py',
   'PYMODULE'),
  ('django.contrib.flatpages.views',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\flatpages\\views.py',
   'PYMODULE'),
  ('django.contrib.gis',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\apps.py',
   'PYMODULE'),
  ('django.contrib.gis.db',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\base\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.base.adapter',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\base\\adapter.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.base.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\base\\features.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.base.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\base\\models.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.base.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\base\\operations.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.mysql',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\mysql\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.mysql.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\mysql\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.mysql.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\mysql\\features.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.mysql.introspection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\mysql\\introspection.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.mysql.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\mysql\\operations.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.mysql.schema',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\mysql\\schema.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.adapter',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\adapter.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\features.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.introspection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\introspection.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\models.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\operations.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.schema',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\schema.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.adapter',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\adapter.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.const',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\const.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\features.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.introspection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\introspection.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\models.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\operations.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.pgraster',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\pgraster.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.schema',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\schema.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.adapter',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\adapter.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\client.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\features.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.introspection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\introspection.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\models.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\operations.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.schema',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\schema.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\backends\\utils.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\models\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.aggregates',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\models\\aggregates.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.fields',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\models\\fields.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\models\\functions.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.lookups',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\models\\lookups.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.proxy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\models\\proxy.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.sql',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\models\\sql\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.sql.conversion',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\db\\models\\sql\\conversion.py',
   'PYMODULE'),
  ('django.contrib.gis.feeds',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\feeds.py',
   'PYMODULE'),
  ('django.contrib.gis.forms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\forms\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.forms.fields',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\forms\\fields.py',
   'PYMODULE'),
  ('django.contrib.gis.forms.widgets',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\forms\\widgets.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.datasource',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\datasource.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.driver',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\driver.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.envelope',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\envelope.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.error',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\error.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.feature',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\feature.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.field',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\field.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.geometries',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\geometries.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.geomtype',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\geomtype.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.layer',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\layer.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.libgdal',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\libgdal.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes.ds',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\ds.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes.errcheck',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\errcheck.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes.generation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\generation.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes.geom',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\geom.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes.raster',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\raster.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes.srs',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\srs.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.raster',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\raster\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.raster.band',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\raster\\band.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.raster.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\raster\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.raster.const',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\raster\\const.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.raster.source',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\raster\\source.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.srs',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\gdal\\srs.py',
   'PYMODULE'),
  ('django.contrib.gis.geoip2',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geoip2\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.geoip2.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geoip2\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.geoip2.resources',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geoip2\\resources.py',
   'PYMODULE'),
  ('django.contrib.gis.geometry',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geometry.py',
   'PYMODULE'),
  ('django.contrib.gis.geos',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.collections',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\collections.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.coordseq',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\coordseq.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.error',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\error.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.factory',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\factory.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.geometry',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\geometry.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.io',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\io.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.libgeos',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\libgeos.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.linestring',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\linestring.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.mutable_list',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\mutable_list.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.point',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\point.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.polygon',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\polygon.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prepared',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\prepared.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.coordseq',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\coordseq.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.errcheck',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\errcheck.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.geom',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\geom.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.io',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\io.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.misc',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\misc.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.predicates',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\predicates.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.prepared',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\prepared.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.threadsafe',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\threadsafe.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.topology',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\topology.py',
   'PYMODULE'),
  ('django.contrib.gis.management',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\management\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.management.commands',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.management.commands.inspectdb',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\management\\commands\\inspectdb.py',
   'PYMODULE'),
  ('django.contrib.gis.management.commands.ogrinspect',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\management\\commands\\ogrinspect.py',
   'PYMODULE'),
  ('django.contrib.gis.measure',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\measure.py',
   'PYMODULE'),
  ('django.contrib.gis.ptr',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\ptr.py',
   'PYMODULE'),
  ('django.contrib.gis.serializers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\serializers\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.serializers.geojson',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\serializers\\geojson.py',
   'PYMODULE'),
  ('django.contrib.gis.shortcuts',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\shortcuts.py',
   'PYMODULE'),
  ('django.contrib.gis.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\utils\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.utils.layermapping',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\utils\\layermapping.py',
   'PYMODULE'),
  ('django.contrib.gis.utils.ogrinfo',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\utils\\ogrinfo.py',
   'PYMODULE'),
  ('django.contrib.gis.utils.ogrinspect',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\utils\\ogrinspect.py',
   'PYMODULE'),
  ('django.contrib.gis.utils.srs',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\utils\\srs.py',
   'PYMODULE'),
  ('django.contrib.gis.views',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\gis\\views.py',
   'PYMODULE'),
  ('django.contrib.humanize',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\humanize\\__init__.py',
   'PYMODULE'),
  ('django.contrib.humanize.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\humanize\\apps.py',
   'PYMODULE'),
  ('django.contrib.humanize.templatetags',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\humanize\\templatetags\\__init__.py',
   'PYMODULE'),
  ('django.contrib.humanize.templatetags.humanize',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\humanize\\templatetags\\humanize.py',
   'PYMODULE'),
  ('django.contrib.messages',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\__init__.py',
   'PYMODULE'),
  ('django.contrib.messages.api',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\api.py',
   'PYMODULE'),
  ('django.contrib.messages.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\apps.py',
   'PYMODULE'),
  ('django.contrib.messages.constants',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\constants.py',
   'PYMODULE'),
  ('django.contrib.messages.context_processors',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\context_processors.py',
   'PYMODULE'),
  ('django.contrib.messages.middleware',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\middleware.py',
   'PYMODULE'),
  ('django.contrib.messages.storage',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\storage\\__init__.py',
   'PYMODULE'),
  ('django.contrib.messages.storage.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\storage\\base.py',
   'PYMODULE'),
  ('django.contrib.messages.storage.cookie',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\storage\\cookie.py',
   'PYMODULE'),
  ('django.contrib.messages.storage.fallback',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\storage\\fallback.py',
   'PYMODULE'),
  ('django.contrib.messages.storage.session',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\storage\\session.py',
   'PYMODULE'),
  ('django.contrib.messages.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\utils.py',
   'PYMODULE'),
  ('django.contrib.messages.views',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\messages\\views.py',
   'PYMODULE'),
  ('django.contrib.postgres',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\__init__.py',
   'PYMODULE'),
  ('django.contrib.postgres.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\apps.py',
   'PYMODULE'),
  ('django.contrib.postgres.constraints',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\constraints.py',
   'PYMODULE'),
  ('django.contrib.postgres.expressions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\expressions.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\fields\\__init__.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields.array',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\fields\\array.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields.citext',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\fields\\citext.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields.hstore',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\fields\\hstore.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields.jsonb',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\fields\\jsonb.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields.ranges',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\fields\\ranges.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\fields\\utils.py',
   'PYMODULE'),
  ('django.contrib.postgres.forms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\forms\\__init__.py',
   'PYMODULE'),
  ('django.contrib.postgres.forms.array',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\forms\\array.py',
   'PYMODULE'),
  ('django.contrib.postgres.forms.hstore',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\forms\\hstore.py',
   'PYMODULE'),
  ('django.contrib.postgres.forms.ranges',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\forms\\ranges.py',
   'PYMODULE'),
  ('django.contrib.postgres.functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\functions.py',
   'PYMODULE'),
  ('django.contrib.postgres.indexes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\indexes.py',
   'PYMODULE'),
  ('django.contrib.postgres.lookups',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\lookups.py',
   'PYMODULE'),
  ('django.contrib.postgres.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\operations.py',
   'PYMODULE'),
  ('django.contrib.postgres.search',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\search.py',
   'PYMODULE'),
  ('django.contrib.postgres.serializers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\serializers.py',
   'PYMODULE'),
  ('django.contrib.postgres.signals',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\signals.py',
   'PYMODULE'),
  ('django.contrib.postgres.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\utils.py',
   'PYMODULE'),
  ('django.contrib.postgres.validators',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\postgres\\validators.py',
   'PYMODULE'),
  ('django.contrib.redirects',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\redirects\\__init__.py',
   'PYMODULE'),
  ('django.contrib.redirects.admin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\redirects\\admin.py',
   'PYMODULE'),
  ('django.contrib.redirects.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\redirects\\apps.py',
   'PYMODULE'),
  ('django.contrib.redirects.middleware',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\redirects\\middleware.py',
   'PYMODULE'),
  ('django.contrib.redirects.migrations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\redirects\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.redirects.migrations.0001_initial',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\redirects\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.redirects.migrations.0002_alter_redirect_new_path_help_text',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\redirects\\migrations\\0002_alter_redirect_new_path_help_text.py',
   'PYMODULE'),
  ('django.contrib.redirects.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\redirects\\models.py',
   'PYMODULE'),
  ('django.contrib.sessions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sessions.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\apps.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\backends\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\backends\\base.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends.cache',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\backends\\cache.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends.cached_db',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\backends\\cached_db.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends.db',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\backends\\db.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends.file',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\backends\\file.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends.signed_cookies',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\backends\\signed_cookies.py',
   'PYMODULE'),
  ('django.contrib.sessions.base_session',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\base_session.py',
   'PYMODULE'),
  ('django.contrib.sessions.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\exceptions.py',
   'PYMODULE'),
  ('django.contrib.sessions.management',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\management\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sessions.management.commands',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sessions.management.commands.clearsessions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\management\\commands\\clearsessions.py',
   'PYMODULE'),
  ('django.contrib.sessions.middleware',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\middleware.py',
   'PYMODULE'),
  ('django.contrib.sessions.migrations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sessions.migrations.0001_initial',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.sessions.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\models.py',
   'PYMODULE'),
  ('django.contrib.sessions.serializers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sessions\\serializers.py',
   'PYMODULE'),
  ('django.contrib.sitemaps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sitemaps\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sitemaps.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sitemaps\\apps.py',
   'PYMODULE'),
  ('django.contrib.sitemaps.management',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sitemaps\\management\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sitemaps.management.commands',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sitemaps\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sitemaps.management.commands.ping_google',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sitemaps\\management\\commands\\ping_google.py',
   'PYMODULE'),
  ('django.contrib.sitemaps.views',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sitemaps\\views.py',
   'PYMODULE'),
  ('django.contrib.sites',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sites.admin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\admin.py',
   'PYMODULE'),
  ('django.contrib.sites.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\apps.py',
   'PYMODULE'),
  ('django.contrib.sites.checks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\checks.py',
   'PYMODULE'),
  ('django.contrib.sites.management',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\management.py',
   'PYMODULE'),
  ('django.contrib.sites.managers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\managers.py',
   'PYMODULE'),
  ('django.contrib.sites.middleware',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\middleware.py',
   'PYMODULE'),
  ('django.contrib.sites.migrations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sites.migrations.0001_initial',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.sites.migrations.0002_alter_domain_unique',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\migrations\\0002_alter_domain_unique.py',
   'PYMODULE'),
  ('django.contrib.sites.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\models.py',
   'PYMODULE'),
  ('django.contrib.sites.requests',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\requests.py',
   'PYMODULE'),
  ('django.contrib.sites.shortcuts',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\sites\\shortcuts.py',
   'PYMODULE'),
  ('django.contrib.staticfiles',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\__init__.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\apps.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.checks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\checks.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.finders',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\finders.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.handlers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\handlers.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.management',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\management\\__init__.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.management.commands',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.management.commands.collectstatic',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\management\\commands\\collectstatic.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.management.commands.findstatic',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\management\\commands\\findstatic.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.management.commands.runserver',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\management\\commands\\runserver.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.storage',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\storage.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.testing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\testing.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.urls',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\urls.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\utils.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.views',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\staticfiles\\views.py',
   'PYMODULE'),
  ('django.contrib.syndication',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\syndication\\__init__.py',
   'PYMODULE'),
  ('django.contrib.syndication.apps',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\syndication\\apps.py',
   'PYMODULE'),
  ('django.contrib.syndication.views',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\contrib\\syndication\\views.py',
   'PYMODULE'),
  ('django.core',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\__init__.py',
   'PYMODULE'),
  ('django.core.asgi',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\asgi.py',
   'PYMODULE'),
  ('django.core.cache',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\cache\\__init__.py',
   'PYMODULE'),
  ('django.core.cache.backends',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\cache\\backends\\__init__.py',
   'PYMODULE'),
  ('django.core.cache.backends.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\cache\\backends\\base.py',
   'PYMODULE'),
  ('django.core.cache.backends.db',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\cache\\backends\\db.py',
   'PYMODULE'),
  ('django.core.cache.backends.dummy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\cache\\backends\\dummy.py',
   'PYMODULE'),
  ('django.core.cache.backends.filebased',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\cache\\backends\\filebased.py',
   'PYMODULE'),
  ('django.core.cache.backends.locmem',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\cache\\backends\\locmem.py',
   'PYMODULE'),
  ('django.core.cache.backends.memcached',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\cache\\backends\\memcached.py',
   'PYMODULE'),
  ('django.core.cache.backends.redis',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\cache\\backends\\redis.py',
   'PYMODULE'),
  ('django.core.cache.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\cache\\utils.py',
   'PYMODULE'),
  ('django.core.checks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\__init__.py',
   'PYMODULE'),
  ('django.core.checks.async_checks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\async_checks.py',
   'PYMODULE'),
  ('django.core.checks.caches',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\caches.py',
   'PYMODULE'),
  ('django.core.checks.compatibility',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\compatibility\\__init__.py',
   'PYMODULE'),
  ('django.core.checks.compatibility.django_4_0',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\compatibility\\django_4_0.py',
   'PYMODULE'),
  ('django.core.checks.database',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\database.py',
   'PYMODULE'),
  ('django.core.checks.files',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\files.py',
   'PYMODULE'),
  ('django.core.checks.messages',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\messages.py',
   'PYMODULE'),
  ('django.core.checks.model_checks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\model_checks.py',
   'PYMODULE'),
  ('django.core.checks.registry',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\registry.py',
   'PYMODULE'),
  ('django.core.checks.security',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\security\\__init__.py',
   'PYMODULE'),
  ('django.core.checks.security.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\security\\base.py',
   'PYMODULE'),
  ('django.core.checks.security.csrf',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\security\\csrf.py',
   'PYMODULE'),
  ('django.core.checks.security.sessions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\security\\sessions.py',
   'PYMODULE'),
  ('django.core.checks.templates',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\templates.py',
   'PYMODULE'),
  ('django.core.checks.translation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\translation.py',
   'PYMODULE'),
  ('django.core.checks.urls',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\checks\\urls.py',
   'PYMODULE'),
  ('django.core.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\exceptions.py',
   'PYMODULE'),
  ('django.core.files',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\__init__.py',
   'PYMODULE'),
  ('django.core.files.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\base.py',
   'PYMODULE'),
  ('django.core.files.images',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\images.py',
   'PYMODULE'),
  ('django.core.files.locks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\locks.py',
   'PYMODULE'),
  ('django.core.files.move',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\move.py',
   'PYMODULE'),
  ('django.core.files.storage',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\storage\\__init__.py',
   'PYMODULE'),
  ('django.core.files.storage.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\storage\\base.py',
   'PYMODULE'),
  ('django.core.files.storage.filesystem',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\storage\\filesystem.py',
   'PYMODULE'),
  ('django.core.files.storage.handler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\storage\\handler.py',
   'PYMODULE'),
  ('django.core.files.storage.memory',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\storage\\memory.py',
   'PYMODULE'),
  ('django.core.files.storage.mixins',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\storage\\mixins.py',
   'PYMODULE'),
  ('django.core.files.temp',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\temp.py',
   'PYMODULE'),
  ('django.core.files.uploadedfile',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\uploadedfile.py',
   'PYMODULE'),
  ('django.core.files.uploadhandler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\uploadhandler.py',
   'PYMODULE'),
  ('django.core.files.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\files\\utils.py',
   'PYMODULE'),
  ('django.core.handlers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\handlers\\__init__.py',
   'PYMODULE'),
  ('django.core.handlers.asgi',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\handlers\\asgi.py',
   'PYMODULE'),
  ('django.core.handlers.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\handlers\\base.py',
   'PYMODULE'),
  ('django.core.handlers.exception',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\handlers\\exception.py',
   'PYMODULE'),
  ('django.core.handlers.wsgi',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\handlers\\wsgi.py',
   'PYMODULE'),
  ('django.core.mail',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\mail\\__init__.py',
   'PYMODULE'),
  ('django.core.mail.backends',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\mail\\backends\\__init__.py',
   'PYMODULE'),
  ('django.core.mail.backends.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\mail\\backends\\base.py',
   'PYMODULE'),
  ('django.core.mail.backends.console',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\mail\\backends\\console.py',
   'PYMODULE'),
  ('django.core.mail.backends.dummy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\mail\\backends\\dummy.py',
   'PYMODULE'),
  ('django.core.mail.backends.filebased',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\mail\\backends\\filebased.py',
   'PYMODULE'),
  ('django.core.mail.backends.locmem',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\mail\\backends\\locmem.py',
   'PYMODULE'),
  ('django.core.mail.backends.smtp',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\mail\\backends\\smtp.py',
   'PYMODULE'),
  ('django.core.mail.message',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\mail\\message.py',
   'PYMODULE'),
  ('django.core.mail.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\mail\\utils.py',
   'PYMODULE'),
  ('django.core.management',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\__init__.py',
   'PYMODULE'),
  ('django.core.management.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\base.py',
   'PYMODULE'),
  ('django.core.management.color',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\color.py',
   'PYMODULE'),
  ('django.core.management.commands',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.core.management.commands.check',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\check.py',
   'PYMODULE'),
  ('django.core.management.commands.compilemessages',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\compilemessages.py',
   'PYMODULE'),
  ('django.core.management.commands.createcachetable',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\createcachetable.py',
   'PYMODULE'),
  ('django.core.management.commands.dbshell',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\dbshell.py',
   'PYMODULE'),
  ('django.core.management.commands.diffsettings',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\diffsettings.py',
   'PYMODULE'),
  ('django.core.management.commands.dumpdata',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\dumpdata.py',
   'PYMODULE'),
  ('django.core.management.commands.flush',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\flush.py',
   'PYMODULE'),
  ('django.core.management.commands.inspectdb',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\inspectdb.py',
   'PYMODULE'),
  ('django.core.management.commands.loaddata',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\loaddata.py',
   'PYMODULE'),
  ('django.core.management.commands.makemessages',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\makemessages.py',
   'PYMODULE'),
  ('django.core.management.commands.makemigrations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\makemigrations.py',
   'PYMODULE'),
  ('django.core.management.commands.migrate',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\migrate.py',
   'PYMODULE'),
  ('django.core.management.commands.optimizemigration',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\optimizemigration.py',
   'PYMODULE'),
  ('django.core.management.commands.runserver',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\runserver.py',
   'PYMODULE'),
  ('django.core.management.commands.sendtestemail',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\sendtestemail.py',
   'PYMODULE'),
  ('django.core.management.commands.shell',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\shell.py',
   'PYMODULE'),
  ('django.core.management.commands.showmigrations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\showmigrations.py',
   'PYMODULE'),
  ('django.core.management.commands.sqlflush',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\sqlflush.py',
   'PYMODULE'),
  ('django.core.management.commands.sqlmigrate',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\sqlmigrate.py',
   'PYMODULE'),
  ('django.core.management.commands.sqlsequencereset',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\sqlsequencereset.py',
   'PYMODULE'),
  ('django.core.management.commands.squashmigrations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\squashmigrations.py',
   'PYMODULE'),
  ('django.core.management.commands.startapp',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\startapp.py',
   'PYMODULE'),
  ('django.core.management.commands.startproject',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\startproject.py',
   'PYMODULE'),
  ('django.core.management.commands.test',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\test.py',
   'PYMODULE'),
  ('django.core.management.commands.testserver',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\commands\\testserver.py',
   'PYMODULE'),
  ('django.core.management.sql',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\sql.py',
   'PYMODULE'),
  ('django.core.management.templates',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\templates.py',
   'PYMODULE'),
  ('django.core.management.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\management\\utils.py',
   'PYMODULE'),
  ('django.core.paginator',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\paginator.py',
   'PYMODULE'),
  ('django.core.serializers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\serializers\\__init__.py',
   'PYMODULE'),
  ('django.core.serializers.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\serializers\\base.py',
   'PYMODULE'),
  ('django.core.serializers.json',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\serializers\\json.py',
   'PYMODULE'),
  ('django.core.serializers.jsonl',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\serializers\\jsonl.py',
   'PYMODULE'),
  ('django.core.serializers.python',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\serializers\\python.py',
   'PYMODULE'),
  ('django.core.serializers.pyyaml',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\serializers\\pyyaml.py',
   'PYMODULE'),
  ('django.core.serializers.xml_serializer',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\serializers\\xml_serializer.py',
   'PYMODULE'),
  ('django.core.servers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\servers\\__init__.py',
   'PYMODULE'),
  ('django.core.servers.basehttp',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\servers\\basehttp.py',
   'PYMODULE'),
  ('django.core.signals',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\signals.py',
   'PYMODULE'),
  ('django.core.signing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\signing.py',
   'PYMODULE'),
  ('django.core.validators',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\validators.py',
   'PYMODULE'),
  ('django.core.wsgi',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\core\\wsgi.py',
   'PYMODULE'),
  ('django.db',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\__init__.py',
   'PYMODULE'),
  ('django.db.backends',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\base\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.base.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\base\\base.py',
   'PYMODULE'),
  ('django.db.backends.base.client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\base\\client.py',
   'PYMODULE'),
  ('django.db.backends.base.creation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\base\\creation.py',
   'PYMODULE'),
  ('django.db.backends.base.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\base\\features.py',
   'PYMODULE'),
  ('django.db.backends.base.introspection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\base\\introspection.py',
   'PYMODULE'),
  ('django.db.backends.base.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\base\\operations.py',
   'PYMODULE'),
  ('django.db.backends.base.schema',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\base\\schema.py',
   'PYMODULE'),
  ('django.db.backends.base.validation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\base\\validation.py',
   'PYMODULE'),
  ('django.db.backends.ddl_references',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\ddl_references.py',
   'PYMODULE'),
  ('django.db.backends.dummy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\dummy\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.dummy.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\dummy\\base.py',
   'PYMODULE'),
  ('django.db.backends.dummy.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\dummy\\features.py',
   'PYMODULE'),
  ('django.db.backends.mysql',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\mysql\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.mysql.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\mysql\\base.py',
   'PYMODULE'),
  ('django.db.backends.mysql.client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\mysql\\client.py',
   'PYMODULE'),
  ('django.db.backends.mysql.compiler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\mysql\\compiler.py',
   'PYMODULE'),
  ('django.db.backends.mysql.creation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\mysql\\creation.py',
   'PYMODULE'),
  ('django.db.backends.mysql.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\mysql\\features.py',
   'PYMODULE'),
  ('django.db.backends.mysql.introspection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\mysql\\introspection.py',
   'PYMODULE'),
  ('django.db.backends.mysql.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\mysql\\operations.py',
   'PYMODULE'),
  ('django.db.backends.mysql.schema',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\mysql\\schema.py',
   'PYMODULE'),
  ('django.db.backends.mysql.validation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\mysql\\validation.py',
   'PYMODULE'),
  ('django.db.backends.oracle',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\oracle\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.oracle.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\oracle\\base.py',
   'PYMODULE'),
  ('django.db.backends.oracle.client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\oracle\\client.py',
   'PYMODULE'),
  ('django.db.backends.oracle.creation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\oracle\\creation.py',
   'PYMODULE'),
  ('django.db.backends.oracle.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\oracle\\features.py',
   'PYMODULE'),
  ('django.db.backends.oracle.functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\oracle\\functions.py',
   'PYMODULE'),
  ('django.db.backends.oracle.introspection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\oracle\\introspection.py',
   'PYMODULE'),
  ('django.db.backends.oracle.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\oracle\\operations.py',
   'PYMODULE'),
  ('django.db.backends.oracle.schema',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\oracle\\schema.py',
   'PYMODULE'),
  ('django.db.backends.oracle.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\oracle\\utils.py',
   'PYMODULE'),
  ('django.db.backends.oracle.validation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\oracle\\validation.py',
   'PYMODULE'),
  ('django.db.backends.postgresql',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\postgresql\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\postgresql\\base.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\postgresql\\client.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.creation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\postgresql\\creation.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\postgresql\\features.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.introspection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\postgresql\\introspection.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\postgresql\\operations.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.psycopg_any',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\postgresql\\psycopg_any.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.schema',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\postgresql\\schema.py',
   'PYMODULE'),
  ('django.db.backends.signals',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\signals.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3._functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\sqlite3\\_functions.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\sqlite3\\base.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\sqlite3\\client.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.creation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\sqlite3\\creation.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\sqlite3\\features.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.introspection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\sqlite3\\introspection.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\sqlite3\\operations.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.schema',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\sqlite3\\schema.py',
   'PYMODULE'),
  ('django.db.backends.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\backends\\utils.py',
   'PYMODULE'),
  ('django.db.migrations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.db.migrations.autodetector',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\autodetector.py',
   'PYMODULE'),
  ('django.db.migrations.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\exceptions.py',
   'PYMODULE'),
  ('django.db.migrations.executor',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\executor.py',
   'PYMODULE'),
  ('django.db.migrations.graph',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\graph.py',
   'PYMODULE'),
  ('django.db.migrations.loader',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\loader.py',
   'PYMODULE'),
  ('django.db.migrations.migration',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\migration.py',
   'PYMODULE'),
  ('django.db.migrations.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\operations\\__init__.py',
   'PYMODULE'),
  ('django.db.migrations.operations.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\operations\\base.py',
   'PYMODULE'),
  ('django.db.migrations.operations.fields',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\operations\\fields.py',
   'PYMODULE'),
  ('django.db.migrations.operations.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\operations\\models.py',
   'PYMODULE'),
  ('django.db.migrations.operations.special',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\operations\\special.py',
   'PYMODULE'),
  ('django.db.migrations.optimizer',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\optimizer.py',
   'PYMODULE'),
  ('django.db.migrations.questioner',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\questioner.py',
   'PYMODULE'),
  ('django.db.migrations.recorder',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\recorder.py',
   'PYMODULE'),
  ('django.db.migrations.serializer',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\serializer.py',
   'PYMODULE'),
  ('django.db.migrations.state',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\state.py',
   'PYMODULE'),
  ('django.db.migrations.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\utils.py',
   'PYMODULE'),
  ('django.db.migrations.writer',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\migrations\\writer.py',
   'PYMODULE'),
  ('django.db.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\__init__.py',
   'PYMODULE'),
  ('django.db.models.aggregates',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\aggregates.py',
   'PYMODULE'),
  ('django.db.models.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\base.py',
   'PYMODULE'),
  ('django.db.models.constants',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\constants.py',
   'PYMODULE'),
  ('django.db.models.constraints',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\constraints.py',
   'PYMODULE'),
  ('django.db.models.deletion',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\deletion.py',
   'PYMODULE'),
  ('django.db.models.enums',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\enums.py',
   'PYMODULE'),
  ('django.db.models.expressions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\expressions.py',
   'PYMODULE'),
  ('django.db.models.fields',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\fields\\__init__.py',
   'PYMODULE'),
  ('django.db.models.fields.files',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\fields\\files.py',
   'PYMODULE'),
  ('django.db.models.fields.json',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\fields\\json.py',
   'PYMODULE'),
  ('django.db.models.fields.mixins',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\fields\\mixins.py',
   'PYMODULE'),
  ('django.db.models.fields.proxy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\fields\\proxy.py',
   'PYMODULE'),
  ('django.db.models.fields.related',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\fields\\related.py',
   'PYMODULE'),
  ('django.db.models.fields.related_descriptors',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\fields\\related_descriptors.py',
   'PYMODULE'),
  ('django.db.models.fields.related_lookups',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\fields\\related_lookups.py',
   'PYMODULE'),
  ('django.db.models.fields.reverse_related',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\fields\\reverse_related.py',
   'PYMODULE'),
  ('django.db.models.functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\functions\\__init__.py',
   'PYMODULE'),
  ('django.db.models.functions.comparison',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\functions\\comparison.py',
   'PYMODULE'),
  ('django.db.models.functions.datetime',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\functions\\datetime.py',
   'PYMODULE'),
  ('django.db.models.functions.math',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\functions\\math.py',
   'PYMODULE'),
  ('django.db.models.functions.mixins',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\functions\\mixins.py',
   'PYMODULE'),
  ('django.db.models.functions.text',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\functions\\text.py',
   'PYMODULE'),
  ('django.db.models.functions.window',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\functions\\window.py',
   'PYMODULE'),
  ('django.db.models.indexes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\indexes.py',
   'PYMODULE'),
  ('django.db.models.lookups',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\lookups.py',
   'PYMODULE'),
  ('django.db.models.manager',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\manager.py',
   'PYMODULE'),
  ('django.db.models.options',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\options.py',
   'PYMODULE'),
  ('django.db.models.query',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\query.py',
   'PYMODULE'),
  ('django.db.models.query_utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\query_utils.py',
   'PYMODULE'),
  ('django.db.models.signals',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\signals.py',
   'PYMODULE'),
  ('django.db.models.sql',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\sql\\__init__.py',
   'PYMODULE'),
  ('django.db.models.sql.compiler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\sql\\compiler.py',
   'PYMODULE'),
  ('django.db.models.sql.constants',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\sql\\constants.py',
   'PYMODULE'),
  ('django.db.models.sql.datastructures',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\sql\\datastructures.py',
   'PYMODULE'),
  ('django.db.models.sql.query',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\sql\\query.py',
   'PYMODULE'),
  ('django.db.models.sql.subqueries',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\sql\\subqueries.py',
   'PYMODULE'),
  ('django.db.models.sql.where',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\sql\\where.py',
   'PYMODULE'),
  ('django.db.models.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\models\\utils.py',
   'PYMODULE'),
  ('django.db.transaction',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\transaction.py',
   'PYMODULE'),
  ('django.db.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\db\\utils.py',
   'PYMODULE'),
  ('django.dispatch',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\dispatch\\__init__.py',
   'PYMODULE'),
  ('django.dispatch.dispatcher',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\dispatch\\dispatcher.py',
   'PYMODULE'),
  ('django.forms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\forms\\__init__.py',
   'PYMODULE'),
  ('django.forms.boundfield',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\forms\\boundfield.py',
   'PYMODULE'),
  ('django.forms.fields',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\forms\\fields.py',
   'PYMODULE'),
  ('django.forms.forms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\forms\\forms.py',
   'PYMODULE'),
  ('django.forms.formsets',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\forms\\formsets.py',
   'PYMODULE'),
  ('django.forms.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\forms\\models.py',
   'PYMODULE'),
  ('django.forms.renderers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\forms\\renderers.py',
   'PYMODULE'),
  ('django.forms.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\forms\\utils.py',
   'PYMODULE'),
  ('django.forms.widgets',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\forms\\widgets.py',
   'PYMODULE'),
  ('django.http',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\http\\__init__.py',
   'PYMODULE'),
  ('django.http.cookie',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\http\\cookie.py',
   'PYMODULE'),
  ('django.http.multipartparser',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\http\\multipartparser.py',
   'PYMODULE'),
  ('django.http.request',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\http\\request.py',
   'PYMODULE'),
  ('django.http.response',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\http\\response.py',
   'PYMODULE'),
  ('django.middleware',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\middleware\\__init__.py',
   'PYMODULE'),
  ('django.middleware.cache',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\middleware\\cache.py',
   'PYMODULE'),
  ('django.middleware.clickjacking',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\middleware\\clickjacking.py',
   'PYMODULE'),
  ('django.middleware.common',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\middleware\\common.py',
   'PYMODULE'),
  ('django.middleware.csrf',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\middleware\\csrf.py',
   'PYMODULE'),
  ('django.middleware.gzip',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\middleware\\gzip.py',
   'PYMODULE'),
  ('django.middleware.http',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\middleware\\http.py',
   'PYMODULE'),
  ('django.middleware.locale',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\middleware\\locale.py',
   'PYMODULE'),
  ('django.middleware.security',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\middleware\\security.py',
   'PYMODULE'),
  ('django.shortcuts',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\shortcuts.py',
   'PYMODULE'),
  ('django.template',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\__init__.py',
   'PYMODULE'),
  ('django.template.autoreload',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\autoreload.py',
   'PYMODULE'),
  ('django.template.backends',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\backends\\__init__.py',
   'PYMODULE'),
  ('django.template.backends.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\backends\\base.py',
   'PYMODULE'),
  ('django.template.backends.django',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\backends\\django.py',
   'PYMODULE'),
  ('django.template.backends.dummy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\backends\\dummy.py',
   'PYMODULE'),
  ('django.template.backends.jinja2',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\backends\\jinja2.py',
   'PYMODULE'),
  ('django.template.backends.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\backends\\utils.py',
   'PYMODULE'),
  ('django.template.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\base.py',
   'PYMODULE'),
  ('django.template.context',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\context.py',
   'PYMODULE'),
  ('django.template.context_processors',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\context_processors.py',
   'PYMODULE'),
  ('django.template.defaultfilters',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\defaultfilters.py',
   'PYMODULE'),
  ('django.template.defaulttags',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\defaulttags.py',
   'PYMODULE'),
  ('django.template.engine',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\engine.py',
   'PYMODULE'),
  ('django.template.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\exceptions.py',
   'PYMODULE'),
  ('django.template.library',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\library.py',
   'PYMODULE'),
  ('django.template.loader',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\loader.py',
   'PYMODULE'),
  ('django.template.loader_tags',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\loader_tags.py',
   'PYMODULE'),
  ('django.template.loaders',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\loaders\\__init__.py',
   'PYMODULE'),
  ('django.template.loaders.app_directories',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\loaders\\app_directories.py',
   'PYMODULE'),
  ('django.template.loaders.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\loaders\\base.py',
   'PYMODULE'),
  ('django.template.loaders.cached',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\loaders\\cached.py',
   'PYMODULE'),
  ('django.template.loaders.filesystem',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\loaders\\filesystem.py',
   'PYMODULE'),
  ('django.template.loaders.locmem',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\loaders\\locmem.py',
   'PYMODULE'),
  ('django.template.response',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\response.py',
   'PYMODULE'),
  ('django.template.smartif',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\smartif.py',
   'PYMODULE'),
  ('django.template.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\template\\utils.py',
   'PYMODULE'),
  ('django.templatetags',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\templatetags\\__init__.py',
   'PYMODULE'),
  ('django.templatetags.cache',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\templatetags\\cache.py',
   'PYMODULE'),
  ('django.templatetags.i18n',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\templatetags\\i18n.py',
   'PYMODULE'),
  ('django.templatetags.l10n',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\templatetags\\l10n.py',
   'PYMODULE'),
  ('django.templatetags.static',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\templatetags\\static.py',
   'PYMODULE'),
  ('django.templatetags.tz',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\templatetags\\tz.py',
   'PYMODULE'),
  ('django.test',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\test\\__init__.py',
   'PYMODULE'),
  ('django.test.client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\test\\client.py',
   'PYMODULE'),
  ('django.test.html',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\test\\html.py',
   'PYMODULE'),
  ('django.test.runner',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\test\\runner.py',
   'PYMODULE'),
  ('django.test.selenium',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\test\\selenium.py',
   'PYMODULE'),
  ('django.test.signals',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\test\\signals.py',
   'PYMODULE'),
  ('django.test.testcases',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\test\\testcases.py',
   'PYMODULE'),
  ('django.test.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\test\\utils.py',
   'PYMODULE'),
  ('django.urls',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\urls\\__init__.py',
   'PYMODULE'),
  ('django.urls.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\urls\\base.py',
   'PYMODULE'),
  ('django.urls.conf',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\urls\\conf.py',
   'PYMODULE'),
  ('django.urls.converters',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\urls\\converters.py',
   'PYMODULE'),
  ('django.urls.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\urls\\exceptions.py',
   'PYMODULE'),
  ('django.urls.resolvers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\urls\\resolvers.py',
   'PYMODULE'),
  ('django.urls.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\urls\\utils.py',
   'PYMODULE'),
  ('django.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\__init__.py',
   'PYMODULE'),
  ('django.utils._os',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\_os.py',
   'PYMODULE'),
  ('django.utils.archive',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\archive.py',
   'PYMODULE'),
  ('django.utils.asyncio',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\asyncio.py',
   'PYMODULE'),
  ('django.utils.autoreload',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\autoreload.py',
   'PYMODULE'),
  ('django.utils.baseconv',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\baseconv.py',
   'PYMODULE'),
  ('django.utils.cache',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\cache.py',
   'PYMODULE'),
  ('django.utils.connection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\connection.py',
   'PYMODULE'),
  ('django.utils.crypto',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\crypto.py',
   'PYMODULE'),
  ('django.utils.datastructures',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\datastructures.py',
   'PYMODULE'),
  ('django.utils.dateformat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\dateformat.py',
   'PYMODULE'),
  ('django.utils.dateparse',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\dateparse.py',
   'PYMODULE'),
  ('django.utils.dates',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\dates.py',
   'PYMODULE'),
  ('django.utils.datetime_safe',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\datetime_safe.py',
   'PYMODULE'),
  ('django.utils.deconstruct',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\deconstruct.py',
   'PYMODULE'),
  ('django.utils.decorators',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\decorators.py',
   'PYMODULE'),
  ('django.utils.deprecation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\deprecation.py',
   'PYMODULE'),
  ('django.utils.duration',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\duration.py',
   'PYMODULE'),
  ('django.utils.encoding',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\encoding.py',
   'PYMODULE'),
  ('django.utils.feedgenerator',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\feedgenerator.py',
   'PYMODULE'),
  ('django.utils.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\formats.py',
   'PYMODULE'),
  ('django.utils.functional',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\functional.py',
   'PYMODULE'),
  ('django.utils.hashable',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\hashable.py',
   'PYMODULE'),
  ('django.utils.html',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\html.py',
   'PYMODULE'),
  ('django.utils.http',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\http.py',
   'PYMODULE'),
  ('django.utils.inspect',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\inspect.py',
   'PYMODULE'),
  ('django.utils.ipv6',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\ipv6.py',
   'PYMODULE'),
  ('django.utils.itercompat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\itercompat.py',
   'PYMODULE'),
  ('django.utils.jslex',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\jslex.py',
   'PYMODULE'),
  ('django.utils.log',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\log.py',
   'PYMODULE'),
  ('django.utils.lorem_ipsum',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\lorem_ipsum.py',
   'PYMODULE'),
  ('django.utils.module_loading',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\module_loading.py',
   'PYMODULE'),
  ('django.utils.numberformat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\numberformat.py',
   'PYMODULE'),
  ('django.utils.regex_helper',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\regex_helper.py',
   'PYMODULE'),
  ('django.utils.safestring',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\safestring.py',
   'PYMODULE'),
  ('django.utils.termcolors',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\termcolors.py',
   'PYMODULE'),
  ('django.utils.text',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\text.py',
   'PYMODULE'),
  ('django.utils.timesince',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\timesince.py',
   'PYMODULE'),
  ('django.utils.timezone',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\timezone.py',
   'PYMODULE'),
  ('django.utils.topological_sort',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\topological_sort.py',
   'PYMODULE'),
  ('django.utils.translation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\translation\\__init__.py',
   'PYMODULE'),
  ('django.utils.translation.reloader',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\translation\\reloader.py',
   'PYMODULE'),
  ('django.utils.translation.template',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\translation\\template.py',
   'PYMODULE'),
  ('django.utils.translation.trans_null',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\translation\\trans_null.py',
   'PYMODULE'),
  ('django.utils.translation.trans_real',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\translation\\trans_real.py',
   'PYMODULE'),
  ('django.utils.tree',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\tree.py',
   'PYMODULE'),
  ('django.utils.version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\version.py',
   'PYMODULE'),
  ('django.utils.xmlutils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\utils\\xmlutils.py',
   'PYMODULE'),
  ('django.views',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\__init__.py',
   'PYMODULE'),
  ('django.views.csrf',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\csrf.py',
   'PYMODULE'),
  ('django.views.debug',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\debug.py',
   'PYMODULE'),
  ('django.views.decorators',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\decorators\\__init__.py',
   'PYMODULE'),
  ('django.views.decorators.cache',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\decorators\\cache.py',
   'PYMODULE'),
  ('django.views.decorators.clickjacking',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\decorators\\clickjacking.py',
   'PYMODULE'),
  ('django.views.decorators.common',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\decorators\\common.py',
   'PYMODULE'),
  ('django.views.decorators.csrf',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\decorators\\csrf.py',
   'PYMODULE'),
  ('django.views.decorators.debug',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\decorators\\debug.py',
   'PYMODULE'),
  ('django.views.decorators.gzip',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\decorators\\gzip.py',
   'PYMODULE'),
  ('django.views.decorators.http',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\decorators\\http.py',
   'PYMODULE'),
  ('django.views.decorators.vary',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\decorators\\vary.py',
   'PYMODULE'),
  ('django.views.defaults',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\defaults.py',
   'PYMODULE'),
  ('django.views.generic',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\generic\\__init__.py',
   'PYMODULE'),
  ('django.views.generic.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\generic\\base.py',
   'PYMODULE'),
  ('django.views.generic.dates',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\generic\\dates.py',
   'PYMODULE'),
  ('django.views.generic.detail',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\generic\\detail.py',
   'PYMODULE'),
  ('django.views.generic.edit',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\generic\\edit.py',
   'PYMODULE'),
  ('django.views.generic.list',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\generic\\list.py',
   'PYMODULE'),
  ('django.views.i18n',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\i18n.py',
   'PYMODULE'),
  ('django.views.static',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\django\\views\\static.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\doctest.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('email',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.mime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\mime\\__init__.py',
   'PYMODULE'),
  ('email.mime.audio',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\mime\\audio.py',
   'PYMODULE'),
  ('email.mime.base',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\mime\\base.py',
   'PYMODULE'),
  ('email.mime.image',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\mime\\image.py',
   'PYMODULE'),
  ('email.mime.message',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\mime\\message.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.text',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\mime\\text.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\email\\utils.py',
   'PYMODULE'),
  ('eopcn_staff',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\__init__.py',
   'PYMODULE'),
  ('eopcn_staff.admin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\admin.py',
   'PYMODULE'),
  ('eopcn_staff.apps',
   'C:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\apps.py',
   'PYMODULE'),
  ('eopcn_staff.forms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\forms.py',
   'PYMODULE'),
  ('eopcn_staff.migrations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\migrations\\__init__.py',
   'PYMODULE'),
  ('eopcn_staff.migrations.0001_initial',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('eopcn_staff.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\models.py',
   'PYMODULE'),
  ('eopcn_staff.templatetags',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\templatetags\\__init__.py',
   'PYMODULE'),
  ('eopcn_staff.templatetags.form_extras',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\templatetags\\form_extras.py',
   'PYMODULE'),
  ('eopcn_staff.tests',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\tests.py',
   'PYMODULE'),
  ('eopcn_staff.urls',
   'C:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\urls.py',
   'PYMODULE'),
  ('eopcn_staff.views',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\views.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\html\\parser.py',
   'PYMODULE'),
  ('http',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imghdr',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\imghdr.py',
   'PYMODULE'),
  ('imp',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ipaddress.py',
   'PYMODULE'),
  ('isodate',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\isodate\\__init__.py',
   'PYMODULE'),
  ('isodate.duration',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\isodate\\duration.py',
   'PYMODULE'),
  ('isodate.isodates',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\isodate\\isodates.py',
   'PYMODULE'),
  ('isodate.isodatetime',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\isodate\\isodatetime.py',
   'PYMODULE'),
  ('isodate.isoduration',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\isodate\\isoduration.py',
   'PYMODULE'),
  ('isodate.isoerror',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\isodate\\isoerror.py',
   'PYMODULE'),
  ('isodate.isostrf',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\isodate\\isostrf.py',
   'PYMODULE'),
  ('isodate.isotime',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\isodate\\isotime.py',
   'PYMODULE'),
  ('isodate.isotzinfo',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\isodate\\isotzinfo.py',
   'PYMODULE'),
  ('isodate.tzinfo',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\isodate\\tzinfo.py',
   'PYMODULE'),
  ('isodate.version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\isodate\\version.py',
   'PYMODULE'),
  ('json',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.config',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\logging\\config.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\mimetypes.py',
   'PYMODULE'),
  ('mssql',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\__init__.py',
   'PYMODULE'),
  ('mssql.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\base.py',
   'PYMODULE'),
  ('mssql.client',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\client.py',
   'PYMODULE'),
  ('mssql.compiler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\compiler.py',
   'PYMODULE'),
  ('mssql.creation',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\creation.py',
   'PYMODULE'),
  ('mssql.features',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\features.py',
   'PYMODULE'),
  ('mssql.functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\functions.py',
   'PYMODULE'),
  ('mssql.introspection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\introspection.py',
   'PYMODULE'),
  ('mssql.management',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\management\\__init__.py',
   'PYMODULE'),
  ('mssql.management.commands',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('mssql.management.commands.inspectdb',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\management\\commands\\inspectdb.py',
   'PYMODULE'),
  ('mssql.management.commands.install_regex_clr',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\management\\commands\\install_regex_clr.py',
   'PYMODULE'),
  ('mssql.operations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\operations.py',
   'PYMODULE'),
  ('mssql.schema',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\mssql\\schema.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\py_compile.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\random.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\site.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\smtplib.py',
   'PYMODULE'),
  ('sndhdr',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\sndhdr.py',
   'PYMODULE'),
  ('socket',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlparse',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\__init__.py',
   'PYMODULE'),
  ('sqlparse.cli',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\cli.py',
   'PYMODULE'),
  ('sqlparse.engine',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlparse.engine.filter_stack',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\engine\\filter_stack.py',
   'PYMODULE'),
  ('sqlparse.engine.grouping',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\engine\\grouping.py',
   'PYMODULE'),
  ('sqlparse.engine.statement_splitter',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\engine\\statement_splitter.py',
   'PYMODULE'),
  ('sqlparse.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\exceptions.py',
   'PYMODULE'),
  ('sqlparse.filters',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\filters\\__init__.py',
   'PYMODULE'),
  ('sqlparse.filters.aligned_indent',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\filters\\aligned_indent.py',
   'PYMODULE'),
  ('sqlparse.filters.others',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\filters\\others.py',
   'PYMODULE'),
  ('sqlparse.filters.output',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\filters\\output.py',
   'PYMODULE'),
  ('sqlparse.filters.reindent',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\filters\\reindent.py',
   'PYMODULE'),
  ('sqlparse.filters.right_margin',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\filters\\right_margin.py',
   'PYMODULE'),
  ('sqlparse.filters.tokens',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\filters\\tokens.py',
   'PYMODULE'),
  ('sqlparse.formatter',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\formatter.py',
   'PYMODULE'),
  ('sqlparse.keywords',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\keywords.py',
   'PYMODULE'),
  ('sqlparse.lexer',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\lexer.py',
   'PYMODULE'),
  ('sqlparse.sql',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\sql.py',
   'PYMODULE'),
  ('sqlparse.tokens',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\tokens.py',
   'PYMODULE'),
  ('sqlparse.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\sqlparse\\utils.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\statistics.py',
   'PYMODULE'),
  ('storages',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\__init__.py',
   'PYMODULE'),
  ('storages.backends',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\backends\\__init__.py',
   'PYMODULE'),
  ('storages.backends.apache_libcloud',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\backends\\apache_libcloud.py',
   'PYMODULE'),
  ('storages.backends.azure_storage',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\backends\\azure_storage.py',
   'PYMODULE'),
  ('storages.backends.dropbox',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\backends\\dropbox.py',
   'PYMODULE'),
  ('storages.backends.ftp',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\backends\\ftp.py',
   'PYMODULE'),
  ('storages.backends.gcloud',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\backends\\gcloud.py',
   'PYMODULE'),
  ('storages.backends.s3',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\backends\\s3.py',
   'PYMODULE'),
  ('storages.backends.s3boto3',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\backends\\s3boto3.py',
   'PYMODULE'),
  ('storages.backends.sftpstorage',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\backends\\sftpstorage.py',
   'PYMODULE'),
  ('storages.base',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\base.py',
   'PYMODULE'),
  ('storages.compress',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\compress.py',
   'PYMODULE'),
  ('storages.utils',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\storages\\utils.py',
   'PYMODULE'),
  ('string',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('tzdata',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\uu.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\uuid.py',
   'PYMODULE'),
  ('wave',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\wave.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\webbrowser.py',
   'PYMODULE'),
  ('wsgiref',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\wsgiref\\__init__.py',
   'PYMODULE'),
  ('wsgiref.handlers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\wsgiref\\handlers.py',
   'PYMODULE'),
  ('wsgiref.headers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\wsgiref\\headers.py',
   'PYMODULE'),
  ('wsgiref.simple_server',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\wsgiref\\simple_server.py',
   'PYMODULE'),
  ('wsgiref.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\wsgiref\\util.py',
   'PYMODULE'),
  ('xml',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.8_3.8.2800.0_x64__qbz5n2kfra8p0\\lib\\zipimport.py',
   'PYMODULE'),
  ('zipp',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.compat',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob',
   'C:\\Users\\<USER>\\Py '
   'test\\Python-testing\\EOPCNOpApp\\venv\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE')])
