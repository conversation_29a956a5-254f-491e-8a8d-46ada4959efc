{% extends "base.html" %}

{% block title %}{{ staff_member.first_name }} {{ staff_member.last_name }} Details{% endblock %}

{% block content %}
{% load custom_filters %}
<!DOCTYPE html>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">

<style>
  :root {
    --primary-color: #2c5282;
    --primary-light: #3182ce;
    --primary-dark: #1a365d;
    --secondary-color: #38a169;
    --accent-color: #ed8936;
    --background-light: #f7fafc;
    --background-white: #ffffff;
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --border-light: #e2e8f0;
    --border-medium: #cbd5e0;
    --success-color: #48bb78;
    --danger-color: #f56565;
    --warning-color: #ed8936;
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-light);
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0;
    padding: 0;
  }

  .container-main {
    max-width: 1800px;
    margin: 0 auto;
    padding: 20px;
  }

  .page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
  }

  .page-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: -0.025em;
  }

  .button-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--background-white);
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-light);
  }

  .btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    gap: 0.5rem;
  }

  .btn-primary {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
  }

  .btn-primary:hover {
    background: var(--primary-dark);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  .btn-success {
    background: var(--secondary-color);
    color: white;
    box-shadow: var(--shadow-sm);
  }

  .btn-success:hover {
    background: #2f855a;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  .btn-secondary {
    background: #718096;
    color: white;
    box-shadow: var(--shadow-sm);
  }

  .btn-secondary:hover {
    background: #4a5568;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  .select-modern {
    padding: 0.5rem 0.75rem;
    border: 2px solid var(--border-light);
    border-radius: 6px;
    font-size: 0.95rem;
    background: white;
    transition: border-color 0.2s ease;
    min-width: 280px;
  }

  .select-modern:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 82, 130, 0.1);
  }

  .layout-grid {
    display: grid;
    grid-template-columns: 700px 1fr;
    gap: 2rem;
    align-items: start;
  }

  .card-modern {
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: box-shadow 0.2s ease;
  }

  .card-modern:hover {
    box-shadow: var(--shadow-md);
  }

  .card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-light);
  }

  .card-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .card-body {
    padding: 1.5rem;
  }

  .staff-profile {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
    flex-wrap: wrap;
  }

  .profile-image {
    width: 160px;
    height: 160px;
    border-radius: 12px;
    object-fit: cover;
    object-position: center 25%;
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
  }

  .profile-placeholder {
    width: 160px;
    height: 160px;
    border-radius: 12px;
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-size: 3rem;
    font-weight: 300;
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
  }

  .profile-info {
    flex: 1;
    min-width: 280px;
  }

  .profile-info h1 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.2;
  }

  .profile-meta {
    display: grid;
    gap: 0.75rem;
    margin-top: 1rem;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
  }

  .meta-label {
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 140px;
    flex-shrink: 0;
  }

  .meta-value {
    color: var(--text-primary);
  }

  .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .status-active {
    background-color: #d4edda;
    color: #155724;
  }

  .status-inactive {
    background-color: #f8d7da;
    color: #721c24;
  }

  .contact-list {
    display: grid;
    gap: 1rem;
  }

  .contact-item {
    background: #f8fafc;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 1.25rem;
    transition: all 0.2s ease;
  }

  .contact-item:hover {
    background: #f1f5f9;
    border-color: var(--border-medium);
  }

  .contact-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }

  .contact-clinic {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
  }

  .contact-clinic a {
    color: var(--primary-color);
    text-decoration: none;
  }

  .contact-clinic a:hover {
    text-decoration: underline;
  }

  .contact-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    font-size: 0.9rem;
  }

  .detail-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .detail-label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .detail-value {
    color: var(--text-primary);
  }

  .weekdays {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .weekday-tag {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
  }

  .assignments-grid {
    display: grid;
    gap: 2rem;
  }

  .assignment-pair {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    padding: 1rem 0 0 0;
  }

  .assignment-pair:first-child {
    padding-top: 0;
  }

  .role-info {
    display: grid;
    gap: 0.75rem;
    margin-top: 1rem;
  }

  .allocation-list {
    display: grid;
    gap: 1rem;
    margin-top: 1rem;
  }

  .allocation-item {
    background: #f8fafc;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 1rem;
  }

  .divider {
    height: 1px;
    background: linear-gradient(to right, transparent, var(--border-medium), transparent);
    margin: 2rem 0;
  }

  .leave-list {
    display: grid;
    gap: 1rem;
  }

  .leave-item {
    background: #f8fafc;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 1.25rem;
  }

  .leave-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .leave-type {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
  }

  .coverage-list {
    margin-left: 1rem;
    margin-top: 0.75rem;
    padding-left: 1rem;
    border-left: 3px solid var(--border-medium);
  }

  .coverage-item {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
  }

  .link-primary {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
  }

  .link-primary:hover {
    text-decoration: underline;
  }

  @media (max-width: 1400px) {
    .layout-grid {
      grid-template-columns: 400px 1fr;
    }
  }

  @media (max-width: 1200px) {
    .layout-grid {
      grid-template-columns: 1fr;
    }
    
    .assignment-pair {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 768px) {
    .button-container {
      flex-direction: column;
      gap: 1rem;
    }
    
    .staff-profile {
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
    
    .contact-details {
      grid-template-columns: 1fr;
    }
  }
</style>
</head>
<body>

<div class="container-main">
  {% if messages %}
    <script>
      {% for message in messages %}
        alert("{{ message }}");
      {% endfor %}
    </script>
  {% endif %}

  <script>
    function redirectToStaffDetail() {
      const staffId = document.getElementById('staff_member').value;
      if (staffId) {
        const url = `{% url 'staff_detail' 0 %}`.replace('0', staffId);
        window.location.href = url;
      }
    }
  </script>

  <div class="layout-grid">
    <!-- Left Column -->
    <div class="left-column">
      <!-- Staff Profile Card -->
      <div class="card-modern">
        <div class="card-header">
          <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
            <h2 style="margin: 0;">Staff Profile</h2>
            <div style="display: flex; gap: 1rem; align-items: center;">
              <a href="{% url 'lists' %}" class="btn btn-secondary" style="font-size: 0.875rem; padding: 0.5rem 1rem;">Staff Lists</a>
              <form method="get" id="staffDetailForm" style="display: inline;">
                <select name="staff_member" id="staff_member" onchange="redirectToStaffDetail()" class="select-modern" style="min-width: 250px;">
                  <option value="" selected disabled>Jump to Staff Member</option>
                  {% for staff in staff_members %}
                    <option value="{{ staff.pk }}" {% if staff.pk == staff_member.pk %}selected{% endif %}>{{ staff.first_name }} {{ staff.last_name }}</option>
                  {% endfor %}
                </select>
              </form>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="staff-profile">
            <div>
              {% if staff_photo_url %}
                <img src="{{ staff_photo_url }}" alt="{{ staff_member.name }}" class="profile-image" 
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div class="profile-placeholder" style="display: none;">
                  {{ staff_member.first_name|first }}{{ staff_member.last_name|first }}
                </div>
              {% else %}
                <div class="profile-placeholder">
                  {{ staff_member.first_name|first }}{{ staff_member.last_name|first }}
                </div>
              {% endif %}
            </div>
            
            <div class="profile-info">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h1>{{ staff_member.first_name }} {{ staff_member.last_name }}</h1>
                <div style="display: flex; gap: 0.5rem;">
                  <a href="{% url 'edit_profile' staff_member.pk %}" class="btn btn-primary">Edit Profile</a>
                  {% if staff_is_supervisor %}
                    <a href="{% url 'supervisor_staff' supervisor_id %}" class="btn btn-secondary">View Team</a>
                  {% endif %}
                </div>
              </div>

              <div class="profile-meta">
                <div class="meta-item" style="opacity: 0.7; font-size: 0.85rem;">
                  <span class="meta-label">Database ID:</span>
                  <span class="meta-value">{{ staff_member.staff_id }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">Status:</span>
                  <span class="status-badge {% if staff_member.currently_active %}status-active{% else %}status-inactive{% endif %}">
                    {{ staff_member.currently_active|yesno:"Active,Inactive" }}
                  </span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">Start Date:</span>
                  <span class="meta-value">{{ staff_member.start_date }}</span>
                </div>
                {% if staff_member.end_date %}
                <div class="meta-item">
                  <span class="meta-label">End Date:</span>
                  <span class="meta-value">{{ staff_member.end_date }}</span>
                </div>
                {% endif %}
                <div class="meta-item">
                  <span class="meta-label">Email:</span>
                  <span class="meta-value">{{ staff_member.suggested_email }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">N95 Size:</span>
                  <span class="meta-value">{{ staff_member.n95_mask_size }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">Computer:</span>
                  <span class="meta-value">{{ staff_member.computer_number }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact Information Card -->
      <div class="card-modern" style="margin-top: 2rem;">
        <div class="card-header">
          <h2>
            Contact Information
            <div style="display: flex; gap: 0.5rem;">
              <a href="{% url 'seating_map' %}" class="btn btn-secondary" style="font-size: 0.875rem; padding: 0.5rem 1rem;">Seating Map</a>
              <a href="{% url 'add_contact_info' staff_member.pk %}" class="btn btn-success" style="font-size: 0.875rem; padding: 0.5rem 1rem;">+ Add Contact</a>
            </div>
          </h2>
        </div>
        <div class="card-body">
          <div class="contact-list">
            {% for contact in location_contacts %}
            <div class="contact-item">
              <div class="contact-header">
                <h3 class="contact-clinic">
                  {% if not contact.clinic.clinic_name %}
                    {{ contact.contact_type }}
                  {% else %}
                    {% if contact.clinic.clinic_id %}
                      <a href="{% url 'clinic_detail' contact.clinic.clinic_id %}">{{ contact.clinic.clinic_name }}</a>
                    {% else %}
                      {{ contact.clinic.clinic_name }}
                    {% endif %}
                  {% endif %}
                </h3>
                <a href="{% url 'edit_contact_info' contact.location_contact_id %}" class="btn btn-primary" style="font-size: 0.8rem; padding: 0.4rem 0.8rem;">Edit</a>
              </div>

              <div class="contact-details">
                {% if contact.office_number %}
                <div class="detail-group">
                  <span class="detail-label">Office</span>
                  <span class="detail-value">{{ contact.office_number }}</span>
                </div>
                {% endif %}
                
                <div class="detail-group">
                  <span class="detail-label">Contact Type</span>
                  <span class="detail-value">{{ contact.contact_type|default:"N/A" }}</span>
                </div>
                
                {% if contact.desk_number %}
                <div class="detail-group">
                  <span class="detail-label">Desk</span>
                  <span class="detail-value">{{ contact.desk_number }}</span>
                </div>
                {% endif %}
                
                <div class="detail-group">
                  <span class="detail-label">Direct Phone</span>
                  <span class="detail-value">{{ contact.phone|phone_format|default:"N/A" }}</span>
                </div>
                
                {% if contact.clinic.business_phone %}
                <div class="detail-group">
                  <span class="meta-label">Clinic Contact</span>
                  <span class="meta-value">{{ contact.clinic.business_phone|phone_format }}{% if contact.extension %} Ext. {{ contact.extension }}{% endif %}</span>
                </div>
                {% endif %}
              </div>

              <div class="detail-group" style="margin-top: 1rem;">
                <span class="detail-label">Days Present</span>
                <div class="weekdays">
                  {% if contact.monday %}<span class="weekday-tag">Mon</span>{% endif %}
                  {% if contact.tuesday %}<span class="weekday-tag">Tue</span>{% endif %}
                  {% if contact.wednesday %}<span class="weekday-tag">Wed</span>{% endif %}
                  {% if contact.thursday %}<span class="weekday-tag">Thu</span>{% endif %}
                  {% if contact.friday %}<span class="weekday-tag">Fri</span>{% endif %}
                </div>
              </div>

              {% if contact.contact_notes %}
              <div class="detail-group" style="margin-top: 1rem;">
                <span class="detail-label">Notes</span>
                <span class="detail-value">{{ contact.contact_notes }}</span>
              </div>
              {% endif %}
            </div>
            {% empty %}
            <div class="contact-item">
              <p style="margin: 0; color: var(--text-muted); font-style: italic;">No contact information available.</p>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>

      <!-- Leave Records Card -->
      <div class="card-modern" style="margin-top: 2rem;">
        <div class="card-header">
          <h2>
            Leave Records
            <a href="{% url 'add_staff_leave' staff_member.pk %}" class="btn btn-success" style="font-size: 0.875rem; padding: 0.5rem 1rem;">+ Add Leave</a>
          </h2>
        </div>
        <div class="card-body">
          <div class="leave-list">
            {% for leave in staff_member.leaves.all %}
            <div class="leave-item">
              <div class="leave-header">
                <div>
                  <div class="leave-type">{{ leave.leave_type }}</div>
                  <div style="color: var(--text-secondary); font-size: 0.9rem;">{{ leave.leave_start_date }} to {{ leave.return_date }}</div>
                </div>
                <a href="{% url 'edit_staff_leave' leave.pk %}" class="btn btn-primary" style="font-size: 0.8rem; padding: 0.4rem 0.8rem;">Edit</a>
              </div>

              <div class="coverage-list">
                {% for coverage in leave.staffcoverage_set.all %}
                <div class="coverage-item">
                  {% if coverage.covering_staff %}
                    <strong>Covering Staff:</strong>
                    <a href="{% url 'staff_detail' coverage.covering_staff.pk %}" class="link-primary">
                      {{ coverage.covering_staff.first_name }} {{ coverage.covering_staff.last_name }}
                    </a>
                    <br>
                    <strong>Coverage Period:</strong> {{ coverage.coverage_start_date }} to {{ coverage.coverage_end_date }}
                    <br>
                    <strong>Type:</strong> {{ coverage.coverage_type|default:"Not specified" }}
                  {% else %}
                    <em>No covering staff assigned</em>
                  {% endif %}
                </div>
                {% empty %}
                <div class="coverage-item">
                  <em>No coverage records available for this leave.</em>
                </div>
                {% endfor %}
              </div>
            </div>
            {% empty %}
            <div class="leave-item">
              <p style="margin: 0; color: var(--text-muted); font-style: italic;">No leave records available.</p>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column - Assignments -->
    <div class="right-column">
      <div class="card-modern">
        <div class="card-header">
          <h2>
            Role Assignments & Allocations
            <a href="{% url 'add_staff_assignment' staff_member.pk %}" class="btn btn-success" style="font-size: 0.875rem; padding: 0.5rem 1rem;">+ Add Role</a>
          </h2>
        </div>
        <div class="card-body">
          <div class="assignments-grid">
            {% for assignment in assignments %}
            {% if not forloop.first %}
              <div class="divider"></div>
            {% endif %}

            <div class="assignment-pair">
              <!-- Role Assignment -->
              <div>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                  <h3 style="margin: 0; color: var(--primary-color);">Role Assignment {{ forloop.counter }}</h3>
                  <a href="{% url 'edit_assignment' assignment.pk %}" class="btn btn-primary" style="font-size: 0.8rem; padding: 0.4rem 0.8rem;">Edit Role</a>
                </div>

                <div style="background: #f8fafc; padding: 1.25rem; border-radius: 8px; border: 1px solid var(--border-light);">
                  <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                    <span class="status-badge {% if assignment.currently_active %}status-active{% else %}status-inactive{% endif %}">
                      {{ assignment.currently_active|yesno:"Active,Inactive" }}
                    </span>
                  </div>
                  
                  <div style="margin-left: calc(140px + 0.5rem); margin-top: -2.5rem; margin-bottom: 1rem;">
                    <span style="font-weight: 600; color: var(--text-primary);">{{ assignment.role.role_name }} (FTE: {{ assignment.role_fte }})</span>
                  </div>
                  
                  <div class="role-info">
                    <div class="meta-item">
                      <span class="meta-label">Program:</span>
                      <span class="meta-value">{{ assignment.service.service_name }}</span>
                    </div>
                    <div class="meta-item">
                      <span class="meta-label">Position Number:</span>
                      <span class="meta-value">{{ assignment.position.position_number }}</span>
                    </div>
                    <div class="meta-item">
                      <span class="meta-label">Type:</span>
                      <span class="meta-value">{{ assignment.permanent_vs_temporary }}</span>
                    </div>
                    <div class="meta-item">
                      <span class="meta-label">Reports To:</span>
                      <span class="meta-value">
                        {% if assignment.supervisor and assignment.supervisor.pk %}
                          <a href="{% url 'supervisor_staff' assignment.supervisor.pk %}" class="link-primary">
                            {{ assignment.supervisor.staff.first_name }} {{ assignment.supervisor.staff.last_name }} (view team)
                          </a>
                        {% else %}
                          {{ assignment.supervisor.staff.first_name|default:"" }} {{ assignment.supervisor.staff.last_name|default:"" }}
                        {% endif %}
                      </span>
                    </div>
                    <div class="meta-item">
                      <span class="meta-label">Start Date:</span>
                      <span class="meta-value">{{ assignment.start_date }}</span>
                    </div>
                    {% if assignment.end_date %}
                    <div class="meta-item">
                      <span class="meta-label">End Date:</span>
                      <span class="meta-value">{{ assignment.end_date }}</span>
                    </div>
                    {% endif %}
                  </div>

                  {% if staff_member.coverages.all %}
                  <div style="margin-top: 1.5rem;">
                    <h5 style="margin: 0 0 0.75rem 0; color: var(--text-secondary);">Currently Covering For:</h5>
                    <div style="background: white; padding: 1rem; border-radius: 6px; border: 1px solid var(--border-light);">
                      {% for coverage in staff_member.coverages.all %}
                      <div style="margin-bottom: 0.5rem;">
                        <a href="{% url 'staff_detail' coverage.leave.staff.pk %}" class="link-primary">
                          {{ coverage.leave.staff.first_name }} {{ coverage.leave.staff.last_name }}
                        </a>
                        <span style="color: var(--text-muted); font-size: 0.9rem;">
                          ({{ coverage.coverage_start_date }} - {{ coverage.coverage_end_date }})
                        </span>
                      </div>
                      {% endfor %}
                    </div>
                  </div>
                  {% endif %}
                </div>
              </div>

              <!-- FTE Allocations -->
              <div>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                  <h3 style="margin: 0; color: var(--secondary-color);">FTE Allocations</h3>
                  <a href="{% url 'edit_allocation' assignment.pk %}" class="btn btn-success" style="font-size: 0.8rem; padding: 0.4rem 0.8rem;">Manage Allocations</a>
                </div>

                <div class="allocation-list">
                  {% for allocation in assignment.allocations.all %}
                  <div class="allocation-item">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                      <span class="status-badge {% if allocation.currently_active %}status-active{% else %}status-inactive{% endif %}">
                        {{ allocation.currently_active|yesno:"Active,Inactive" }}
                      </span>
                      <span style="font-weight: 600; color: var(--text-primary);">{{ allocation.allocation_type }}</span>
                    </div>

                    <div style="display: grid; gap: 0.5rem; font-size: 0.9rem;">
                      <div class="meta-item">
                        <span class="meta-label">Type:</span>
                        <span class="meta-value">{{ allocation.centralized_vs_ric }}</span>
                      </div>
                      
                      {% if allocation.clinic %}
                      <div class="meta-item">
                        <span class="meta-label">Clinic:</span>
                        <span class="meta-value">
                          {% if allocation.clinic.clinic_id %}
                            <a href="{% url 'clinic_detail' allocation.clinic.clinic_id %}" class="link-primary">{{ allocation.clinic.clinic_name }}</a>
                          {% else %}
                            {{ allocation.clinic.clinic_name }}
                          {% endif %}
                        </span>
                      </div>
                      {% endif %}

                      {% if allocation.assignment_in_clinic %}
                      <div class="meta-item">
                        <span class="meta-label">Assignment:</span>
                        <span class="meta-value">{{ allocation.assignment_in_clinic }}</span>
                      </div>
                      {% endif %}

                      {% if allocation.program %}
                      <div class="meta-item">
                        <span class="meta-label">EOPCN Team:</span>
                        <span class="meta-value">{{ allocation.program.program_name }}</span>
                      </div>
                      {% endif %}

                      {% if allocation.allocation_type == "Remote" or allocation.allocation_type == "In-Person" %}
                      <div class="meta-item">
                        <span class="meta-label">FTE:</span>
                        <span class="meta-value">{{ allocation.fte }}</span>
                      </div>
                      {% endif %}

                      <div class="meta-item">
                        <span class="meta-label">Period:</span>
                        <span class="meta-value">{{ allocation.start_date }}{% if allocation.end_date %} - {{ allocation.end_date }}{% endif %}</span>
                      </div>

                      {% if allocation.allocation_type != "Remote" and allocation.allocation_type != "Project Based Support" %}
                      <div style="margin-top: 0.75rem;">
                        <span class="detail-label">Schedule:</span>
                        <div class="weekdays" style="margin-top: 0.25rem;">
                          {% if allocation.monday %}<span class="weekday-tag">Mon</span>{% endif %}
                          {% if allocation.tuesday %}<span class="weekday-tag">Tue</span>{% endif %}
                          {% if allocation.wednesday %}<span class="weekday-tag">Wed</span>{% endif %}
                          {% if allocation.thursday %}<span class="weekday-tag">Thu</span>{% endif %}
                          {% if allocation.friday %}<span class="weekday-tag">Fri</span>{% endif %}
                        </div>
                      </div>
                      {% endif %}
                    </div>
                  </div>
                  {% empty %}
                  <div class="allocation-item">
                    <p style="margin: 0; color: var(--text-muted); font-style: italic;">No allocations for this role.</p>
                  </div>
                  {% endfor %}
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

</body>
</html>
{% endblock %}