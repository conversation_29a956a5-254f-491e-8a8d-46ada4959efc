{% extends "base.html" %}

{% block title %}Physician Panel Data Preview{% endblock %}

{% block content %}
<style>
body {
  font-family: arial, sans-serif;
  margin: 20px;
  padding: 0;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
}

.container {
  max-width: 1200px;
  margin: auto;
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 15px;
  border: 1px solid #c3e6cb;
  border-radius: 5px;
  margin-bottom: 20px;
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.preview-table th, .preview-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.preview-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.preview-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 10px 5px;
  cursor: pointer;
  border-radius: 4px;
}

.button-blue {
  background-color: #008CBA;
}

.button-blue:hover {
  background-color: #006e92;
}

.button-green {
  background-color: #4CAF50;
}

.button-green:hover {
  background-color: #45a049;
}

.section {
  margin-bottom: 30px;
}

.stats {
  font-size: 1.1em;
  margin-bottom: 15px;
}
</style>

<div class="container">
    <h2>Physician Panel Data Upload Preview</h2>

    {% if upload_success %}
        <div class="success-message">
            <strong>Upload Successful!</strong><br>
            Successfully processed file <strong>{{ file_name }}</strong>.<br>
            <strong>{{ total_records }}</strong> records were successfully inserted into <code>AH_staging_physician_panel</code>.
            {% if total_attempted and total_attempted != total_records %}
                <br><span style="color: orange;">{{ total_attempted|add:"-"|add:total_records }} records had issues and were skipped.</span>
            {% endif %}
            <br>The table has been cleared and repopulated with the contents of your file.
            <br>Column mapping: <code>Last_Name → Phys_Last_Name</code>, <code>First_Name → Phys_First_Name</code>, <code>Total_Panel → Total_Panel_Size</code>
            <br>The <code>report_month</code> was set to <strong>{{ report_month }}</strong> and <code>report_year</code> was set to <strong>{{ report_year }}</strong>.
            <br>Other columns like <code>Full_Name_Concat</code>, <code>name_update_status</code>, and <code>physician_id</code> will be populated in the next steps.
        </div>
    {% endif %}

    <div class="section">
        <h3>Preview of Uploaded Data</h3>
        <div class="stats">
            <strong>Total Records:</strong> {{ preview_data|length }}<br>
            <strong>Report Period:</strong> {{ report_month }}/{{ report_year }}
        </div>

        {% if preview_data %}
            <table class="preview-table">
                <thead>
                    <tr>
                        {% for column in preview_columns %}
                            <th>{{ column }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for row in preview_data %}
                        <tr>
                            {% for cell in row %}
                                <td>{{ cell|default_if_none:"" }}</td>
                            {% endfor %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <p>No data to preview.</p>
        {% endif %}
    </div>

    <div class="section">
        <h3>Next Steps</h3>
        <p>The data is now in the staging table. You can proceed with the data processing steps from the notebook to:</p>
        <ol>
            <li>Concatenate physician names</li>
            <li>Update names using the mapping table</li>
            <li>Insert physician IDs</li>
            <li>Transfer data to the master table</li>
        </ol>
        
        <form method="post" action="{% url 'process_staged_panel_data' %}" style="display: inline;">
            {% csrf_token %}
            <button type="submit" class="button button-green">Process Staged Data</button>
        </form>
    </div>
</div>

{% endblock %}
