# Generated by Django 4.2.15 on 2024-08-31 03:08

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Clinic',
            fields=[
                ('clinic_id', models.AutoField(primary_key=True, serialize=False)),
                ('clinic_name', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('med_group_or_site', models.CharField(blank=True, max_length=255, null=True)),
                ('street_address', models.CharField(blank=True, max_length=255, null=True)),
                ('floor_unit_room', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('city', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('province', models.CharField(blank=True, max_length=100, null=True)),
                ('postal_code', models.Char<PERSON>ield(blank=True, max_length=20, null=True)),
                ('business_phone', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=20, null=True)),
                ('extension', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=10, null=True)),
                ('fax', models.Char<PERSON>ield(blank=True, max_length=20, null=True)),
                ('clinic_website', models.URLField(blank=True, null=True)),
                ('clinic_emr', models.CharField(blank=True, max_length=100, null=True)),
                ('PIA_number', models.CharField(blank=True, max_length=50, null=True)),
                ('include_on_eopcn_website', models.BooleanField(default=False)),
                ('external_member_clinic', models.BooleanField(default=False)),
                ('EOPCN_facilitator', models.CharField(blank=True, max_length=255, null=True)),
                ('development_stage', models.CharField(blank=True, max_length=100, null=True)),
                ('primary_contact', models.CharField(blank=True, max_length=255, null=True)),
                ('primary_contact_role', models.CharField(blank=True, max_length=255, null=True)),
                ('primary_contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('primary_contact_ext', models.CharField(blank=True, max_length=10, null=True)),
                ('primary_contact_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('date_modified', models.DateTimeField(auto_now=True)),
                ('modified_by', models.CharField(blank=True, max_length=255, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'mh_clinics',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Position',
            fields=[
                ('position_id', models.AutoField(primary_key=True, serialize=False)),
                ('position_number', models.CharField(max_length=255)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.CharField(max_length=255)),
            ],
            options={
                'db_table': 'positions',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Program',
            fields=[
                ('program_id', models.AutoField(primary_key=True, serialize=False)),
                ('program_name', models.CharField(max_length=255)),
            ],
            options={
                'db_table': 'programs',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('service_id', models.AutoField(primary_key=True, serialize=False)),
                ('service_name', models.CharField(max_length=255)),
                ('division', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'services',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Staff',
            fields=[
                ('staff_id', models.AutoField(primary_key=True, serialize=False)),
                ('staff_name', models.CharField(max_length=255)),
                ('start_date', models.DateField()),
                ('leave_start_date', models.DateField(blank=True, null=True)),
                ('return_date', models.DateField(blank=True, null=True)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('currently_active', models.BooleanField(default=True)),
                ('suggested_email', models.EmailField(max_length=255)),
                ('date_modified', models.DateTimeField(auto_now=True)),
                ('modified_by', models.CharField(max_length=255)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.CharField(max_length=255)),
                ('currently_on_leave', models.BooleanField(default=False)),
            ],
            options={
                'db_table': 'staff',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StaffAllocation',
            fields=[
                ('staff_allocation_id', models.AutoField(primary_key=True, serialize=False)),
                ('centralized_vs_ric', models.CharField(max_length=255)),
                ('fte', models.DecimalField(decimal_places=2, max_digits=5)),
                ('start_date', models.DateField(blank=True, null=True)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('date_modified', models.DateTimeField(auto_now=True)),
                ('modified_by', models.CharField(default='system', max_length=255)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True)),
                ('created_by', models.CharField(default='system', max_length=255)),
            ],
            options={
                'db_table': 'staff_allocation',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StaffAssignment',
            fields=[
                ('staff_role_id', models.AutoField(primary_key=True, serialize=False)),
                ('currently_active', models.BooleanField(default=True)),
                ('permanent_vs_temporary', models.CharField(max_length=255)),
                ('role_fte', models.CharField(max_length=255)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('date_modified', models.DateTimeField(auto_now=True)),
                ('modified_by', models.CharField(max_length=255)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.CharField(max_length=255)),
            ],
            options={
                'db_table': 'staff_assignments',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StaffRole',
            fields=[
                ('role_id', models.AutoField(primary_key=True, serialize=False)),
                ('role_name', models.CharField(max_length=255)),
                ('ah_role_title', models.CharField(max_length=255)),
                ('ah_role_category', models.CharField(max_length=255)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.CharField(max_length=255)),
            ],
            options={
                'db_table': 'staff_roles',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='StaffSupervisor',
            fields=[
                ('supervisor_id', models.AutoField(primary_key=True, serialize=False)),
                ('supervisor_name', models.CharField(max_length=255)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.CharField(max_length=255)),
            ],
            options={
                'db_table': 'staff_supervisors',
                'managed': False,
            },
        ),
    ]
