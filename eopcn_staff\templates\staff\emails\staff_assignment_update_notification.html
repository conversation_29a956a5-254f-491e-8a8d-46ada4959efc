<!DOCTYPE html>
<html>
<head>
    <title>Assignment Updated for {{ staff_member.first_name }} {{ staff_member.last_name }}</title>
</head>
<body>
    <h2>Assignment Updated for {{ staff_member.first_name }} {{ staff_member.last_name }}</h2>

    <p>Hello,</p>

    <p>
        The role assignment for <strong>{{ staff_member.first_name }} {{ staff_member.last_name }}</strong> has been updated. Below are the updated details for the assignment:
    </p>

    <p><strong>Assignment Details:</strong></p>
    <ul>
        {% if assignment.role %}
        <li><strong{% if changes.role %} style="background-color:#fff8c6;"{% endif %}>Role:</strong>
            {% if changes.role %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.role.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.role.new_value }}</span>
            {% else %}
                {{ assignment.role.role_name }}
            {% endif %}
        </li>
        {% endif %}

        {% if assignment.supervisor %}
        <li><strong{% if changes.supervisor %} style="background-color:#fff8c6;"{% endif %}>Supervisor:</strong>
            {% if changes.supervisor %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.supervisor.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.supervisor.new_value }}</span>
            {% else %}
                {{ assignment.supervisor.supervisor_name }}
            {% endif %}
        </li>
        {% endif %}

        {% if assignment.position %}
        <li><strong{% if changes.position %} style="background-color:#fff8c6;"{% endif %}>Position:</strong>
            {% if changes.position %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.position.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.position.new_value }}</span>
            {% else %}
                {{ assignment.position.position_number }}
            {% endif %}
        </li>
        {% endif %}

        {% if assignment.permanent_vs_temporary %}
        <li><strong{% if changes.permanent_vs_temporary %} style="background-color:#fff8c6;"{% endif %}>Assignment Type:</strong>
            {% if changes.permanent_vs_temporary %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.permanent_vs_temporary.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.permanent_vs_temporary.new_value }}</span>
            {% else %}
                {{ assignment.permanent_vs_temporary }}
            {% endif %}
        </li>
        {% endif %}

        {% if assignment.start_date %}
        <li><strong{% if changes.start_date %} style="background-color:#fff8c6;"{% endif %}>Start Date:</strong>
            {% if changes.start_date %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.start_date.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.start_date.new_value }}</span>
            {% else %}
                {{ assignment.start_date }}
            {% endif %}
        </li>
        {% endif %}

        {% if assignment.end_date %}
        <li><strong{% if changes.end_date %} style="background-color:#fff8c6;"{% endif %}>End Date:</strong>
            {% if changes.end_date %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.end_date.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.end_date.new_value }}</span>
            {% else %}
                {{ assignment.end_date }}
            {% endif %}
        </li>
        {% endif %}

        {% if assignment.role_fte %}
        <li><strong{% if changes.role_fte %} style="background-color:#fff8c6;"{% endif %}>FTE:</strong>
            {% if changes.role_fte %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.role_fte.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.role_fte.new_value }}</span>
            {% else %}
                {{ assignment.role_fte }}
            {% endif %}
        </li>
        {% endif %}
    </ul>



    {% if comment %}
    <h3>Additional Comments</h3>
    <p><strong>Comment:</strong> <span style="background-color:#DDEBF7">{{ comment }}</span></p>
    {% endif %}

    {% if details_url %}
    <p>
        <a href="{{ details_url }}">View record in operational database</a>
    </p>
    {% endif %}
    <p>
        Submitted by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).
    </p>

    <p>Best regards,</p>
    <p>EOPCN Automation Admin</p>
</body>
</html>
