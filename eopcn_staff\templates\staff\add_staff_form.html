{% extends "base.html" %}

{% block title %}Add/Edit Staff Member{% endblock %}

{% block content %}
{% load form_extras static %}
<!-- Link to the external stylesheet -->
<link rel="stylesheet" type="text/css" href="{% static 'eopcn_staff/css/staff_form.css' %}">

<div class="form-wrapper">
    <div class="form-container">
        <h2 class="subheading">Add a New Staff Member</h2>
        <div class="alert alert-info">
            <small><em style="color: #6c757d;">Note: Fields marked with an asterisk (*) are required.</em></small>
        </div>
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}

            <!-- Display Errors for Staff Form -->
            {% if staff_form.errors %}
                <div class="alert alert-danger">
                    <ul>
                        {% for field, errors in staff_form.errors.items %}
                            <li>{{ field }}: {{ errors|join:", " }}</li>
                        {% endfor %}
                    </ul>
                </div>
            {% endif %}
    </div>
</div>

<div class="form-wrapper">
    <div class="form-container">
        <!-- Staff Details Section -->
        <h3>1. Staff Profile</h3>
        <hr>

        <!-- Photo Upload Field -->
        <div class="form-group">
            {{ staff_form.photo.label_tag }}
            {{ staff_form.photo }}
        </div>

        <div class="form-group">
            <label for="{{ staff_form.first_name.id_for_label }}">{{ staff_form.first_name.label }} <span class="text-danger">:*</span></label>
            {{ staff_form.first_name }}
        </div>

        <div class="form-group">
            <label for="{{ staff_form.last_name.id_for_label }}">{{ staff_form.last_name.label }} <span class="text-danger">:*</span></label>
            {{ staff_form.last_name }}
        </div>
        
        <div class="form-group">
            <label for="{{ staff_form.start_date.id_for_label }}">{{ staff_form.start_date.label }} <span class="text-danger">:*</span></label>
            {{ staff_form.start_date }}
        </div>

        <div class="form-group">
            {{ staff_form.suggested_email.label_tag }}
            {{ staff_form.suggested_email }}
        </div>

        <div class="form-group">
            <label for="{{ staff_form.currently_active.id_for_label }}">{{ staff_form.currently_active.label }} <span class="text-danger">:*</span></label>
            {{ staff_form.currently_active }}
        </div>

        <div class="form-group">
            {{ staff_form.n95_mask_size.label_tag }}
            {{ staff_form.n95_mask_size }}
        </div>

        <div class="form-group">
            {{ staff_form.computer_number.label_tag }}
            {{ staff_form.computer_number }}
        </div>

        <!-- Display Errors for Assignment Form -->
        {% if assignment_form.errors %}
            <div class="alert alert-danger">
                <ul>
                    {% for field, errors in assignment_form.errors.items %}
                        <li>{{ field }}: {{ errors|join:", " }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
    </div>
</div>

<div class="form-wrapper">
    <div class="form-container">
        <!-- Staff Role Assignment Section -->
        <h3>2. Staff Role Assignment</h3>
        <hr>
        <div class="form-group">
            <label for="{{ assignment_form.role.id_for_label }}">{{ assignment_form.role.label }} <span class="text-danger">:*</span></label>
            {{ assignment_form.role }}
        </div>
        <div class="form-group">
            {{ assignment_form.supervisor.label_tag }}
            {{ assignment_form.supervisor }}
        </div>

        <div class="form-group">
            {{ assignment_form.position.label_tag }}
            {{ assignment_form.position }}
        </div>

        <div class="form-group">
            {{ assignment_form.permanent_vs_temporary.label_tag }}<br>
            {% for radio in assignment_form.permanent_vs_temporary %}
                <div class="form-check form-check-inline">
                    {{ radio.tag }}
                    <label class="form-check-label">{{ radio.choice_label }}</label>
                </div>
            {% endfor %}
        </div>        

        <div class="form-group">
            <label for="{{ assignment_form.role_fte.id_for_label }}">{{ assignment_form.role_fte.label }} <span class="text-danger">:*</span></label>
            {{ assignment_form.role_fte }}
        </div>

        <div class="form-group">
            {{ assignment_form.service.label_tag }}
            {{ assignment_form.service }}
        </div>

        <div class="form-group">
            <label for="{{ assignment_form.start_date.id_for_label }}">{{ assignment_form.start_date.label }} <span class="text-danger">:*</span></label>
            {{ assignment_form.start_date }}
        </div>
    </div>
</div>

<!-- Staff Allocation Section -->
<div class="form-container">
    <h3 class="allocation-heading">3. Staff FTE Allocations for Role Assignment</h3>

    <!-- Render the formset management form -->
    {{ allocation_formset.management_form }}

    <!-- Loop through the formset to render each allocation form -->
    <div id="allocation-formset">
        {% for form in allocation_formset %}
        <div class="allocation-form-row">
            <hr>
            <div class="allocation-header">
                <div class="allocation-title">
                    <h4 class="subheading">Allocation {{ forloop.counter }}</h4>
                </div>
                {% comment %} <button type="reset" class="reset-form">Reset Form</button> {% endcomment %}
            </div>
            
            <div class="gap-separator"></div>

            <!-- Display Individual Form Errors -->
            {% if form.errors %}
                <div class="alert alert-danger">
                    <ul>
                    {% for field, errors in form.errors.items %}
                        <li>{{ field }}: {{ errors|join:", " }}</li>
                    {% endfor %}
                    </ul>
                </div>
            {% endif %}
            
            <!-- Centralized/Decentralized -->
            <div class="form-group" id="centralized_vs_ric">
                <label>{{ form.centralized_vs_ric.label }}</label><br>
                {% for radio in form.centralized_vs_ric %}
                    <div class="form-check form-check-inline">
                        {{ radio.tag }}
                        <label class="form-check-label">{{ radio.choice_label }}</label>
                    </div>
                {% endfor %}
            </div>

            <!-- Days of the week -->
            <div class="form-group">
                <label>Allocated days:</label><br>
                <div class="form-check form-check-inline">
                    {{ form.monday }} {{ form.monday.label_tag }}
                </div>
                <div class="form-check form-check-inline">
                    {{ form.tuesday }} {{ form.tuesday.label_tag }}
                </div>
                <div class="form-check form-check-inline">
                    {{ form.wednesday }} {{ form.wednesday.label_tag }}
                </div>
                <div class="form-check form-check-inline">
                    {{ form.thursday }} {{ form.thursday.label_tag }}
                </div>
                <div class="form-check form-check-inline">
                    {{ form.friday }} {{ form.friday.label_tag }}
                </div>
            </div>
            
            <!-- Clinic Field -->
            <div class="form-group" id="clinic">
                {{ form.clinic.label_tag }} 
                {{ form.clinic }}
            </div>

            <!-- Assignment in Clinic Field -->
            <div class="form-group" id="assignment_in_clinic">
                {{ form.assignment_in_clinic.label_tag }} 
                {{ form.assignment_in_clinic }}
            </div>

            <!-- Program Field -->
            <div class="form-group" id="program">
                {{ form.program.label_tag }} 
                {{ form.program }}
            </div>

            <!-- FTE -->
            <div class="form-group">
                <label for="{{ form.fte.id_for_label }}">{{ form.fte.label }} <span class="text-danger">:*</span></label>
                {{ form.fte }}
            </div>

            <!-- Start Date -->
            <div class="form-group">
                <label for="{{ form.start_date.id_for_label }}">{{ form.start_date.label }} <span class="text-danger">:*</span></label>
                {{ form.start_date }}
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Staff Location and Contact Section -->
<div class="form-wrapper">
    <div class="form-container">
        <h3>4. Staff Location & Contact Details</h3>
        <div class="alert alert-info" style="padding: 8px 12px;">
            <small><em style="color: #6c757d;">Note: Contact information entered here will populate the Staff List located in MS Teams > EOPCN All Staff Team > Lists and Operational Info.</em></small>
        </div>
        <hr>
        
        <!-- Display Errors for Location Contact Form -->
        {% if location_contact_form.errors %}
            <div class="alert alert-danger">
                <ul>
                    {% for field, errors in location_contact_form.errors.items %}
                        <li>{{ field }}: {{ errors|join:", " }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
        
        <div class="form-group">
            {{ location_contact_form.contact_type.label_tag }}
            {{ location_contact_form.contact_type }}
        </div>

        <div class="form-group">
            {{ location_contact_form.clinic.label_tag }}
            {{ location_contact_form.clinic }}
        </div>

        <div class="form-group">
            {{ location_contact_form.phone.label_tag }}
            {{ location_contact_form.phone }}
        </div>

        <div class="form-group">
            {{ location_contact_form.extension.label_tag }}
            {{ location_contact_form.extension }}
        </div>

        <div class="form-group">
            {{ location_contact_form.office_number.label_tag }}
            {{ location_contact_form.office_number }}
        </div>

        <!-- Days of the week with shortened labels -->
        <div class="form-group">
            <label>Available days:</label><br>
            <div class="form-check form-check-inline">
                {{ location_contact_form.monday }} <label class="form-check-label">Mon</label>
            </div>
            <div class="form-check form-check-inline">
                {{ location_contact_form.tuesday }} <label class="form-check-label">Tue</label>
            </div>
            <div class="form-check form-check-inline">
                {{ location_contact_form.wednesday }} <label class="form-check-label">Wed</label>
            </div>
            <div class="form-check form-check-inline">
                {{ location_contact_form.thursday }} <label class="form-check-label">Thu</label>
            </div>
            <div class="form-check form-check-inline">
                {{ location_contact_form.friday }} <label class="form-check-label">Fri</label>
            </div>
        </div>

        <div class="form-group">
            {{ location_contact_form.contact_notes.label_tag }}
            <!-- Set rows attribute to reduce vertical space -->
            <textarea name="{{ location_contact_form.contact_notes.name }}" class="form-control" rows="2">{{ location_contact_form.contact_notes.value|default_if_none:"" }}</textarea>
        </div>
    </div>
</div>

<!-- Email Group Selection -->
<div class="form-wrapper">
    <div class="form-container">
        {% with form=staff_form %}
            {% include 'staff/email_group_widget.html' %}
        {% endwith %}
    </div>
</div>

<!-- Submit and Add allocation buttons -->
{% comment %} <div class="button-container">
    <button type="button" id="add-allocation">+ Add another allocation</button>
</div> {% endcomment %}

<div class="form-wrapper">
    <div class="form-container">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <button type="submit" class="btn btn-primary mt-3">Save</button>  
            <a href="{% url 'list_staff' %}" class="btn-secondary mt-3">Back to Staff List</a>
        </div>
    </div>
</div>

</form>

<script src="{% static 'eopcn_staff/js/allocation_form_management.js' %}"></script>
{% endblock %}