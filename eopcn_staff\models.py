from django.db import models
from django.utils import timezone
from datetime import date



class Clinic(models.Model):
    clinic_id = models.AutoField(primary_key=True)
    clinic_name = models.Char<PERSON>ield(max_length=255)
    med_group_or_site = models.CharField(max_length=255, null=True, blank=True)
    street_address = models.CharField(max_length=255, null=True, blank=True)
    floor_unit_room = models.CharField(max_length=100, null=True, blank=True)
    city = models.CharField(max_length=100, null=True, blank=True)
    province = models.CharField(max_length=50, null=True, blank=True)
    postal_code = models.Char<PERSON>ield(max_length=10, null=True, blank=True)
    business_phone = models.Char<PERSON>ield(max_length=20, null=True, blank=True)
    extension = models.CharField(max_length=10, null=True, blank=True)
    fax = models.CharField(max_length=20, null=True, blank=True)
    clinic_website = models.URLField(null=True, blank=True)
    clinic_emr = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    pia_number = models.Char<PERSON>ield(max_length=50, null=True, blank=True)
    include_on_eopcn_website = models.BooleanField(default=False)
    external_member_clinic = models.BooleanField(default=False)
    eopcn_facilitator = models.CharField(max_length=255, null=True, blank=True)
    development_stage = models.CharField(max_length=100, null=True, blank=True)
    primary_contact = models.CharField(max_length=255, null=True, blank=True)
    primary_contact_role = models.CharField(max_length=100, null=True, blank=True)
    primary_contact_phone = models.CharField(max_length=20, null=True, blank=True)
    primary_contact_ext = models.CharField(max_length=10, null=True, blank=True)
    primary_contact_email = models.EmailField(null=True, blank=True)
    primary_contact_first_name = models.CharField(max_length=100, null=True, blank=True)
    primary_contact_last_name = models.CharField(max_length=100, null=True, blank=True)
    alternate_contact = models.CharField(max_length=255, null=True, blank=True)
    alternate_contact_role = models.CharField(max_length=100, null=True, blank=True)
    alternate_contact_phone = models.CharField(max_length=20, null=True, blank=True)
    alternate_contact_ext = models.CharField(max_length=10, null=True, blank=True)
    alternate_contact_email = models.EmailField(null=True, blank=True)
    alternate_contact_first_name = models.CharField(max_length=100, null=True, blank=True)
    alternate_contact_last_name = models.CharField(max_length=100, null=True, blank=True)
    date_modified = models.DateTimeField(null=True, blank=True)
    modified_by = models.CharField(max_length=100, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        managed = False  # Django will not attempt to manage this table
        db_table = 'mh_clinics'  # Exact name of the table in the database

    def __str__(self):
        return self.clinic_name
    

class Staff(models.Model):
    staff_id = models.AutoField(primary_key=True)  # Auto-incrementing primary key
    first_name = models.CharField(max_length=255)  # Assuming max length for names
    last_name = models.CharField(max_length=255)  # Assuming max length for names
    photo = models.ImageField(upload_to='staff_photos', null=True, blank=True)  # Image field
    start_date = models.DateField()
    leave_start_date = models.DateField(null=True, blank=True)  # Nullable field
    return_date = models.DateField(null=True, blank=True)  # Nullable field
    end_date = models.DateField(null=True, blank=True)  # Nullable field
    currently_active = models.BooleanField(null=True, blank=True)  # Boolean field
    desk_number = models.CharField(max_length=50, null=True, blank=True)  # Desk number field
    office_number = models.CharField(max_length=50, null=True, blank=True)  # Office number field
    computer_number = models.CharField(max_length=50, null=True, blank=True)  # Computer number field
    phone = models.CharField(max_length=20, null=True, blank=True)  # Phone field
    ext = models.CharField(max_length=10, null=True, blank=True)  # Extension field
    suggested_email = models.EmailField(max_length=255)  # Email field with max length
    date_modified = models.DateTimeField(null=True, blank=True)  # Auto-update on modification
    modified_by = models.CharField(max_length=100, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)  # Auto-set on creation
    created_by = models.CharField(max_length=255, null=True, blank=True, default='Automation Admin')
    currently_on_leave = models.BooleanField(null=True, blank=True)  # Boolean field
    n95_mask_size = models.CharField(max_length=20, null=True, blank=True)  # N95 mask size field

    class Meta:
        db_table = 'staff'
        managed = False

    def __str__(self):
        return f"{self.first_name} {self.last_name}"
    
class StaffSupervisor(models.Model):
    supervisor_id = models.AutoField(primary_key=True)
    staff = models.ForeignKey('Staff', on_delete=models.CASCADE)  # Assuming a relationship to the Staff model
    supervisor_name = models.CharField(max_length=255)
    first_name = models.CharField(max_length=255)
    last_name = models.CharField(max_length=255)
    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=255)
    currently_active = models.BooleanField(null=True, blank=True)


    class Meta:
        db_table = 'staff_supervisors'
        managed = False  # Assuming you're managing this table directly in SQL Server

    def __str__(self):
        return f"{self.staff.first_name} {self.staff.last_name}" 
    
class StaffRole(models.Model):
    role_id = models.AutoField(primary_key=True)
    role_name = models.CharField(max_length=255)
    ah_role_title = models.CharField(max_length=255)
    ah_role_category = models.CharField(max_length=255)
    date_created = models.DateTimeField(auto_now_add=True)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    created_by = models.CharField(max_length=255)

    class Meta:
        db_table = 'staff_roles'
        managed = False  # Assuming you're managing this table directly in SQL Server

    def __str__(self):
        return self.role_name

class Program(models.Model):
    program_id = models.AutoField(primary_key=True)
    program_name = models.CharField(max_length=255)
    service = models.ForeignKey('Service', on_delete=models.CASCADE, blank=True, null=True)

    class Meta:
        db_table = 'programs'
        managed = False

    def __str__(self):
        return self.program_name

class Service(models.Model):
    service_id = models.AutoField(primary_key=True)
    service_name = models.CharField(max_length=255)
    division = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'services'
        managed = False

    def __str__(self):
        return self.service_name

class Position(models.Model):
    position_id = models.AutoField(primary_key=True)
    position_number = models.CharField(max_length=255, unique=True)
    is_available = models.BooleanField(default=True)
    staff_assignment = models.ForeignKey('StaffAssignment', db_column='staff_role_id' ,on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_positions')  # Unique related_name
    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=255)
    decommissioned = models.BooleanField(default=False)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    
    class Meta:
        db_table = 'positions'
        managed = False

    def __str__(self):
        return self.position_number
    
class PositionList(models.Model):
    position_id = models.IntegerField(primary_key=True)  # Primary key field
    position_number = models.CharField(max_length=255)  # Primary key field
    staff_name = models.CharField(max_length=512)  # Concatenated first and last name
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE, db_column='staff_id', related_name='position_list')  # Link to Staff
    service_name = models.CharField(max_length=255, null=True, blank=True)
    program_names = models.TextField(null=True, blank=True)  # Aggregated program names
    role_name = models.CharField(max_length=255, null=True, blank=True)
    role_fte = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    assignment_in_clinic_types = models.TextField(null=True, blank=True)  # Aggregated assignment names
    allocation_type = models.TextField(null=True, blank=True)  # Aggregated centralized_vs_ric values
    is_available = models.BooleanField()
    active_vacancy_type = models.CharField(max_length=255, null=True, blank=True)  # New field: active vacancy type
    active_vacancy_dates = models.TextField(null=True, blank=True)  # New field: active vacancy dates
    active_covering_staff = models.TextField(null=True, blank=True)  # New field: active covering staff
    active_covering_dates = models.TextField(null=True, blank=True)  # New field: active covering dates
    position_status = models.CharField(max_length=255, null=True, blank=True)  # New field: position status
    assignment_currently_active = models.BooleanField()
    staff_currently_active = models.BooleanField()
    assignment_start_date = models.DateField(null=True, blank=True)
    assignment_end_date = models.DateField(null=True, blank=True)

    class Meta:
        db_table = 'PositionList'  # Matches the name of your SQL Server view
        managed = False  # Prevent Django from managing the database table/view

    def __str__(self):
        return f"{self.position_number} - {self.staff_name}"


class StaffAssignment(models.Model):
    staff_role_id = models.AutoField(primary_key=True)
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='assignments')
    role = models.ForeignKey(StaffRole, on_delete=models.CASCADE, null=True, blank=True)  # Optional field
    currently_active = models.BooleanField(null=True, blank=True)
    supervisor = models.ForeignKey(StaffSupervisor, on_delete=models.CASCADE, related_name='supervised_assignments', null=True, blank=True)  # Optional field
    position = models.ForeignKey(Position, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_staff')  # Unique related_name
    service = models.ForeignKey(Service, on_delete=models.CASCADE, null=True, blank=True)  # Optional field
    permanent_vs_temporary = models.CharField(max_length=255, null=True, blank=True)  # Optional field
    role_fte = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)  # Optional field
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    date_modified = models.DateTimeField(null=True, blank=True)
    modified_by = models.CharField(max_length=255, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=255, default='Automatin Admin')

    class Meta:
        db_table = 'staff_assignments'
        managed = False

    def __str__(self):
        """Human readable representation for admin and logs."""
        return f"Assignment {self.staff_role_id} for {self.staff}"


class StaffAllocation(models.Model):
    staff_allocation_id = models.AutoField(primary_key=True)
    staff_assignment = models.ForeignKey(StaffAssignment, on_delete=models.CASCADE, related_name='allocations')  # Link to StaffAssignment
    clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE, related_name='allocations', null=True, blank=True)
    program = models.ForeignKey(Program, on_delete=models.CASCADE, null=True, blank=True)
    centralized_vs_ric = models.CharField(max_length=255, null=True, blank=True)
    fte = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    allocation_type = models.CharField(max_length=255, null=True, blank=True)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    date_modified = models.DateTimeField(null=True, blank=True)
    modified_by = models.CharField(max_length=255, null=True, blank=True)  # Assuming you want a default value
    date_created = models.DateTimeField(auto_now_add=True, null=True)  # Temporarily allow null values
    created_by = models.CharField(max_length=255, default='Automation Admin')  # Default value added here
    assignment_in_clinic = models.ForeignKey('StaffAssignmentsInClinic', on_delete=models.CASCADE, null=True, blank=True)
    currently_active = models.BooleanField(null=True, blank=True)


    # New fields for days of the week
    monday = models.BooleanField(default=False)
    tuesday = models.BooleanField(default=False)
    wednesday = models.BooleanField(default=False)
    thursday = models.BooleanField(default=False)
    friday = models.BooleanField(default=False)

    class Meta:
        db_table = 'staff_allocation'
        managed = False

    def __str__(self):
        """Human readable representation for admin and logs."""
        return f"Allocation {self.staff_allocation_id} for {self.staff_assignment.staff}"

class StaffWithRole(models.Model):
    staff_id = models.IntegerField(primary_key=True)
    first_name = models.CharField(max_length=255)
    last_name = models.CharField(max_length=255)
    start_date = models.DateField(null=True)
    end_date = models.DateField(null=True)
    suggested_email = models.EmailField(null=True)
    currently_on_leave = models.BooleanField(default=False)
    currently_active = models.BooleanField(default=True)
    date_modified = models.DateField(null=True)
    modified_by = models.CharField(max_length=255)
    date_created = models.DateField(null=True)
    created_by = models.CharField(max_length=255)
    role_currently_active = models.BooleanField(default=True)
    role_name = models.CharField(max_length=255, null=True)
    role_start_date = models.DateField(null=True)
    role_end_date = models.DateField(null=True)
    permanent_vs_temporary = models.CharField(max_length=50, null=True)
    role_fte = models.DecimalField(max_digits=5, decimal_places=2, null=True)
    supervisor_id = models.IntegerField(null=True)  # Add this field
    supervisor_full_name = models.CharField(max_length=255, null=True)  # Already exists
    position_number = models.CharField(max_length=255, null=True)
    service_name = models.CharField(max_length=255, null=True)

    class Meta:
        managed = False
        db_table = 'vw_staff_with_roles'


class Physician(models.Model):
    physician_id = models.AutoField(primary_key=True)
    physician_name = models.CharField(max_length=255, null=True, blank=True)
    first_name = models.CharField(max_length=255, null=True, blank=True)  # New field
    last_name = models.CharField(max_length=255, null=True, blank=True)   # New field
    title = models.CharField(max_length=100, null=True, blank=True)
    practitioner_id = models.IntegerField(null=True, blank=True)
    physician_active = models.CharField(max_length=10, null=True, blank=True)
    scope = models.TextField(null=True, blank=True)
    date_signed_eopcn = models.DateField(null=True, blank=True)
    date_left_eopcn = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=10, null=True, blank=True)
    primary_email = models.EmailField(null=True, blank=True)
    masked_primary_email = models.EmailField(null=True, blank=True)
    alternate_email = models.EmailField(null=True, blank=True)
    masked_alternate_email = models.EmailField(null=True, blank=True)
    do_not_email = models.BooleanField(default=False)
    primary_phone = models.CharField(max_length=20, null=True, blank=True)
    alternate_phone = models.CharField(max_length=20, null=True, blank=True)
    active_in_clinic = models.CharField(max_length=10, null=True, blank=True)
    no_longer_practicing = models.CharField(max_length=10, null=True, blank=True)
    reason_for_leaving = models.TextField(null=True, blank=True)
    date_no_longer_practicing = models.DateField(null=True, blank=True)
    date_modified = models.DateTimeField(auto_now=True)
    modified_by = models.CharField(max_length=100, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=100, null=True, blank=True)
    form_id = models.CharField(max_length=50, null=True, blank=True)
    approved_by = models.CharField(max_length=100, null=True, blank=True)
    approved_date = models.DateField(null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'mh_physicians'

    def __str__(self):
        if self.first_name and self.last_name:
            return f"{self.last_name}, {self.first_name}"
        return self.physician_name


class ClinicPhysician(models.Model):
    clinics_physicians_ID = models.AutoField(primary_key=True)
    physician = models.ForeignKey(Physician, on_delete=models.CASCADE)
    clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE, related_name='physician_associations')
    clinic_name = models.CharField(max_length=255)  # Keep for backward compatibility
    portion_of_practice = models.CharField(max_length=10, null=True, blank=True)
    accepting_patients = models.CharField(max_length=10, null=True, blank=True)
    limitations = models.TextField(null=True, blank=True)
    date_active_in_clinic = models.DateField(null=True, blank=True)
    date_left_clinic = models.DateField(null=True, blank=True)
    include_on_afad_website = models.CharField(max_length=10, null=True, blank=True)
    include_on_eopcn_website = models.CharField(max_length=10, null=True, blank=True)
    date_modified = models.DateTimeField(auto_now=True)
    modified_by = models.CharField(max_length=100, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=100, null=True, blank=True)
    form_id = models.CharField(max_length=50, null=True, blank=True)
    approved_by = models.CharField(max_length=100, null=True, blank=True)
    approved_date = models.DateField(null=True, blank=True)
    
    # Add the three new fields
    CPAR_Panel_ID = models.IntegerField(null=True, blank=True)
    active_CII = models.BooleanField(null=True, blank=True)
    active_CPAR = models.BooleanField(null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'mh_clinics_physicians'

    def __str__(self):
        return f"{self.physician.physician_name} at {self.clinic.clinic_name}"
    
    def save(self, *args, **kwargs):
        # Update clinic_name from the related Clinic model
        if self.clinic:
            self.clinic_name = self.clinic.clinic_name
        super().save(*args, **kwargs)
    
class StaffLeaveTypes(models.Model):
    leave_type_id = models.AutoField(primary_key=True)
    leave_type_name = models.CharField(max_length=50, unique=True)
    date_created = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'staff_leave_types'
        managed = False

    def __str__(self):
        return self.leave_type_name


class StaffLeave(models.Model):
    leave_id = models.AutoField(db_column='leave_id', primary_key=True)
    staff = models.ForeignKey('Staff', db_column='staff_id', on_delete=models.CASCADE, related_name='leaves')
    leave_type = models.ForeignKey('StaffLeaveTypes', db_column='leave_type_id', on_delete=models.CASCADE)
    leave_start_date = models.DateField()
    return_date = models.DateField(null=True, blank=True)
    # Optional datetime for sending reminder emails about this leave
    reminder_datetime = models.DateTimeField(null=True, blank=True)
    # Indicates whether the reminder email has been sent
    reminder_sent = models.BooleanField(default=False)
    # Optional email group to receive reminder
    reminder_email_group = models.ForeignKey(
        'EmailGroup',
        db_column='reminder_email_group_id',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    # Email address for sending reminder to the user who set it
    reminder_email_address = models.EmailField(max_length=254, null=True, blank=True)
    date_created = models.DateTimeField()
    created_by = models.CharField(max_length=50)
    date_modified = models.DateTimeField()
    modified_by = models.CharField(max_length=50)

    class Meta:
        db_table = 'staff_leave'
        managed = False

    def get_reminder_recipients(self):
        """Get list of email addresses for reminders"""
        recipients = []
        if self.reminder_email_address:
            recipients.append(self.reminder_email_address)
        if self.reminder_email_group:
            recipients.extend(self.reminder_email_group.get_active_emails())
        # Remove duplicates
        return list(set(recipients)) or None

    def is_reminder_due(self):
        """Check if reminder is due to be sent"""
        if not self.reminder_datetime or self.reminder_sent:
            return False

        import pytz
        from django.utils import timezone

        mst = pytz.timezone('US/Mountain')
        now_mst = timezone.now().astimezone(mst)
        reminder_dt_mst = self.reminder_datetime.astimezone(mst)

        return reminder_dt_mst <= now_mst

    def should_send_automatic_reminders(self):
        """Determine if this leave should have automatic reminders"""
        # Only send automatic reminders for leaves that start in the future
        # and have a return date
        from datetime import date
        today = date.today()

        return (self.leave_start_date > today and
                self.return_date is not None and
                not self.reminder_datetime)  # Don't override manual reminders


class StaffAssignmentsInClinic(models.Model):
    assignment_in_clinic_id = models.AutoField(primary_key=True)
    assignment_name = models.CharField(max_length=255, unique=True)
    date_created = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'staff_assignments_in_clinic'
        managed = False  # Assuming you're managing this table directly in SQL Server

    def __str__(self):
        return self.assignment_name


class PhysicianPanelDetails(models.Model):
    physician_id = models.IntegerField(primary_key=True)
    physician_name = models.CharField(max_length=255)
    last_name = models.CharField(max_length=255)
    first_name = models.CharField(max_length=255)
    title = models.CharField(max_length=100, null=True)
    physician_active = models.BooleanField()
    date_signed_eopcn = models.DateField(null=True)
    date_left_eopcn = models.DateField(null=True)
    clinic_id = models.IntegerField()  # Added field to match the updated view
    clinic_name = models.CharField(max_length=255)
    date_active_in_clinic = models.DateField(null=True)
    date_left_clinic = models.DateField(null=True)
    portion_of_practice = models.DecimalField(max_digits=5, decimal_places=2, null=True)
    accepting_patients = models.BooleanField()
    panel_size_oct_2024 = models.IntegerField(null=True)
    multiple_primary_practices = models.BooleanField()
    alternate_without_primary = models.BooleanField()
    panel_size_report_year = models.IntegerField(null=True)
    panel_size_report_month = models.IntegerField(null=True)
    include_on_afad_website = models.CharField(max_length=10, null=True, blank=True)  # Added field
    include_on_eopcn_website = models.CharField(max_length=10, null=True, blank=True) # Added field
    CPAR_Panel_ID = models.IntegerField(null=True, blank=True)  # Added field
    active_CII = models.BooleanField(null=True, blank=True)  # Added field
    active_CPAR = models.BooleanField(null=True, blank=True)  # Added field

    class Meta:
        managed = False  # Since this is a view, not a table
        db_table = 'Physician_Panel_Details_View'


class ClinicStaffAllocation(models.Model):
    id = models.AutoField(primary_key=True)  # Synthetic primary key
    clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE, db_column='clinic_id', related_name='staff_allocations')
    clinic_name = models.CharField(max_length=255)
    active_physicians = models.IntegerField()
    staff_id = models.IntegerField(null=True, blank=True)
    first_name = models.CharField(max_length=255, null=True, blank=True)
    last_name = models.CharField(max_length=255, null=True, blank=True)
    staff_role = models.CharField(max_length=255, null=True, blank=True)
    assignment_in_clinic = models.CharField(max_length=255, null=True, blank=True)
    allocation_type = models.CharField(max_length=255, null=True, blank=True)
    fte = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    monday = models.BooleanField(default=False)
    tuesday = models.BooleanField(default=False)
    wednesday = models.BooleanField(default=False)
    thursday = models.BooleanField(default=False)
    friday = models.BooleanField(default=False)
    primary_contact = models.CharField(max_length=255, null=True, blank=True)
    primary_contact_role = models.CharField(max_length=255, null=True, blank=True)
    primary_contact_phone = models.CharField(max_length=20, null=True, blank=True)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)

    class Meta:
        managed = False
        db_table = "vw_clinic_staff_allocations"


class StaffLocationContact(models.Model):
    location_contact_id = models.AutoField(primary_key=True)
    staff = models.ForeignKey('Staff', on_delete=models.CASCADE, db_column='staff_id', related_name='location_contacts')
    clinic = models.ForeignKey('Clinic', on_delete=models.SET_NULL, null=True, db_column='clinic_id', related_name='location_contacts')
    office_number = models.CharField(max_length=255, null=True, blank=True)
    desk_number = models.CharField(max_length=255, null=True, blank=True)
    location_type = models.CharField(max_length=255, null=True, blank=True)  # E.g., "Centralized", "Decentralized"
    contact_type = models.CharField(max_length=255, null=True, blank=True)  # E.g., "Desk Phone", "Work Cell"
    phone = models.CharField(max_length=15, null=True, blank=True)
    extension = models.CharField(max_length=10, null=True, blank=True)
    monday = models.BooleanField(default=False)
    tuesday = models.BooleanField(default=False)
    wednesday = models.BooleanField(default=False)
    thursday = models.BooleanField(default=False)
    friday = models.BooleanField(default=False)
    contact_notes = models.TextField(null=True, blank=True)
    date_modified = models.DateTimeField(auto_now=True)
    modified_by = models.CharField(max_length=255, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        db_table = 'staff_location_contact'
        managed = False  # Since the table is already created in SQL Server

    def __str__(self):
        return f"Contact Info for Staff ID {self.staff_id} - {self.phone or 'No Phone'}"

class SeatingMap(models.Model):
    file = models.FileField(upload_to='seatingmap/')
    uploaded_at = models.DateTimeField(auto_now_add=True)  # Timestamp of the upload


class ClinicWithActivePhysicianCount(models.Model):
    clinic_id = models.IntegerField(primary_key=True)
    clinic_name = models.CharField(max_length=255)
    street_address = models.CharField(max_length=255, blank=True, null=True)
    floor_unit_room = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=255, blank=True, null=True)
    business_phone = models.CharField(max_length=20, blank=True, null=True)
    fax = models.CharField(max_length=20, blank=True, null=True)
    include_on_eopcn_website = models.BooleanField()
    primary_contact = models.CharField(max_length=255, blank=True, null=True)
    primary_contact_role = models.CharField(max_length=255, blank=True, null=True)
    primary_contact_phone = models.CharField(max_length=20, blank=True, null=True)
    primary_contact_email = models.EmailField(blank=True, null=True)
    clinic_website = models.URLField(blank=True, null=True)
    active_physician_count = models.IntegerField()
    last_note_date = models.DateField(null=True, blank=True)  # Last note date for the clinic

    class Meta:
        managed = False  # Django won't try to create or alter this view
        db_table = 'vw_clinics_w_active_physician_count'  # Exact name of the database view
        

class StaffCoverage(models.Model):
    staff_coverage_id = models.AutoField(primary_key=True)  # Matches the PK in SQL Server
    leave = models.ForeignKey(StaffLeave, on_delete=models.CASCADE)  # FK to StaffLeave
    covering_staff = models.ForeignKey(Staff, on_delete=models.CASCADE, null=True, blank=True, related_name='coverages')  # FK to Staff for covering staff
    coverage_type = models.CharField(max_length=50, null=True, blank=True)
    coverage_start_date = models.DateField(null=True, blank=True)
    coverage_end_date = models.DateField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=50, null=True, blank=True)
    date_modified = models.DateTimeField(auto_now=True)
    modified_by = models.CharField(max_length=50, null=True, blank=True)

    def __str__(self):
        return f"Coverage for Leave {self.leave_id} by Staff {self.covering_staff_id}"

    class Meta:
        db_table = "staff_coverage"  # Matches the existing table name in SQL Server
        managed = False  # Django won't manage migrations for this table


class DashboardStats(models.Model):
    id = models.IntegerField(primary_key=True)
    active_staff_count = models.IntegerField()
    total_fte_active_staff = models.DecimalField(max_digits=5, decimal_places=2)
    on_leave_count = models.IntegerField()
    centralized_staff_count = models.IntegerField()
    ric_staff_count = models.IntegerField()
    vacancy_count = models.IntegerField()
    active_physicians_count = models.IntegerField()
    active_clinics_count = models.IntegerField()
    latest_panel_month = models.IntegerField()
    latest_panel_year = models.IntegerField()
    total_panel_size = models.IntegerField()

    class Meta:
        managed = False  # Django will NOT create or modify this table
        db_table = "vwDashboardStats"  # Match the exact SQL Server view name


class ClinicNote(models.Model):
    note_id = models.AutoField(primary_key=True, db_column='note_ID')
    clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE, db_column='clinic_id', related_name='notes')
    clinic_name = models.CharField(max_length=255, db_column='clinic_name', null=True, blank=True)  # Store clinic name directly for optimization
    date_of_entry = models.DateTimeField()
    author_of_entry = models.CharField(max_length=255)
    note = models.TextField()
    type_of_entry = models.CharField(max_length=100, null=True, blank=True)
    date_modified = models.DateTimeField(auto_now=True)
    date_created = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = False  # Table is managed outside of Django
        db_table = 'mh_clinic_notes'
        ordering = ['-date_of_entry']

    def __str__(self):
        clinic_name = self.clinic.clinic_name if self.clinic else self.clinic_name
        date_str = self.date_of_entry.strftime('%Y-%m-%d') if self.date_of_entry else 'No Date'
        return f"Note for {clinic_name} on {date_str}"

class ClinicNotePhysicianAttendance(models.Model):
    """Model to track which physicians were in attendance for a clinic note"""
    attendance_id = models.AutoField(primary_key=True)
    clinic_note = models.ForeignKey('ClinicNote', on_delete=models.CASCADE, related_name='physician_attendance')
    physician = models.ForeignKey('Physician', on_delete=models.CASCADE)
    date_created = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        managed = False  # Table is managed outside of Django
        db_table = 'mh_clinic_note_physician_attendance'
        unique_together = ('clinic_note', 'physician')  # Prevent duplicate entries
    
    def __str__(self):
        return f"{self.physician.physician_name} - Note {self.clinic_note.note_id}"

class PhysicianNameMapping(models.Model):
    physician_id = models.IntegerField(null=True, blank=True)
    ah_full_name = models.CharField(max_length=255, db_column='AH_Full_Name')
    eopcn_full_name = models.CharField(max_length=255, db_column='EOPCN_full_name')
    date_modified = models.DateTimeField(auto_now=True)
    date_created = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = False  # Django won't manage this table
        db_table = 'AH_physician_name_mapping'
        # Use composite primary key since there's no single primary key
        unique_together = ('physician_id', 'ah_full_name')

    def __str__(self):
        return f"{self.ah_full_name} → {self.eopcn_full_name}"


# Email Management Models
class EmailRecipient(models.Model):
    recipient_id = models.AutoField(primary_key=True)
    email = models.EmailField(max_length=255)
    name = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=100, null=True, blank=True)
    date_modified = models.DateTimeField(null=True, blank=True)
    modified_by = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'email_recipients'

    def __str__(self):
        return f"{self.name} ({self.email})"


class EmailGroup(models.Model):
    group_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=100, null=True, blank=True)
    date_modified = models.DateTimeField(null=True, blank=True)
    modified_by = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'email_groups'

    def __str__(self):
        return self.name

    def get_active_recipients(self):
        """Get all active recipients in this group"""
        return EmailRecipient.objects.filter(
            emailgroupmembership__group=self,
            is_active=True
        ).order_by('name')

    def get_active_emails(self):
        """Get list of active email addresses in this group"""
        return list(self.get_active_recipients().values_list('email', flat=True))


class EmailGroupMembership(models.Model):
    membership_id = models.AutoField(primary_key=True)
    group = models.ForeignKey(EmailGroup, on_delete=models.CASCADE)
    recipient = models.ForeignKey(EmailRecipient, on_delete=models.CASCADE)
    date_added = models.DateTimeField(auto_now_add=True)
    added_by = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'email_group_memberships'
        unique_together = ('group', 'recipient')

    def __str__(self):
        return f"{self.recipient.name} in {self.group.name}"


# Enhanced Leave Reminder Models
class LeaveReminderType(models.Model):
    """Define different types of leave reminders"""
    REMINDER_TYPES = [
        ('before_leave', 'Before Leave Starts'),
        ('during_leave', 'During Leave'),
        ('before_return', 'Before Return Date'),
        ('overdue_return', 'Overdue Return'),
        ('custom', 'Custom Reminder'),
    ]

    type_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)
    reminder_type = models.CharField(max_length=20, choices=REMINDER_TYPES)
    days_offset = models.IntegerField(help_text="Days before/after leave start (negative for before, positive for after)")
    is_active = models.BooleanField(default=True)
    description = models.TextField(null=True, blank=True)

    class Meta:
        db_table = 'leave_reminder_types'

    def __str__(self):
        return f"{self.name} ({self.get_reminder_type_display()})"


class LeaveReminder(models.Model):
    """Track individual reminders for staff leaves"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    reminder_id = models.AutoField(primary_key=True)
    staff_leave = models.ForeignKey(StaffLeave, on_delete=models.CASCADE, related_name='reminders')
    reminder_type = models.ForeignKey(LeaveReminderType, on_delete=models.CASCADE)
    scheduled_datetime = models.DateTimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    sent_datetime = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(null=True, blank=True)
    recipients = models.TextField(help_text="JSON list of email addresses")

    # Override email settings for this specific reminder
    custom_email_group = models.ForeignKey(EmailGroup, on_delete=models.SET_NULL, null=True, blank=True)
    custom_email_address = models.EmailField(max_length=254, null=True, blank=True)

    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=50, null=True, blank=True)

    class Meta:
        db_table = 'leave_reminders'
        unique_together = ('staff_leave', 'reminder_type')

    def __str__(self):
        return f"Reminder for {self.staff_leave.staff.first_name} {self.staff_leave.staff.last_name} - {self.reminder_type.name}"

    def get_recipients(self):
        """Get list of email addresses for this reminder"""
        import json

        recipients = []

        # Use custom settings if provided
        if self.custom_email_address:
            recipients.append(self.custom_email_address)
        if self.custom_email_group:
            recipients.extend(self.custom_email_group.get_active_emails())

        # Fall back to leave-level settings
        if not recipients:
            recipients = self.staff_leave.get_reminder_recipients() or []

        # Remove duplicates
        return list(set(recipients))

    def is_due(self):
        """Check if this reminder is due to be sent"""
        if self.status != 'pending':
            return False

        from django.utils import timezone
        import pytz

        mst = pytz.timezone('US/Mountain')
        now_mst = timezone.now().astimezone(mst)
        scheduled_dt_mst = self.scheduled_datetime.astimezone(mst)

        return scheduled_dt_mst <= now_mst

class FourCutPanelMaster(models.Model):
    physician_panel_id = models.AutoField(primary_key=True)
    physician_id = models.IntegerField(null=True, blank=True)
    physician_name = models.CharField(max_length=255)
    panel_size = models.IntegerField(null=True, blank=True)
    report_month = models.IntegerField(null=True, blank=True)
    report_year = models.IntegerField(null=True, blank=True)
    date_created = models.DateTimeField(null=True, blank=True)

    class Meta:
        managed = False
        db_table = "AH_physician_fourcut_panel_MASTER"

    def __str__(self):
        return f"{self.physician_name} ({self.report_month}/{self.report_year})"

class PhysicianLanguage(models.Model):
    physicians_languages_id = models.AutoField(primary_key=True)
    physician = models.ForeignKey(Physician, on_delete=models.CASCADE, related_name='languages')
    physician_name = models.CharField(max_length=255, null=True, blank=True)
    language = models.CharField(max_length=100)
    physician_Active = models.CharField(max_length=10, null=True, blank=True)
    date_modified = models.DateTimeField(auto_now=True)
    modified_by = models.CharField(max_length=100, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'physicians_languages'

    def __str__(self):
        return f"{self.physician.physician_name} - {self.language}"

    def save(self, *args, **kwargs):
        # Update physician_name from the related Physician model
        if self.physician:
            self.physician_name = self.physician.physician_name
        super().save(*args, **kwargs)
