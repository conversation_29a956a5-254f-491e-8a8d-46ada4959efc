{% extends "base.html" %}

{% block title %}{{ clinic.clinic_name }} Details{% endblock %}

{% block content %}
{% load custom_filters %}
<!-- Remove this line to avoid the conflict -->
<!-- {% load humanize %} -->

<style>
  /* Container that wraps the report */
  .detail-container { 
      max-width: 1400px; /* Increased from 1200px */
      margin: 20px auto; 
      background: #fff; 
      padding: 20px; 
      border: 1px solid #ddd; 
      border-radius: 5px; 
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  /* Header styling */
  .detail-header {
      background-color: #0067b1;
      color: white;
      padding: 25px 15px;
      border-radius: 5px 5px 0 0;
      margin: -20px -20px 20px -20px;
  }
  
  .clinic-name {
      font-size: 1.75rem;
      font-weight: 600;
      margin: 0;
      text-align: center;
  }
  
  /* Navigation header */
  .navigation-header {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.08);
  }
  
  /* Button styling */
  .button-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 10px;
  }
  
  .left-buttons, .right-buttons {
      flex: 0 0 auto;
  }
  
  .center-dropdown {
      flex: 1;
      display: flex;
      justify-content: center;
  }
  
  .button {
      display: inline-block;
      padding: 8px 15px;
      font-weight: 500;
      text-decoration: none;
      text-align: center;
      white-space: nowrap;
      border: 1px solid transparent;
      border-radius: 4px;
      transition: all 0.15s ease-in-out;
      cursor: pointer;
  }
  
  .button-primary {
      background-color: #0067b1;
      color: white;
  }
  
  .button-primary:hover {
      background-color: #005091;
      color: white !important;
      text-decoration: none;
  }
  
  .button-secondary {
      background-color: #6c757d;
      color: white;
  }
  
  .button-secondary:hover {
      background-color: #5a6268;
      color: white !important;
      text-decoration: none;
  }
  
  .button-sm {
      padding: 5px 10px;
      font-size: 0.85em;
  }
  
  #clinic_selector {
      padding: 8px 12px;
      font-size: 1rem;
      border-radius: 4px;
      border: 1px solid #ced4da;
      min-width: 300px;
      background-color: white;
  }
  
  /* Section styling */
  .detail-section {
      margin-bottom: 35px; /* Increased from 20px */
      padding: 20px; /* Added padding */
      background-color: #fafafa; /* Light background */
      border-radius: 8px; /* Rounded corners */
      border: 1px solid #e9ecef; /* Subtle border */
  }
  
  .detail-section:last-child {
      margin-bottom: 20px; /* Less margin for last section */
  }
  
  .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px; /* Increased from 15px */
      padding-bottom: 10px; /* Added padding */
      border-bottom: 2px solid #dee2e6; /* Stronger border */
  }
  
  .section-title {
      font-size: 1.35em; /* Increased from 1.25em */
      color: #0067b1; /* Match header color */
      font-weight: 600; /* Bolder */
      margin-bottom: 0;
      flex-grow: 1;
      border-bottom: none; /* Remove individual border */
      padding-bottom: 0; /* Remove individual padding */
  }
  
  .section-action {
      padding: 5px 10px;
      font-size: 0.85em;
      background-color: #80caf0;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      margin-left: 15px;
  }
  
  .section-action:hover {
      background-color: #015081;
      color: white !important;
      text-decoration: none;
  }
  
  /* Info grid styling */
  .info-grid {
      display: grid;
      grid-template-columns: 200px auto 200px auto;
      gap: 15px 25px; /* Increased gaps */
      align-items: center;
      margin-top: 15px; /* Increased from 10px */
      background-color: white; /* White background */
      padding: 15px;
      border-radius: 6px;
      border: 1px solid #e9ecef;
  }
  
  .info-row {
      display: contents;
  }
  
  .info-label {
      font-weight: bold;
      color: #495057;
  }
  
  .info-value {
      color: #212529;
  }
  
  /* Single column info for address */
  .info-single {
      display: flex;
      margin-bottom: 15px; /* Increased from 10px */
      align-items: flex-start;
      background-color: white; /* White background */
      padding: 15px;
      border-radius: 6px;
      border: 1px solid #e9ecef;
  }
  
  .info-single .info-label {
      width: 200px;
      flex-shrink: 0;
  }
  
  .info-single .info-value {
      flex: 1;
  }
  
  /* Table styling */
  .table-responsive {
      margin-top: 20px; /* Increased from 15px */
      background-color: white; /* White background for tables */
      border-radius: 6px;
      border: 1px solid #dee2e6;
      overflow: hidden;
  }
  
  .data-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 0;
  }
  
  .data-table th, 
  .data-table td {
      padding: 12px 10px; /* Increased horizontal padding */
      border-top: 1px solid #dee2e6;
      text-align: left;
      vertical-align: top;
  }
  
  .data-table th {
      font-weight: 600; /* Increased from 500 */
      color: #495057;
      border-bottom: 2px solid #dee2e6;
      background-color: #f8f9fa;
      font-size: 0.9em;
  }
  
  .data-table tr:nth-child(even) {
      background-color: #f8f9fa;
  }
  
  .data-table tr:hover {
      background-color: #e8f4f8; /* Slightly blue hover */
  }
  
  /* Toggle controls */
  .toggle-container {
      display: flex;
      align-items: center;
      margin-left: 15px;
  }
  
  .toggle-container input[type="checkbox"] {
      margin-right: 8px;
  }
  
  .toggle-container label {
      font-size: 0.85em;
      color: #6c757d;
      font-weight: normal;
  }
  
  /* Inactive item styling */
  .inactive-row {
      opacity: 0.65;
  }
  
  /* Alert messages */
  .messages-container {
      margin-bottom: 20px;
  }
  
  .alert {
      position: relative;
      padding: 10px 15px;
      margin-bottom: 10px;
      border-radius: 4px;
      border: 1px solid transparent;
  }
  
  .alert-success {
      background-color: #d4edda;
      border-color: #c3e6cb;
      color: #155724;
  }
  
  .alert-warning {
      background-color: #fff3cd;
      border-color: #ffeeba;
      color: #856404;
  }
  
  .alert-error, .alert-danger {
      background-color: #f8d7da;
      border-color: #f5c6cb;
      color: #721c24;
  }
  
  .alert-info {
      background-color: #d1ecf1;
      border-color: #bee5eb;
      color: #0c5460;
  }
  
  .alert-dismissible .close {
      position: absolute;
      top: 0;
      right: 0;
      padding: 10px 15px;
      color: inherit;
      background: none;
      border: 0;
      cursor: pointer;
  }
  
  /* Note content styling */
  .note-content {
      white-space: pre-wrap;
      line-height: 1.4;
      max-width: 400px; /* Increased from 300px */
      word-wrap: break-word;
      font-size: 0.9em;
      padding: 8px;
      background-color: #f8f9fa;
      border-radius: 4px;
      border: 1px solid #e9ecef;
  }
  
  .note-content p {
      margin: 0 0 0.5rem 0;
  }
  
  .note-content p:last-child {
      margin-bottom: 0;
  }
  
  /* Attendees styling */
  .physicians-present {
      margin: 5px 0;
      padding-left: 20px;
      list-style-type: disc;
  }
  
  .other-attendees {
      font-family: inherit;
      white-space: pre-wrap;
      margin: 5px 0;
      padding: 0;
      font-size: 0.9em;
      background: transparent;
      border: none;
  }
  
  /* Links styling */
  a {
      color: #0067b1;
      text-decoration: none;
  }

  a:hover {
      color: #004d84;
      text-decoration: underline;
  }

  /* DataTables custom layout styling */
  .dataTables_wrapper .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
  }

  .dataTables_wrapper .bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
  }

  .dataTables_filter {
      float: none !important;
      text-align: left !important;
  }

  .dataTables_length {
      float: none !important;
      text-align: right !important;
  }

  .dataTables_buttons {
      float: none !important;
  }

  .dataTables_info {
      float: none !important;
      text-align: center !important;
  }

  .dataTables_paginate {
      float: none !important;
      text-align: right !important;
  }
  
  /* Responsive adjustments */
  @media (max-width: 992px) {
      .center-dropdown {
          flex-basis: 100%;
          order: -1;
          margin-bottom: 1rem;
      }
      
      .button-container {
          flex-wrap: wrap;
      }
      
      .info-grid {
          grid-template-columns: 150px auto;
          gap: 8px 15px;
      }
  }
  
  @media (max-width: 768px) {
      .detail-container {
          margin: 10px;
          padding: 15px;
      }
      
      .info-grid {
          grid-template-columns: 1fr;
          gap: 8px;
      }
      
      .info-single {
          flex-direction: column;
      }
      
      .info-single .info-label {
          width: 100%;
          margin-bottom: 5px;
      }
      
      .data-table {
          font-size: 0.85em;
      }
  }
  
  /* Contact section specific styling */
  .contact-subsection {
      background-color: white;
      padding: 15px;
      border-radius: 6px;
      border: 1px solid #e9ecef;
      margin-bottom: 15px;
  }
  
  .contact-subsection:last-child {
      margin-bottom: 0;
  }
  
  .contact-subsection h4 {
      margin-top: 0;
      margin-bottom: 15px;
      font-size: 1.1em;
      color: #0067b1;
      border-bottom: 1px solid #dee2e6;
      padding-bottom: 8px;
  }
</style>

<div class="detail-container">
    <!-- Display alert messages -->
    {% if messages %}
        <div class="messages-container">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Navigation header -->
    <div class="navigation-header">
        <div class="button-container">
            <!-- Left-aligned button -->
            <div class="left-buttons">
                <a href="{% url 'clinic_list' %}" class="button button-secondary">Back to Clinic List</a>
            </div>

            <!-- Centered dropdown -->
            <div class="center-dropdown">
                <form method="get" id="clinicDetailForm" style="display: inline;">
                    <label for="clinic_selector" class="sr-only">Jump to Clinic:</label>
                    <select name="clinic_selector" id="clinic_selector" onchange="redirectToClinicDetail()">
                        <option value="" selected disabled>--Select Clinic--</option>
                        {% for clinic_item in all_clinics %}
                            <option value="{{ clinic_item.clinic_id }}" {% if clinic_item.clinic_id == clinic.clinic_id %}selected{% endif %}>{{ clinic_item.clinic_name }}</option>
                        {% endfor %}
                    </select>
                </form>
            </div>

            <!-- Right-aligned navigation buttons -->
            <div class="right-buttons">
                {% if previous_clinic %}
                    <a href="{% url 'clinic_detail' previous_clinic.clinic_id %}" class="button button-secondary button-sm">&lt; Previous</a>
                {% endif %}
                
                {% if next_clinic %}
                    <a href="{% url 'clinic_detail' next_clinic.clinic_id %}" class="button button-secondary button-sm">Next &gt;</a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Clinic header -->
    <div class="detail-header">
        <h1 class="clinic-name">{{ clinic.clinic_name }}</h1>
    </div>

    <!-- Clinic Information -->
    <div class="detail-section">
        <div class="section-header">
            <div class="section-title">Clinic Information</div>
            <a href="{% url 'edit_clinic' clinic.clinic_id %}" class="section-action">Edit Clinic</a>
        </div>
        
        <div class="info-grid">
            <div class="info-row">
                <div class="info-label">Medical Group/Site:</div>
                <div class="info-value">{{ clinic.med_group_or_site|default_if_none:"" }}</div>
                <div class="info-label">Phone:</div>
                <div class="info-value">{{ clinic.business_phone|phone_format|default_if_none:"" }}{% if clinic.extension %} ext. {{ clinic.extension }}{% endif %}</div>
            </div>
            
            <div class="info-row">
                <div class="info-label">EMR System:</div>
                <div class="info-value">{{ clinic.clinic_emr|default_if_none:"" }}</div>
                <div class="info-label">Fax:</div>
                <div class="info-value">{{ clinic.fax|phone_format|default_if_none:"" }}</div>
            </div>
            
            <div class="info-row">
                <div class="info-label">PIA Number:</div>
                <div class="info-value">{{ clinic.pia_number|default_if_none:"" }}</div>
                <div class="info-label">Include on EOPCN Website:</div>
                <div class="info-value">{{ clinic.include_on_eopcn_website|yesno:"Yes,No" }}</div>
            </div>
        </div>
        
        <div class="info-single">
            <div class="info-label">Address:</div>
            <div class="info-value">
                {{ clinic.street_address|default_if_none:"" }}<br>
                {% if clinic.floor_unit_room %}{{ clinic.floor_unit_room }}<br>{% endif %}
                {{ clinic.city|default_if_none:"" }}, {{ clinic.province|default_if_none:"" }} {{ clinic.postal_code|default_if_none:"" }}
            </div>
        </div>
        
        <div class="info-single">
            <div class="info-label">Website:</div>
            <div class="info-value">
                {% if clinic.clinic_website %}
                    <a href="{{ clinic.clinic_website }}" target="_blank">{{ clinic.clinic_website }}</a>
                {% else %}
                    Not provided
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="detail-section">
        <div class="section-header">
            <div class="section-title">Contact Information</div>
        </div>
        
        <div class="contact-subsection">
            <h4>Primary Contact</h4>
            <div class="info-grid" style="background: none; border: none; padding: 0;">
                <div class="info-row">
                    <div class="info-label">Name:</div>
                    <div class="info-value">{{ clinic.primary_contact_first_name|default_if_none:"" }} {{ clinic.primary_contact_last_name|default_if_none:"" }}</div>
                    <div class="info-label">Role:</div>
                    <div class="info-value">{{ clinic.primary_contact_role|default_if_none:"" }}</div>
                </div>
                
                <div class="info-row">
                    <div class="info-label">Phone:</div>
                    <div class="info-value">{{ clinic.primary_contact_phone|phone_format|default_if_none:"" }}{% if clinic.primary_contact_ext %} ext. {{ clinic.primary_contact_ext }}{% endif %}</div>
                    <div class="info-label">Email:</div>
                    <div class="info-value">{{ clinic.primary_contact_email|default_if_none:"" }}</div>
                </div>
            </div>
        </div>
        
        {% if clinic.alternate_contact_first_name or clinic.alternate_contact_last_name %}
        <div class="contact-subsection">
            <h4>Alternate Contact</h4>
            <div class="info-grid" style="background: none; border: none; padding: 0;">
                <div class="info-row">
                    <div class="info-label">Name:</div>
                    <div class="info-value">{{ clinic.alternate_contact_first_name|default_if_none:"" }} {{ clinic.alternate_contact_last_name|default_if_none:"" }}</div>
                    <div class="info-label">Role:</div>
                    <div class="info-value">{{ clinic.alternate_contact_role|default_if_none:"" }}</div>
                </div>
                
                <div class="info-row">
                    <div class="info-label">Phone:</div>
                    <div class="info-value">{{ clinic.alternate_contact_phone|phone_format|default_if_none:"" }}{% if clinic.alternate_contact_ext %} ext. {{ clinic.alternate_contact_ext }}{% endif %}</div>
                    <div class="info-label">Email:</div>
                    <div class="info-value">{{ clinic.alternate_contact_email|default_if_none:"" }}</div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Primary Care Providers -->
    <div class="detail-section">
        <div class="section-header">
            <div class="section-title">Primary Care Providers ({{ clinic_physicians|count_active:'date_left_clinic' }} active)</div>
            <div class="toggle-container">
                <input type="checkbox" id="toggleActivePhysicians" checked>
                <label for="toggleActivePhysicians">Show only active providers</label>
            </div>
        </div>
        {% if clinic_physicians %}
        <div class="table-responsive">
            <table class="data-table physician-table">
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>Name</th>
                        <th>Primary Email</th>
                        <th>Primary Phone</th>
                        <th>Panel Size</th>
                        <th>Report Date</th>
                        <th>Portion of Practice</th>
                        <th>Accepting Patients</th>
                        <th>Date Active in Clinic</th>
                        <th>Date Left Clinic</th>
                    </tr>
                </thead>
                <tbody>
                    {% for cp in clinic_physicians %}
                    <tr class="{% if cp.date_left_clinic %}inactive-row physician-inactive{% endif %}">
                        <td>{{ cp.physician.title|default_if_none:"" }}</td>
                        <td><a href="{% url 'physician_detail' cp.physician.physician_id %}">{{ cp.physician.last_name }}, {{ cp.physician.first_name }}</a></td>
                        <td>{{ cp.physician.primary_email|default_if_none:"" }}</td>
                        <td>{{ cp.physician.primary_phone|phone_format|default_if_none:"" }}</td>
                        <td>{{ cp.panel_size_oct_2024|intcomma|default_if_none:"" }}</td>
                        <td>
                            {% if cp.panel_size_report_month or cp.panel_size_report_year %}
                                {% if cp.panel_size_report_month %}{{ cp.panel_size_report_month|month_abbr }}{% endif %}{% if cp.panel_size_report_year %}, {{ cp.panel_size_report_year }}{% endif %}
                            {% endif %}
                        </td>
                        <td>{{ cp.portion_of_practice|default_if_none:"" }}</td>
                        <td>{{ cp.accepting_patients|default_if_none:"" }}</td>
                        <td>{{ cp.date_active_in_clinic|date:"M d, Y"|default_if_none:"" }}</td>
                        <td>{{ cp.date_left_clinic|date:"M d, Y"|default_if_none:"" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p>No physicians associated with this clinic.</p>
        {% endif %}
    </div>

    <!-- Staff -->
    <div class="detail-section">
        <div class="section-header">
            <div class="section-title">Staff ({{ clinic_staff|filter_valid_staff|count_active:'end_date' }} active)</div>
            <div class="toggle-container">
                <input type="checkbox" id="toggleActiveStaff" checked>
                <label for="toggleActiveStaff">Show only active staff</label>
            </div>
        </div>
        {% with valid_staff=clinic_staff|filter_valid_staff %}
        {% if valid_staff %}
        <div class="table-responsive">
            <table class="data-table staff-table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Role</th>
                        <th>Assignment In Clinic</th>
                        <th>Allocation Delivery</th>
                        <th>FTE</th>
                        <th>Schedule</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                    </tr>
                </thead>
                <tbody>
                    {% for staff in valid_staff %}
                    <tr class="{% if staff.end_date %}inactive-row staff-inactive{% endif %}">
                        <td>{% if staff.staff_id and staff.first_name and staff.last_name %}<a href="{% url 'staff_detail' staff.staff_id %}">{{ staff.first_name }} {{ staff.last_name }}</a>{% else %}N/A{% endif %}</td>
                        <td>{{ staff.staff_role|default_if_none:"" }}</td>
                        <td>{{ staff.assignment_in_clinic|default_if_none:"" }}</td>
                        <td>{{ staff.allocation_type|default_if_none:"" }}</td>
                        <td>{{ staff.fte|default_if_none:"" }}</td>
                        <td>
                            {% if staff.monday %}Mon {% endif %}
                            {% if staff.tuesday %}Tue {% endif %}
                            {% if staff.wednesday %}Wed {% endif %}
                            {% if staff.thursday %}Thu {% endif %}
                            {% if staff.friday %}Fri{% endif %}
                        </td>
                        <td>{{ staff.start_date|date:"M d, Y"|default_if_none:"" }}</td>
                        <td>{{ staff.end_date|date:"M d, Y"|default_if_none:"" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p>No staff allocated to this clinic.</p>
        {% endif %}
        {% endwith %}
    </div>

    <!-- Clinic Notes -->
    <div class="detail-section">
        <div class="section-header">
            <div class="section-title">All Clinic Notes
            {% if clinic_notes %}
                ({{ clinic_notes|length }})
            {% else %}
                (0)
            {% endif %}
            </div>
            <a href="{% url 'add_clinic_note' clinic.clinic_id %}" class="section-action">Add Clinic Note</a>
        </div>
        {% if clinic_notes %}
        <div class="table-responsive">
            <table class="data-table note-table" id="pcndatatable">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Author</th>
                        <th>Type</th>
                        <th>Attendees</th>
                        <th>Note Content</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for note in clinic_notes %}
                    <tr>
                        <td data-order="{{ note.date_of_entry|date:"Y-m-d" }}">
                        {% if note.date_of_entry|default_if_none:"" %}
                            {% if note.date_of_entry|stringformat:"s"|slice:":10" == note.date_of_entry|date:"Y-m-d" %}
                                {{ note.date_of_entry|date:"M d, Y" }}
                            {% else %}
                                {{ note.date_of_entry|date:"M d, Y" }}
                                {% if note.date_of_entry.hour|default_if_none:"" %}
                                    {{ note.date_of_entry|time:"H:i" }}
                                {% endif %}
                            {% endif %}
                        {% endif %}
                        </td>
                        <td>{{ note.author_of_entry }}</td>
                        <td>{{ note.type_of_entry|default_if_none:"" }}</td>
                        <td>
                            {% if note.note_id %}
                                <strong>Primary Care Providers:</strong>
                                {% if note.physician_attendances %}
                                    <ul class="physicians-present">
                                        {% for attendance in note.physician_attendances %}
                                            <li>{{ attendance.physician.last_name }}, {{ attendance.physician.first_name }}</li>
                                        {% endfor %}
                                    </ul>
                                {% else %}
                                    <span style="color: #6c757d;">None</span>
                                {% endif %}
                                
                                {% if note.other_attendees %}
                                    <strong>Other Attendees:</strong>
                                    <pre class="other-attendees">{{ note.other_attendees }}</pre>
                                {% endif %}
                            {% endif %}
                        </td>
                        <td class="note-content">{{ note.note|linebreaksbr }}</td>
                        <td>
                            {% if note.note_id %}
                                <a href="{% url 'edit_clinic_note' note.note_id %}" class="button button-secondary button-sm">Edit</a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p>No notes have been added for this clinic.</p>
        {% endif %}
    </div>
</div>

{% load static %}
<script src="{% static 'eopcn_staff/js/datatables.js' %}"></script>
<script>
    // Override DataTables configuration for clinic notes table to sort by date descending
    $(document).ready(function() {
        // Destroy the existing DataTable if it exists and reinitialize with custom settings
        if ($.fn.DataTable.isDataTable('#pcndatatable')) {
            $('#pcndatatable').DataTable().destroy();
        }

        // Initialize DataTable with custom settings for clinic notes
        $('#pcndatatable').DataTable({
            paging: true,
            searching: true,
            ordering: true,
            pageLength: 10, // Show 10 notes per page
            order: [[0, 'desc']], // Sort by first column (Date) in descending order

            // Custom DOM layout: search top-left, length top-right, export buttons bottom-left, pagination bottom-right
            dom: '<"top"<"dataTables_filter"f><"dataTables_length"l>><"clear">rt<"bottom"<"dataTables_buttons"B><"dataTables_info"i><"dataTables_paginate"p>><"clear">',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],

            // Enable fixed header
            fixedHeader: true
        });
    });

    // Toggle functionality for active/inactive primary care providers
    document.getElementById('toggleActivePhysicians').addEventListener('change', function() {
        const showActive = this.checked;

        // Toggle individual inactive primary care provider rows based on date_left_clinic
        const inactivePhysicianRows = document.querySelectorAll('tr.physician-inactive');
        inactivePhysicianRows.forEach(row => {
            row.style.display = showActive ? 'none' : '';
        });
    });

    // Toggle functionality for active/inactive staff
    document.getElementById('toggleActiveStaff').addEventListener('change', function() {
        const showActive = this.checked;

        // Toggle individual inactive staff rows based on end_date
        const inactiveStaffRows = document.querySelectorAll('tr.staff-inactive');
        inactiveStaffRows.forEach(row => {
            row.style.display = showActive ? 'none' : '';
        });
    });

    // Trigger the toggles on page load to hide inactive primary care providers and staff
    document.getElementById('toggleActivePhysicians').dispatchEvent(new Event('change'));
    document.getElementById('toggleActiveStaff').dispatchEvent(new Event('change'));

    // Add event listener for alert dismissal
    document.addEventListener('DOMContentLoaded', function() {
        var closeButtons = document.querySelectorAll('.alert .close');
        closeButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                this.parentElement.style.display = 'none';
            });
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 1s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.style.display = 'none';
                }, 1000);
            });
        }, 5000);
    });

    function redirectToClinicDetail() {
        const clinicId = document.getElementById('clinic_selector').value;
        if (clinicId) {
            const url = `{% url 'clinic_detail' 0 %}`.replace('0', clinicId);
            window.location.href = url;
        }
    }
</script>
{% endblock %}