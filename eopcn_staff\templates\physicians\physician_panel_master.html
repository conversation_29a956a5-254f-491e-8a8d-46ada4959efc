{% extends "base.html" %}

{% block title %}Panel Master{% endblock %}

{% block content %}
<style>
body {
  font-family: arial, sans-serif;
  margin: 0;
  padding: 0;
}

.column {
  float: left;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.row::after {
  content: "";
  clear: both;
  display: table;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #dddddd;
}

h2 {
  text-align: center;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 4px;
}

.button1 {
    background-color: #008CBA;
}

.button:hover {
    background-color: #006e92;
}

.link-button {
    display: inline-block;
    padding: 5px 10px;
    font-size: 12px;
    background-color: #73b3f8;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    margin-right: 5px;
}

.link-button:hover {
    background-color: #0056b3;
}

.dataTables_filter, .dataTables_length {
    padding-bottom: 10px;
}

.dataTables_filter {
    float: left !important;
    text-align: left !important;
}

.dataTables_length {
    float: right !important;
    text-align: right !important;
}

@media screen and (max-width: 650px) {
  .column {
    width: 100%;
    display: block;
  }
}

.green-button {
  background-color: #5dbea3;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;
  display: inline-block;
}

.green-button:hover {
  background-color: darkgreen;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>

<h2>Panel Master</h2>

<div class="filter-row">
    <label>
        <input type="checkbox" id="toggleLatest" checked data-latest-year="{{ latest_year }}" data-latest-month="{{ latest_month }}"/> Show only latest report period
    </label>
</div>

<div class="row">
  <div class="column">
    <table id="pcndatatable" class="display">
      <thead>
        <tr>
            <th>Physician Name</th>
            <th>Panel Size</th>
            <th>Report Month</th>
            <th>Report Year</th>
        </tr>
    </thead>
    <tbody>
        {% for panel in panels %}
        <tr>
            <td><a href="{% url 'physician_detail' panel.physician_id %}">{{ panel.physician_name }}</a></td>
            <td>{{ panel.panel_size|default_if_none:"" }}</td>
            <td>{{ panel.report_month|default_if_none:"" }}</td>
            <td>{{ panel.report_year|default_if_none:"" }}</td>
        </tr>
        {% endfor %}
    </tbody>
    </table>
  </div>
</div>

{% load static %}
<script src="{% static 'eopcn_staff/js/datatables.js' %}"></script>

{% endblock %}
