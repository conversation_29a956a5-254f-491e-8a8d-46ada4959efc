{% extends "base.html" %}

{% block title %}Edit Physician Name Mapping{% endblock %}

{% block content %}
<style>
body {
  font-family: arial, sans-serif;
  margin: 20px;
  padding: 0;
}

.container {
  max-width: 600px;
  margin: auto;
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

h2 {
  text-align: center;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

input[type="text"], select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

.button {
  background-color: #4CAF50;
  color: white;
  padding: 10px 15px;
  text-decoration: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}

.button:hover {
  background-color: #45a049;
}

.button.cancel {
  background-color: #6c757d;
}

.button.cancel:hover {
  background-color: #545b62;
}

.button.delete {
  background-color: #dc3545;
}

.button.delete:hover {
  background-color: #c82333;
}

.help-text {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.info-section {
  background-color: #e9ecef;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}
</style>

<div class="container">
    <h2>Edit Physician Name Mapping</h2>
    
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }}">{{ message }}</div>
        {% endfor %}
    {% endif %}
    
    <form method="post">
        {% csrf_token %}
        
        <div class="form-group">
            <label for="{{ form.physician_id.id_for_label }}">{{ form.physician_id.label }}:</label>
            {{ form.physician_id }}
            <div class="help-text">{{ form.physician_id.help_text }}</div>
            {% if form.physician_id.errors %}
                <div class="error">{{ form.physician_id.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.eopcn_full_name.id_for_label }}">{{ form.eopcn_full_name.label }}:</label>
            {{ form.eopcn_full_name }}
            <div class="help-text">{{ form.eopcn_full_name.help_text }}</div>
            {% if form.eopcn_full_name.errors %}
                <div class="error">{{ form.eopcn_full_name.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.ah_full_name.id_for_label }}">{{ form.ah_full_name.label }}:</label>
            {{ form.ah_full_name }}
            <div class="help-text">{{ form.ah_full_name.help_text }}</div>
            {% if form.ah_full_name.errors %}
                <div class="error">{{ form.ah_full_name.errors }}</div>
            {% endif %}
        </div>
        
        <div style="margin-top: 20px;">
            <button type="submit" class="button">Update Mapping</button>
            <a href="{% url 'physician_name_mapping_list' %}" class="button cancel">Cancel</a>
            <button type="submit" name="delete" class="button delete" 
                    onclick="return confirm('Are you sure you want to delete this mapping?')">Delete Mapping</button>
        </div>
    </form>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const physicianSelect = document.getElementById('id_physician_id');
            const eopcnNameField = document.getElementById('id_eopcn_full_name');
            
            physicianSelect.addEventListener('change', function() {
                const selectedPhysicianId = this.value;
                
                if (selectedPhysicianId) {
                    // Extract the physician name from the selected option text
                    const selectedOption = this.options[this.selectedIndex];
                    const optionText = selectedOption.text;
                    
                    // Extract the name part (everything before " (ID:")
                    const nameMatch = optionText.match(/^(.+) \(ID:/);
                    if (nameMatch) {
                        eopcnNameField.value = nameMatch[1];
                    }
                } else {
                    eopcnNameField.value = '';
                }
            });
            
            // Pre-select the physician ID if one exists
            const originalPhysicianId = "{{ original_mapping.physician_id }}";
            if (originalPhysicianId && originalPhysicianId !== "None") {
                physicianSelect.value = originalPhysicianId;
            }
        });
    </script>
</div>

{% endblock %}
