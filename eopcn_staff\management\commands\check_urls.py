from django.core.management.base import BaseCommand
from django.urls import reverse, NoReverseMatch
from django.conf import settings


class Command(BaseCommand):
    help = 'Check URL configuration for email group endpoints'

    def handle(self, *args, **options):
        self.stdout.write('Checking URL configuration...')
        
        try:
            # Check if the email group info URL can be reversed
            url = reverse('get_email_group_info', args=[1])
            self.stdout.write(self.style.SUCCESS(f'✓ Email group info URL is configured correctly: {url}'))
        except NoReverseMatch:
            self.stdout.write(self.style.ERROR('✗ Error: get_email_group_info URL cannot be reversed'))
        
        # Check if the email groups list URL can be reversed
        try:
            url = reverse('email_groups_list')
            self.stdout.write(self.style.SUCCESS(f'✓ Email groups list URL is configured correctly: {url}'))
        except NoReverseMatch:
            self.stdout.write(self.style.ERROR('✗ Error: email_groups_list URL cannot be reversed'))
            
        # Print the ROOT_URLCONF setting
        self.stdout.write(f'Root URLconf: {settings.ROOT_URLCONF}')
        
        self.stdout.write(self.style.SUCCESS('URL check completed!'))
