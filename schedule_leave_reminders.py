#!/usr/bin/env python
"""
Simple scheduler for leave reminders that can be run as a cron job.
This script should be run daily to process leave reminders.

Usage:
    python schedule_leave_reminders.py

For cron job, add this line to crontab (runs daily at 9 AM):
    0 9 * * * cd /path/to/your/project && python schedule_leave_reminders.py

For Windows Task Scheduler:
    - Create a new task
    - Set trigger to daily at 9:00 AM
    - Set action to start this script
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# Setup paths
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.append(PROJECT_ROOT)

# Setup logging
log_file = os.path.join(PROJECT_ROOT, 'logs', 'leave_reminders.log')
os.makedirs(os.path.dirname(log_file), exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def run_management_command(command_args):
    """Run a Django management command"""
    try:
        # Change to project directory
        os.chdir(PROJECT_ROOT)
        
        # Run the command
        cmd = [sys.executable, 'manage.py'] + command_args
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        if result.returncode == 0:
            logger.info(f"Command succeeded: {' '.join(command_args)}")
            if result.stdout:
                logger.info(f"Output: {result.stdout}")
            return True
        else:
            logger.error(f"Command failed: {' '.join(command_args)}")
            logger.error(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"Command timed out: {' '.join(command_args)}")
        return False
    except Exception as e:
        logger.error(f"Error running command {' '.join(command_args)}: {e}")
        return False

def main():
    """Main scheduler function"""
    logger.info("=" * 50)
    logger.info("Starting leave reminder scheduler")
    logger.info(f"Current time: {datetime.now()}")
    logger.info(f"Project root: {PROJECT_ROOT}")
    
    success_count = 0
    total_commands = 0
    
    # Command 1: Create automatic reminders for new leaves
    total_commands += 1
    logger.info("Creating automatic reminders...")
    if run_management_command(['send_leave_reminders', '--create-auto-reminders']):
        success_count += 1
    
    # Command 2: Send due reminders
    total_commands += 1
    logger.info("Sending due reminders...")
    if run_management_command(['send_leave_reminders']):
        success_count += 1
    
    # Summary
    logger.info(f"Completed: {success_count}/{total_commands} commands succeeded")
    
    if success_count == total_commands:
        logger.info("All reminder tasks completed successfully")
        return 0
    else:
        logger.error("Some reminder tasks failed")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Scheduler interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error in scheduler: {e}")
        sys.exit(1)
