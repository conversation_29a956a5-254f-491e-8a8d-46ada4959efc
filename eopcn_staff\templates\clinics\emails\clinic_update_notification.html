<!DOCTYPE html>
<html>
<head>
    <style>
        .table-container {
            width: 70%;
            table-layout: fixed;
        }
        .label-cell {
            text-align: right;
            width: 30%;
            padding: 4px;
            line-height: 1.2;
        }
        .value-cell {
            background-color: #DDEBF7;
            width: 70%;
            padding: 4px;
            line-height: 1.2;
        }
        .section-header {
            font-size: 18px;
            margin-top: 10px;
        }
    </style>
</head>
<body>

<h1>Clinic Information Updated</h1>

<!-- Clinic Information Section -->
<h2 class="section-header">Updated Clinic Details</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell"{% if changes.clinic_name %} style="background-color:#fff8c6;"{% endif %}>Clinic Name:</td>
        <td class="value-cell">
            {% if changes.clinic_name %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.clinic_name.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.clinic_name.new_value }}</span>
            {% else %}
                {{ clinic.clinic_name }}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.med_group_or_site %} style="background-color:#fff8c6;"{% endif %}>Medical Group/Site:</td>
        <td class="value-cell">
            {% if changes.med_group_or_site %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.med_group_or_site.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.med_group_or_site.new_value }}</span>
            {% else %}
                {{ clinic.med_group_or_site|default_if_none:"" }}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.street_address or changes.floor_unit_room or changes.city or changes.province or changes.postal_code %} style="background-color:#fff8c6;"{% endif %}>Address:</td>
        <td class="value-cell">
            {{ clinic.street_address|default_if_none:"" }}<br>
            {% if clinic.floor_unit_room %}{{ clinic.floor_unit_room }}<br>{% endif %}
            {{ clinic.city|default_if_none:"" }}, {{ clinic.province|default_if_none:"" }} {{ clinic.postal_code|default_if_none:"" }}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.business_phone or changes.extension %} style="background-color:#fff8c6;"{% endif %}>Phone:</td>
        <td class="value-cell">
            {% if changes.business_phone or changes.extension %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.business_phone.old_value }}{% if changes.extension.old_value %} ext. {{ changes.extension.old_value }}{% endif %}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.business_phone.new_value|default:clinic.business_phone }}{% if changes.extension.new_value %} ext. {{ changes.extension.new_value }}{% elif clinic.extension %} ext. {{ clinic.extension }}{% endif %}</span>
            {% else %}
                {{ clinic.business_phone|default_if_none:"" }}{% if clinic.extension %} ext. {{ clinic.extension }}{% endif %}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.fax %} style="background-color:#fff8c6;"{% endif %}>Fax:</td>
        <td class="value-cell">
            {% if changes.fax %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.fax.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.fax.new_value }}</span>
            {% else %}
                {{ clinic.fax|default_if_none:"" }}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.clinic_website %} style="background-color:#fff8c6;"{% endif %}>Website:</td>
        <td class="value-cell">
            {% if changes.clinic_website %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.clinic_website.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.clinic_website.new_value }}</span>
            {% else %}
                {{ clinic.clinic_website|default_if_none:"" }}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.clinic_emr %} style="background-color:#fff8c6;"{% endif %}>EMR System:</td>
        <td class="value-cell">
            {% if changes.clinic_emr %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.clinic_emr.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.clinic_emr.new_value }}</span>
            {% else %}
                {{ clinic.clinic_emr|default_if_none:"" }}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.pia_number %} style="background-color:#fff8c6;"{% endif %}>PIA Number:</td>
        <td class="value-cell">
            {% if changes.pia_number %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.pia_number.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.pia_number.new_value }}</span>
            {% else %}
                {{ clinic.pia_number|default_if_none:"" }}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.include_on_eopcn_website %} style="background-color:#fff8c6;"{% endif %}>Include on EOPCN Website:</td>
        <td class="value-cell">
            {% if changes.include_on_eopcn_website %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.include_on_eopcn_website.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.include_on_eopcn_website.new_value }}</span>
            {% else %}
                {{ clinic.include_on_eopcn_website|yesno:"Yes,No" }}
            {% endif %}
        </td>
    </tr>
</table>

<!-- Primary Contact Section -->
<h2 class="section-header">Primary Contact</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell"{% if changes.primary_contact_first_name or changes.primary_contact_last_name %} style="background-color:#fff8c6;"{% endif %}>Name:</td>
        <td class="value-cell">
            {% if changes.primary_contact_first_name or changes.primary_contact_last_name %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.primary_contact_first_name.old_value }} {{ changes.primary_contact_last_name.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.primary_contact_first_name.new_value|default:clinic.primary_contact_first_name }} {{ changes.primary_contact_last_name.new_value|default:clinic.primary_contact_last_name }}</span>
            {% else %}
                {{ clinic.primary_contact_first_name|default_if_none:"" }} {{ clinic.primary_contact_last_name|default_if_none:"" }}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.primary_contact_role %} style="background-color:#fff8c6;"{% endif %}>Role:</td>
        <td class="value-cell">
            {% if changes.primary_contact_role %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.primary_contact_role.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.primary_contact_role.new_value }}</span>
            {% else %}
                {{ clinic.primary_contact_role|default_if_none:"" }}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.primary_contact_phone or changes.primary_contact_ext %} style="background-color:#fff8c6;"{% endif %}>Phone:</td>
        <td class="value-cell">
            {% if changes.primary_contact_phone or changes.primary_contact_ext %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.primary_contact_phone.old_value }}{% if changes.primary_contact_ext.old_value %} ext. {{ changes.primary_contact_ext.old_value }}{% endif %}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.primary_contact_phone.new_value|default:clinic.primary_contact_phone }}{% if changes.primary_contact_ext.new_value %} ext. {{ changes.primary_contact_ext.new_value }}{% elif clinic.primary_contact_ext %} ext. {{ clinic.primary_contact_ext }}{% endif %}</span>
            {% else %}
                {{ clinic.primary_contact_phone|default_if_none:"" }}{% if clinic.primary_contact_ext %} ext. {{ clinic.primary_contact_ext }}{% endif %}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.primary_contact_email %} style="background-color:#fff8c6;"{% endif %}>Email:</td>
        <td class="value-cell">
            {% if changes.primary_contact_email %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.primary_contact_email.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.primary_contact_email.new_value }}</span>
            {% else %}
                {{ clinic.primary_contact_email|default_if_none:"" }}
            {% endif %}
        </td>
    </tr>
</table>

{% if clinic.alternate_contact_first_name or clinic.alternate_contact_last_name %}
<!-- Alternate Contact Section -->
<h2 class="section-header">Alternate Contact</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell"{% if changes.alternate_contact_first_name or changes.alternate_contact_last_name %} style="background-color:#fff8c6;"{% endif %}>Name:</td>
        <td class="value-cell">
            {% if changes.alternate_contact_first_name or changes.alternate_contact_last_name %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.alternate_contact_first_name.old_value }} {{ changes.alternate_contact_last_name.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.alternate_contact_first_name.new_value|default:clinic.alternate_contact_first_name }} {{ changes.alternate_contact_last_name.new_value|default:clinic.alternate_contact_last_name }}</span>
            {% else %}
                {{ clinic.alternate_contact_first_name|default_if_none:"" }} {{ clinic.alternate_contact_last_name|default_if_none:"" }}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.alternate_contact_role %} style="background-color:#fff8c6;"{% endif %}>Role:</td>
        <td class="value-cell">
            {% if changes.alternate_contact_role %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.alternate_contact_role.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.alternate_contact_role.new_value }}</span>
            {% else %}
                {{ clinic.alternate_contact_role|default_if_none:"" }}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.alternate_contact_phone or changes.alternate_contact_ext %} style="background-color:#fff8c6;"{% endif %}>Phone:</td>
        <td class="value-cell">
            {% if changes.alternate_contact_phone or changes.alternate_contact_ext %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.alternate_contact_phone.old_value }}{% if changes.alternate_contact_ext.old_value %} ext. {{ changes.alternate_contact_ext.old_value }}{% endif %}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.alternate_contact_phone.new_value|default:clinic.alternate_contact_phone }}{% if changes.alternate_contact_ext.new_value %} ext. {{ changes.alternate_contact_ext.new_value }}{% elif clinic.alternate_contact_ext %} ext. {{ clinic.alternate_contact_ext }}{% endif %}</span>
            {% else %}
                {{ clinic.alternate_contact_phone|default_if_none:"" }}{% if clinic.alternate_contact_ext %} ext. {{ clinic.alternate_contact_ext }}{% endif %}
            {% endif %}
        </td>
    </tr>
    <tr>
        <td class="label-cell"{% if changes.alternate_contact_email %} style="background-color:#fff8c6;"{% endif %}>Email:</td>
        <td class="value-cell">
            {% if changes.alternate_contact_email %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.alternate_contact_email.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.alternate_contact_email.new_value }}</span>
            {% else %}
                {{ clinic.alternate_contact_email|default_if_none:"" }}
            {% endif %}
        </td>
    </tr>
</table>
{% endif %}

{% if comment %}
<!-- Comment Section -->
<h2 class="section-header">Additional Comments</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell"{% if changes.comment %} style="background-color:#fff8c6;"{% endif %}>Comment:</td>
        <td class="value-cell">
            {% if changes.comment %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.comment.old_value }}</span>
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.comment.new_value }}</span>
            {% else %}
                {{ comment }}
            {% endif %}
        </td>
    </tr>
</table>
{% endif %}

{% if details_url %}
<p>
    <a href="{{ details_url }}">View record in operational database</a>
</p>
{% endif %}
<p>
    Updated by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).
</p>

<p>Best regards,</p>
<p>EOPCN Automation Admin</p>

<div style="margin-top: 20px; padding: 10px; background-color: #f1f1f1; text-align: center; border-radius: 0 0 5px 5px; font-size: 12px;">
    <p>This is an automated message from the EOPCN Operational Application.</p>
</div>

</body>
</html>
