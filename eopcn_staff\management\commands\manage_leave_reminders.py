from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta, date
import pytz

from eopcn_staff.models import StaffLeave, LeaveReminder, LeaveReminderType, EmailGroup


class Command(BaseCommand):
    help = "Manage leave reminders - view, create, test, and clean up"

    def add_arguments(self, parser):
        parser.add_argument(
            '--list-pending',
            action='store_true',
            help='List all pending reminders',
        )
        parser.add_argument(
            '--list-types',
            action='store_true',
            help='List all reminder types',
        )
        parser.add_argument(
            '--create-test-leave',
            action='store_true',
            help='Create a test leave with reminders for testing',
        )
        parser.add_argument(
            '--cleanup-old',
            action='store_true',
            help='Clean up old sent/failed reminders (older than 30 days)',
        )
        parser.add_argument(
            '--reset-failed',
            action='store_true',
            help='Reset failed reminders to pending status',
        )
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show reminder statistics',
        )

    def handle(self, *args, **options):
        if options['list_pending']:
            self.list_pending_reminders()
        
        if options['list_types']:
            self.list_reminder_types()
        
        if options['create_test_leave']:
            self.create_test_leave()
        
        if options['cleanup_old']:
            self.cleanup_old_reminders()
        
        if options['reset_failed']:
            self.reset_failed_reminders()
        
        if options['stats']:
            self.show_statistics()
        
        if not any(options.values()):
            self.stdout.write("Use --help to see available options")

    def list_pending_reminders(self):
        """List all pending reminders"""
        try:
            reminders = LeaveReminder.objects.filter(
                status='pending'
            ).select_related('staff_leave__staff', 'reminder_type').order_by('scheduled_datetime')
            
            self.stdout.write(f"\n=== Pending Reminders ({reminders.count()}) ===")
            
            if not reminders:
                self.stdout.write("No pending reminders found")
                return
            
            mst = pytz.timezone('US/Mountain')
            now_mst = timezone.now().astimezone(mst)
            
            for reminder in reminders:
                staff = reminder.staff_leave.staff
                scheduled_mst = reminder.scheduled_datetime.astimezone(mst)
                is_due = scheduled_mst <= now_mst
                
                status_indicator = "🔴 DUE" if is_due else "⏰ PENDING"
                
                self.stdout.write(
                    f"{status_indicator} {staff.first_name} {staff.last_name} - "
                    f"{reminder.reminder_type.name} - "
                    f"{scheduled_mst.strftime('%Y-%m-%d %H:%M %Z')}"
                )
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error listing reminders: {e}"))

    def list_reminder_types(self):
        """List all reminder types"""
        try:
            types = LeaveReminderType.objects.all().order_by('reminder_type', 'days_offset')
            
            self.stdout.write(f"\n=== Reminder Types ({types.count()}) ===")
            
            for reminder_type in types:
                status = "✅ Active" if reminder_type.is_active else "❌ Inactive"
                offset_desc = f"{reminder_type.days_offset} days"
                if reminder_type.days_offset < 0:
                    offset_desc = f"{abs(reminder_type.days_offset)} days before"
                elif reminder_type.days_offset > 0:
                    offset_desc = f"{reminder_type.days_offset} days after"
                else:
                    offset_desc = "same day"
                
                self.stdout.write(
                    f"{status} {reminder_type.name} ({reminder_type.get_reminder_type_display()}) - "
                    f"{offset_desc}"
                )
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error listing reminder types: {e}"))

    def create_test_leave(self):
        """Create a test leave with reminders"""
        try:
            from eopcn_staff.models import Staff, StaffLeaveTypes
            
            # Get first staff member and leave type
            staff = Staff.objects.first()
            leave_type = StaffLeaveTypes.objects.first()
            
            if not staff or not leave_type:
                self.stdout.write(self.style.ERROR("Need at least one staff member and leave type"))
                return
            
            # Create leave starting in 5 days, returning in 10 days
            today = date.today()
            start_date = today + timedelta(days=5)
            return_date = today + timedelta(days=10)
            
            # Create the leave
            test_leave = StaffLeave.objects.create(
                staff=staff,
                leave_type=leave_type,
                leave_start_date=start_date,
                return_date=return_date,
                date_created=timezone.now(),
                created_by='test_system',
                date_modified=timezone.now(),
                modified_by='test_system'
            )
            
            self.stdout.write(
                f"Created test leave {test_leave.leave_id} for {staff.first_name} {staff.last_name}"
            )
            
            # Create reminders
            self.create_reminders_for_leave(test_leave)
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error creating test leave: {e}"))

    def create_reminders_for_leave(self, leave):
        """Create reminders for a specific leave"""
        try:
            mst = pytz.timezone('US/Mountain')
            reminder_types = LeaveReminderType.objects.filter(is_active=True)
            
            created_count = 0
            for reminder_type in reminder_types:
                # Calculate reminder datetime
                if reminder_type.reminder_type == 'before_leave':
                    base_date = leave.leave_start_date
                elif reminder_type.reminder_type == 'before_return':
                    base_date = leave.return_date
                else:
                    continue  # Skip other types for now
                
                if not base_date:
                    continue
                
                reminder_date = base_date + timedelta(days=reminder_type.days_offset)
                reminder_datetime = datetime.combine(reminder_date, datetime.min.time().replace(hour=9))
                reminder_datetime = mst.localize(reminder_datetime).astimezone(pytz.UTC)
                
                # Create reminder
                reminder, created = LeaveReminder.objects.get_or_create(
                    staff_leave=leave,
                    reminder_type=reminder_type,
                    defaults={
                        'scheduled_datetime': reminder_datetime,
                        'status': 'pending',
                        'recipients': '[]',
                        'created_by': 'test_system'
                    }
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(f"  Created {reminder_type.name} reminder")
            
            self.stdout.write(f"Created {created_count} reminders for the test leave")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error creating reminders: {e}"))

    def cleanup_old_reminders(self):
        """Clean up old sent/failed reminders"""
        try:
            cutoff_date = timezone.now() - timedelta(days=30)
            
            old_reminders = LeaveReminder.objects.filter(
                status__in=['sent', 'failed'],
                date_created__lt=cutoff_date
            )
            
            count = old_reminders.count()
            old_reminders.delete()
            
            self.stdout.write(f"Cleaned up {count} old reminders")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error cleaning up reminders: {e}"))

    def reset_failed_reminders(self):
        """Reset failed reminders to pending"""
        try:
            failed_reminders = LeaveReminder.objects.filter(status='failed')
            count = failed_reminders.count()
            
            failed_reminders.update(
                status='pending',
                error_message=None,
                sent_datetime=None
            )
            
            self.stdout.write(f"Reset {count} failed reminders to pending")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error resetting failed reminders: {e}"))

    def show_statistics(self):
        """Show reminder system statistics"""
        try:
            self.stdout.write("\n=== Reminder System Statistics ===")
            
            # Legacy reminders
            legacy_pending = StaffLeave.objects.filter(
                reminder_datetime__isnull=False,
                reminder_sent=False
            ).count()
            
            legacy_sent = StaffLeave.objects.filter(
                reminder_datetime__isnull=False,
                reminder_sent=True
            ).count()
            
            self.stdout.write(f"Legacy System:")
            self.stdout.write(f"  Pending: {legacy_pending}")
            self.stdout.write(f"  Sent: {legacy_sent}")
            
            # New reminder system
            try:
                new_pending = LeaveReminder.objects.filter(status='pending').count()
                new_sent = LeaveReminder.objects.filter(status='sent').count()
                new_failed = LeaveReminder.objects.filter(status='failed').count()
                new_cancelled = LeaveReminder.objects.filter(status='cancelled').count()
                
                self.stdout.write(f"\nNew System:")
                self.stdout.write(f"  Pending: {new_pending}")
                self.stdout.write(f"  Sent: {new_sent}")
                self.stdout.write(f"  Failed: {new_failed}")
                self.stdout.write(f"  Cancelled: {new_cancelled}")
                
            except Exception:
                self.stdout.write(f"\nNew System: Not available (run migrations)")
            
            # Email groups
            active_groups = EmailGroup.objects.filter(is_active=True).count()
            self.stdout.write(f"\nEmail Groups: {active_groups} active")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error showing statistics: {e}"))
