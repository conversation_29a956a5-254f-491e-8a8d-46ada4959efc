{% extends "base.html" %}
{% load custom_filters %}
{% load humanize custom_filters %}

{% block title %}{% if physician.title %}{{ physician.title }} {% endif %}{{ physician.last_name }}, {{ physician.first_name }} Details{% endblock %}

{% block content %}
<style>
  /* Container that wraps the report */
  .detail-container { 
      max-width: 700px; 
      margin: 20px auto; 
      background: #fff; 
      padding: 20px; 
      border: 1px solid #ddd; 
      border-radius: 5px; 
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  /* Printable area: will be the only content printed */
  .printable-area { }
  /* Header styling */
  .detail-header {
      background-color: #0067b1;
      color: white;
      padding: 25px 15px; /* Increased top/bottom padding */
      border-radius: 5px 5px 0 0;
      display: flex; 
      justify-content: space-between;
      align-items: center;
      margin: -20px -20px 20px -20px;
  }
  /* Section styling */
  .detail-section {
      margin-bottom: 20px;
  }
  .detail-section h2 {
      font-size: 1.25em;
      border-bottom: 1px solid #ddd;
      padding-bottom: 5px;
      margin-bottom: 10px;
  }
  /* List styling */
  .detail-list {
      list-style-type: none;
      padding: 0;
  }
  .detail-list li {
      padding: 5px 0;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      flex-wrap: wrap;
  }
  .detail-list li:last-child {
      border-bottom: none;
  }
  /* Label styling: fixed width so values align */
  .detail-label {
      width: 250px; 
      font-weight: bold;
      flex-shrink: 0;
  }
  /* Value styling */
  .detail-value {
      flex: 1;
  }
  /* Card styling for each panel detail */
  .panel-card {
      background: #f9f9f9;
      padding: 15px;
      margin-bottom: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
  }
  /* Back link styling */
  .back-link {
      display: inline-block;
      margin-top: 20px;
      padding: 10px 15px;
      background-color: #6c757d;
      color: white;
      text-decoration: none;
      border-radius: 4px;
  }
  .back-link:hover {
      background-color:rgb(96, 106, 115);
  }

  /* Print button styling */
  .print-button {
    margin-top: 20px;
      padding: 10px 15px;
      background-color: #28a745;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
  }
  .print-button:hover {
      background-color: #218838;
  }
  
  /* Print media query: hide everything except the printable area */
  @media print {
      body * {
          visibility: hidden;
      }
      .printable-area, .printable-area * {
          visibility: visible;
      }
      .printable-area {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
      }
      .no-print {
          display: none !important;
      }
      .qr-code-section {
          display: flex !important;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          visibility: visible !important;
          width: 100px;
          height: auto;
          margin: 0;
          padding: 0;
          border: none;
          text-align: center;
      }
      .qr-code-image {
          width: 80px; 
          height: 80px;
          margin: 0 0 5px 0;
          border: 3px solid white; /* White border */
          border-radius: 8px; /* Rounded corners */
          padding: 4px; /* Padding between border and QR code */
          background-color: white; /* Ensure padding area is white */
          box-sizing: border-box; /* Include padding and border in element's total size */
      }
      .qr-code-text {
          display: block !important;
          font-size: 0.7em;
          line-height: 1.1;
          color: white;
          margin: 0;
      }
      .qr-code-section img {
          visibility: visible !important; 
      }
      .detail-header {
          -webkit-print-color-adjust: exact; 
          print-color-adjust: exact;
          flex-direction: column;
          padding: 15px 20px;
      }
      .print-logo-container {
          display: flex !important;
          justify-content: center;
          width: 100%;
          margin-bottom: 15px;
          visibility: visible !important;
      }
      .print-logo {
          display: block !important;
          visibility: visible !important;
          width: 500px; /* Increased logo width for print */
          height: auto;
      }
      .physician-name-container {
          display: flex !important;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          visibility: visible !important;
      }
      .qr-link-wrapper {
          display: block !important;
          visibility: visible !important;
      }
      .physician-name-container h1 {
          text-align: center !important;
          margin: 0 auto !important;
      }
  }

  /* New styles for dual-row layout */
  .dual-row {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 10px;
  }

  .dual-column {
      flex: 1;
      padding-right: 10px;
  }

  .dual-column .detail-label {
      width: 150px; 
      font-weight: bold;
      flex-shrink: 0;
  }
  .dual-column .detail-value {
      display: inline-block;
      width: 150px;  
      padding-left: 10px;
  }
  .info-grid {
    display: grid;
    grid-template-columns: 150px auto 150px auto;
    gap: 10px 20px;
    align-items: center;
    margin-top: 10px;
  }
  
  .info-row {
    display: contents;
  }
  
  .info-label {
    font-weight: bold;
    text-align: left;
  }
  
  .info-value {
    text-align: left;
  }
  
  /* Edit button styling */
  .edit-button {
      display: inline-block;
      margin-top: 20px;
      margin-left: 10px;
      padding: 10px 15px;
      background-color: #FFC107;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      cursor: pointer;
  }
  .edit-button:hover {
      background-color: #e0a800;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .section-title {
    font-size: 1.25em;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
    margin-bottom: 0;
    flex-grow: 1;
  }
  
  .section-action {
    padding: 5px 10px;
    font-size: 0.85em;
    background-color: #80caf0;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    margin-left: 15px;
  }
  
  .section-action:hover {
    background-color: #015081;
  }  

  .nav-buttons-container {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }

  /* QR Code styling */
  .qr-code-section {
      display: none;
      width: 120px;
      height: auto;
      flex-shrink: 0;
  }
  
  .print-logo-container {
      display: none;
      width: 100%;
  }
  
  .physician-name-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
  }
  
  .print-logo {
      display: none;
      width: 150px;
      height: auto;
  }
  
  .qr-link-wrapper {
      display: none;
  }

  .physician-name-container h1 {
      margin: 0 auto;
      text-align: center;
  }

  /* Add message alert styling */
  .messages {
    margin-bottom: 15px;
  }
  
  .alert {
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 4px;
    border: 1px solid transparent;
  }
  
  .alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
  }
  
  .alert-warning {
    background-color: #fff3cd;
    border-color: #ffeeba;
    color: #856404;
  }
  
  .alert-error, .alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
  }
  
  .alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
  }
  
  /* CPAR row styling */
  .cpar-row {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    padding: 8px 0;
  }
  
  .cpar-row .detail-label {
    width: 150px;
    font-weight: bold;
    flex-shrink: 0;
  }
  
  .cpar-row .cpar-panel-id {
    width: 120px;
    flex-shrink: 0;
    margin-right: 20px;
  }
  
  .cpar-checkboxes {
    display: flex;
    gap: 30px;
    flex: 1;
    min-width: 0;
  }
  
  .cpar-checkbox-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 80px;
  }
  
  .checkbox-label {
    font-size: 0.9em;
    font-weight: bold;
    color: #666;
    margin-bottom: 2px;
  }
  
  .checkbox-value {
    font-size: 0.95em;
    padding: 2px 8px;
    border-radius: 3px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    min-width: 60px;
  }
  
  /* Responsive behavior for smaller screens */
  @media (max-width: 768px) {
    .cpar-row {
      flex-direction: column;
    }
    
    .cpar-row .detail-label,
    .cpar-row .cpar-panel-id {
      width: 100%;
      margin-bottom: 8px;
    }
    
    .cpar-checkboxes {
      width: 100%;
      justify-content: space-around;
    }
  }

  /* Languages section styling */
  .languages-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
  }
  
  .language-badge {
    background-color: #0067b1;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.9em;
    font-weight: normal;
    display: inline-block;
  }
  
  @media print {
    .language-badge {
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
      border: 1px solid #0067b1;
    }
  }
</style>

<!-- Process messages as JavaScript alerts instead of inline HTML -->
{% if messages %}
    <script>
        {% for message in messages %}
            alert("{{ message }}");
        {% endfor %}
    </script>
{% endif %}

<div class="detail-container printable-area">
    <div class="detail-header">  
        <!-- Physician name and QR code container -->
        <div class="physician-name-container">
            <img src="{{ logo_url }}" alt="EOPCN Logo" class="print-logo">
            <h1>{% if physician.title %}{{ physician.title }} {% else %}[Title] {% endif %}{{ physician.last_name }}, {{ physician.first_name }}</h1>
            
            <!-- QR Code Section -->
            <div class="qr-code-section">
                <!-- QR Code wrapped in a link -->
                <a href="{{ form_url }}" class="qr-link-wrapper" target="_blank">
                    <img src="{{ qr_code_url }}" class="qr-code-image" alt="EOPCN Primary Care Provider Information Update Form QR Code">
                </a>
                
                <p class="qr-code-text">Scan to update information</p>
            </div>
        </div>
    </div>
    
    <!-- General Information Section -->
    <div class="detail-section">
        <div class="section-header">
            <div class="section-title">General Information</div>
            <a href="{% url 'edit_physician' physician.physician_id %}" class="section-action no-print">Edit Info</a>
        </div>
        <div class="info-grid">
          <div class="info-row">
            <div class="info-label">Gender:</div>
            <div class="info-value">{{ physician.gender }}</div>
            <div class="info-label">&nbsp;</div>
            <div class="info-value">&nbsp;</div>
          </div>
           <div class="info-row">
            <div class="info-label">Prac ID:</div>
            <div class="info-value">{{ physician.practitioner_id|default_if_none:"" }}</div>
            <div class="info-label"></div>
            <div class="info-value">{{ physician.npi }}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Primary Email:</div>
            <div class="info-value">{{ physician.primary_email }}</div>
            <div class="info-label">Alternate Email:</div>
            <div class="info-value">{{ physician.alternate_email|default_if_none:"" }}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Email Opt-out:</div>
            <div class="info-value">{% if physician.do_not_email %}Yes{% else %}No{% endif %}</div>
            <div class="info-label">&nbsp;</div>
            <div class="info-value">&nbsp;</div>
          </div>
          <div class="info-row">
            <div class="info-label">Primary Phone:</div>
            <div class="info-value">{{ physician.primary_phone|phone_format|default_if_none:"" }}</div>
            <div class="info-label">Alternate Phone:</div>
            <div class="info-value">{{ physician.alternate_phone|phone_format|default_if_none:"" }}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Languages:</div>
            <div class="info-value">
                {% if physician.languages.all %}
                    <div class="languages-container">
                        {% for language in physician.languages.all %}
                            <span class="language-badge">{{ language.language }}</span>
                        {% endfor %}
                    </div>
                {% else %}
                    <span style="color: #666; font-style: italic;">None specified</span>
                {% endif %}
            </div>
            <div class="info-label">&nbsp;</div>
            <div class="info-value">&nbsp;</div>
          </div>
        </div>
    </div>
    
    <!-- Active Clinic Details Section -->
    <div class="detail-section">
        <div class="section-header">
            <div class="section-title">Active Clinic Details</div>
            <a href="{% url 'edit_physician_clinics' physician.physician_id %}" class="section-action no-print">Edit Clinics</a>
        </div>
        {% if panel_details %}
            {# First display primary clinic (where portion_of_practice is "Primary") #}
            {% for detail in panel_details %}
                {% if not detail.date_left_clinic and detail.portion_of_practice == "Primary" %}
                <div class="panel-card">
                    <ul class="detail-list">
                        <li>
                            <span class="detail-label">Clinic Name:</span>
                            <span class="detail-value">
                                {% if detail.clinic_id %}
                                    <a href="{% url 'clinic_detail' detail.clinic_id %}" style="color: #0067b1; text-decoration: none;">
                                        {{ detail.clinic_name }}
                                    </a>
                                {% else %}
                                    {{ detail.clinic_name }}
                                {% endif %}
                            </span>
                        </li>
                        {% if detail.panel_size_oct_2024 %}
                        <li>
                            <span class="detail-label">Panel Size:</span>
                            <span class="detail-value">
                                {{ detail.panel_size_oct_2024|intcomma }}
                                {% if detail.panel_size_report_month or detail.panel_size_report_year %}
                                    ({% if detail.panel_size_report_month %}{{ detail.panel_size_report_month|month_abbr }}{% endif %}{% if detail.panel_size_report_year %}, {{ detail.panel_size_report_year }}{% endif %})
                                {% endif %}
                            </span>
                        </li>
                        {% endif %}
                        <li>
                            <span class="detail-label">Portion Practice:</span>
                            <span class="detail-value">{{ detail.portion_of_practice|default_if_none:"" }}</span>
                        </li>
                        <li>
                            <span class="detail-label">Accepting Patients:</span>
                            <span class="detail-value">{{ detail.accepting_patients|default_if_none:"" }}</span>
                        </li>
                        <li>
                            <span class="detail-label">Include on AFAD:</span>
                            <span class="detail-value">{{ detail.include_on_afad_website|default_if_none:"" }}</span>
                        </li>
                        <li>
                            <span class="detail-label">Include on EOPCN website:</span>
                            <span class="detail-value">{{ detail.include_on_eopcn_website|default_if_none:"" }}</span>
                        </li>
                        <li>
                            <span class="detail-label">Date Active:</span>
                            <span class="detail-value">{{ detail.date_active_in_clinic|date:"M d, Y" }}</span>
                        </li>
                        
                        <!-- Add the new CPAR fields with proper alignment -->
                        <li style="padding-left: 20px;">
                            <span class="detail-label">CPAR Panel ID:</span>
                            <span class="detail-value">{{ detail.CPAR_Panel_ID|default_if_none:"Not specified" }}</span>
                        </li>
                        <li style="padding-left: 20px;">
                            <span class="detail-label">Active CII:</span>
                            <span class="detail-value">{% if detail.active_CII %}Yes{% elif detail.active_CII is False %}No{% else %}Not specified{% endif %}</span>
                        </li>
                        <li style="padding-left: 20px;">
                            <span class="detail-label">Active CPAR:</span>
                            <span class="detail-value">{% if detail.active_CPAR %}Yes{% elif detail.active_CPAR is False %}No{% else %}Not specified{% endif %}</span>
                        </li>
                    </ul>
                </div>
                {% endif %}
            {% endfor %}

            {# Then display all other clinics (non-primary) #}
            {% for detail in panel_details %}
                {% if not detail.date_left_clinic and detail.portion_of_practice != "Primary" %}
                <div class="panel-card">
                    <ul class="detail-list">
                        <li>
                            <span class="detail-label">Clinic Name:</span>
                            <span class="detail-value">
                                {% if detail.clinic_id %}
                                    <a href="{% url 'clinic_detail' detail.clinic_id %}" style="color: #0067b1; text-decoration: none;">
                                        {{ detail.clinic_name }}
                                    </a>
                                {% else %}
                                    {{ detail.clinic_name }}
                                {% endif %}
                            </span>
                        </li>
                        {% if detail.panel_size_oct_2024 %}
                        <li>
                            <span class="detail-label">Panel Size:</span>
                            <span class="detail-value">
                                {{ detail.panel_size_oct_2024|intcomma }}
                                {% if detail.panel_size_report_month or detail.panel_size_report_year %}
                                    ({% if detail.panel_size_report_month %}{{ detail.panel_size_report_month|month_abbr }}{% endif %}{% if detail.panel_size_report_year %}, {{ detail.panel_size_report_year }}{% endif %})
                                {% endif %}
                            </span>
                        </li>
                        {% endif %}
                        <li>
                            <span class="detail-label">Portion Practice:</span>
                            <span class="detail-value">{{ detail.portion_of_practice|default_if_none:"" }}</span>
                        </li>
                        <li>
                            <span class="detail-label">Accepting Patients:</span>
                            <span class="detail-value">{{ detail.accepting_patients|default_if_none:"" }}</span>
                        </li>
                        <li>
                            <span class="detail-label">Include on AFAD:</span>
                            <span class="detail-value">{{ detail.include_on_afad_website|default_if_none:"" }}</span>
                        </li>
                        <li>
                            <span class="detail-label">Include on EOPCN website:</span>
                            <span class="detail-value">{{ detail.include_on_eopcn_website|default_if_none:"" }}</span>
                        </li>
                        <li>
                            <span class="detail-label">Date Active:</span>
                            <span class="detail-value">{{ detail.date_active_in_clinic|date:"M d, Y" }}</span>
                        </li>
                        
                        <!-- Add the new CPAR fields for alternate clinics too -->
                        <li style="padding-left: 20px;">
                            <span class="detail-label">CPAR Panel ID:</span>
                            <span class="detail-value">{{ detail.CPAR_Panel_ID|default_if_none:"Not specified" }}</span>
                        </li>
                        <li style="padding-left: 20px;">
                            <span class="detail-label">Active CII:</span>
                            <span class="detail-value">{% if detail.active_CII %}Yes{% elif detail.active_CII is False %}No{% else %}Not specified{% endif %}</span>
                        </li>
                        <li style="padding-left: 20px;">
                            <span class="detail-label">Active CPAR:</span>
                            <span class="detail-value">{% if detail.active_CPAR %}Yes{% elif detail.active_CPAR is False %}No{% else %}Not specified{% endif %}</span>
                        </li>
                    </ul>
                </div>
                {% endif %}
            {% endfor %}
        {% else %}
            <p>No panel details found.</p>
        {% endif %}
    </div>
    
    <!-- Navigation Buttons -->
     <div class="nav-buttons-container no-print">
        <a href="{% url 'physician_list' %}" class="back-link no-print">Back to Primary Care Provider List</a>
        <button class="print-button no-print" onclick="window.print()">Print Report</button>
    </div>
</div>

{% endblock %}
