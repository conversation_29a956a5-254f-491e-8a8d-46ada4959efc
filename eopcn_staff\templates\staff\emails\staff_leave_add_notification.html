<!DOCTYPE html>
<html>
<head>
    <title>New Leave Record for {{ staff_member.first_name }} {{ staff_member.last_name }}</title>
</head>
<body>
    <h2>New Leave Record for {{ staff_member.first_name }} {{ staff_member.last_name }}</h2>

    <p>Hello,</p>

    <p>A new leave record has been added for <strong>{{ staff_member.first_name }} {{ staff_member.last_name }}</strong>.</p>

    <p>Here are the details of the leave:</p>
    <h3>Leave Details</h3>
    <ul>
        {% if staff_leave.leave_type %}
        <li><strong>Leave Type:</strong> {{ staff_leave.leave_type }}</li>
        {% endif %}

        {% if staff_leave.leave_start_date %}
        <li><strong>Start Date:</strong> {{ staff_leave.leave_start_date }}</li>
        {% endif %}

        {% if staff_leave.return_date %}
        <li><strong>End Date:</strong> {{ staff_leave.return_date }}</li>
        {% endif %}
    </ul>

    <h3>Staff Coverage Details</h3>
    <ul>
        {% for coverage in staff_coverage %}
            <li>
                <strong>Covering Staff:</strong> {{ coverage.covering_staff|default:"None" }}<br>
                {% if coverage.coverage_type %}
                <strong>Coverage Type:</strong> {{ coverage.coverage_type }}<br>
                {% endif %}
                <strong>Coverage Period:</strong> 
                {{ coverage.coverage_start_date|date:"Y-m-d"|default:"Not specified" }} to {{ coverage.coverage_end_date|date:"Y-m-d"|default:"Not specified" }}
            </li>
        {% empty %}
            <li>
                <i>No covering staff assigned.</i>
            </li>
        {% endfor %}
    </ul>

    {% if comment %}
    <h3>Additional Comments</h3>
    <p><strong>Comment:</strong> <span style="background-color:#DDEBF7">{{ comment }}</span></p>
    {% endif %}

    <p>
        Submitted by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).
    </p>
    {% if details_url %}
    <p>
        <a href="{{ details_url }}">View record in operational database</a>
    </p>
    {% endif %}

    <p>Best regards,</p>
    <p>EOPCN Automation Admin</p>
</body>
</html>

