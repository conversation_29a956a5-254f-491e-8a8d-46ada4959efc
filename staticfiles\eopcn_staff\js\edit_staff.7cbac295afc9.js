// Wait for the DOM to be fully loaded before executing the script and this is getting passed into the edit_staff.html template
document.addEventListener("DOMContentLoaded", function() {
    // add variable assignment button click event and pass in a function to add a new variable assignment formset
    const addAssignmentBtn = document.getElementById("add-assignment");
    addAssignmentBtn.addEventListener("click", add_new_assignment_form);

    // create function to add a new assignment formset and pass in function to assignment button event listener
    function add_new_assignment_form(event) {
        // prevent the default action of the button
        if (event) {
            event.preventDefault();
        }

        // get the assignment formset container
        const assignmentContainer = document.getElementById("assignment-formset");
        const totalForms = document.getElementById("id_assignments-TOTAL_FORMS");
        const currentFormCount = parseInt(totalForms.value);

        // print to console the total number of forms
        console.log("Total forms: ", currentFormCount);

    }

    // create function to hide all fields in the extra assignment formset if role for the assignment is not selected
    function hide_assignment_fields(form, role) {
        // get the role select element
        const roleSelect = form.querySelector("select[name$='role']");
        // get the role value
        const roleValue = roleSelect.value;

        // get the fields to hide
        const fieldsToHide = form.querySelectorAll(".field");

        // if the role is not selected
        if (!roleValue) {
            // loop through the fields to hide
            fieldsToHide.forEach(function(field) {
                // hide the field
                field.style.display = "none";
            });
        } else {
            // loop through the fields to hide
            fieldsToHide.forEach(function(field) {
                // show the field
                field.style.display = "";
            });
        }
    }

});