# Production Environment Variables
# ⚠️  WARNING: This file contains actual credentials - DO NOT COMMIT TO VERSION CONTROL
# Copy this file to .env for local development
# For production deployment, set these as environment variables in your hosting platform

# Django Configuration
SECRET_KEY=django-insecure-&6kj1hz@1e0dl&jjeg$$j%_2=d5wq_*kdw4bixbjhvnxs_zvjv
DJANGO_ENVIRONMENT=production

# Azure AD Configuration
AZURE_CLIENT_ID=d7628e66-31af-4eee-8290-fbb45a23f7fa
AZURE_CLIENT_SECRET=****************************************
AZURE_TENANT_ID=8303f5d5-24fe-4856-9015-25d9a4ee9400

# Database Configuration
DB_NAME=PCNQITeamSQLDB
DB_HOST=pcnqisqlsvr.database.windows.net
DB_PORT=1433
DB_USER=<EMAIL>
DB_PASSWORD=Juh49280

# Email Configuration
EMAIL_HOST=smtp.office365.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=Juh49280

# Azure Storage Configuration
AZURE_STORAGE_KEY=dOcf7w0qB79ajxzf+gyoKmpYO+WWLhPCDf8XH6YnOtjZzajL2pMrjCRzLn8hWWVIRAqsdSkx7zqR+ASt6A349w==

# Optional: Custom allowed hosts (comma-separated)
# ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
