{% extends "base.html" %}

{% block title %}Physician List{% endblock %}

{% block content %}
{% load custom_filters %}

<!DOCTYPE html>
<html>
<head>
<style>
body {
  font-family: arial, sans-serif;
  margin: 0;
  padding: 0;
}

.column {
  float: left;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.row::after {
  content: "";
  clear: both;
  display: table;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #dddddd;
}

h2 {
  text-align: center;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 4px;
}

.button1 {
    background-color: #008CBA;
}

.button:hover {
    background-color: #006e92;
}

.link-button {
    display: inline-block;
    padding: 5px 10px;
    font-size: 12px;
    background-color: #73b3f8;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    margin-right: 5px;
}

.link-button:hover {
    background-color: #0056b3;
}

.dataTables_filter, .dataTables_length {
    padding-bottom: 10px;
}

.dataTables_filter {
    float: left !important;
    text-align: left !important;
}

.dataTables_length {
    float: right !important;
    text-align: right !important;
}

@media screen and (max-width: 650px) {
  .column {
    width: 100%;
    display: block;
  }
}

.greyed-out {
  background-color: #f0f0f0;
  color: #888888;
}

/* Add this new style for the green button to match list_staff.html */
.green-button {
  background-color: #5dbea3;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;
  display: inline-block;
}

.green-button:hover {
  background-color: darkgreen;
}
</style>
</head>
<body>

<h2>Primary Care Providers</h2>

<!-- Process messages as JavaScript alerts instead of inline HTML -->
{% if messages %}
    <script>
        {% for message in messages %}
            alert("{{ message }}");
        {% endfor %}
    </script>
{% endif %}

<!-- Update the button placement and styling to match list_staff.html -->
<a href="{% url 'add_physician' %}" class="button green-button">+ Add New Primary Care Provider</a>

<label>
  <input type="checkbox" id="toggleActive" checked /> Show only active primary care providers
</label>

<div class="row">
  <div class="column">
    <table id="pcndatatable" class="display">
      <thead>
        <tr>
            <th>Last Name</th>
            <th>First Name</th>
            <th>Title</th>
            <th>Gender</th>
            <th>Prac ID</th>
            <th>Primary Email</th>
            <th>Email Opt-out</th>
            <th>Primary Phone</th>
            <th>Primary Care Provider Active</th>
            <th>Active In Clinic</th>
            <th>Date Signed</th>
            <th>Date Left</th>
            <th>Date Modified</th>
            <th>Date Created</th>
        </tr>
      </thead>    
      <tbody>
        {% for physician in physicians %}
          <tr {% if physician.date_left_eopcn %}class="greyed-out role-ended"{% endif %}>
            <td><a href="{% url 'physician_detail' physician.physician_id %}">{{ physician.last_name }}</a></td>
            <td><a href="{% url 'physician_detail' physician.physician_id %}">{{ physician.first_name }}</a></td>
            <td>{{ physician.title|default_if_none:"" }}</td>
            <td>{{ physician.gender|default_if_none:"" }}</td>
            <td>{{ physician.practitioner_id|default_if_none:"" }}</td>
            <td>{{ physician.primary_email|default_if_none:"" }}</td>
            <td>{% if physician.do_not_email %}Yes{% else %}No{% endif %}</td>
            <td>{{ physician.primary_phone|phone_format|default_if_none:"" }}</td>
            <td>{{ physician.physician_active }}</td>
            <td>{{ physician.active_in_clinic|default_if_none:"" }}</td>
            <td data-order="{{ physician.date_signed_eopcn|date:"Y-m-d" }}" >{{ physician.date_signed_eopcn|date:"M. j, Y" }}</td>
            <td data-order="{{ physician.date_left_eopcn|date:"Y-m-d" }}" >{{ physician.date_left_eopcn|default_if_none:"" }}</td>
            <td data-order="{{ physician.date_modified|date:"Y-m-d" }}" >{{ physician.date_modified|date:"M. j, Y" }}</td>
            <td data-order="{{ physician.date_created|date:"Y-m-d" }}" >{{ physician.date_created|date:"M. j, Y" }}</td>
          </tr>
        {% endfor %}
      </tbody>    
    </table>
  </div>
</div>

{% load static %}
<script src="{% static 'eopcn_staff/js/datatables.js' %}"></script>

</body>
</html>
{% endblock %}
