{% extends "base.html" %}
{% load static %}

{% block title %}Administrative Lists{% endblock %}

{% block content %}
<link rel="stylesheet" type="text/css" href="{% static 'eopcn_staff/css/staff_form.css' %}">

<div class="form-container">
    <h2 class="subheading">Administrative Lists</h2>
    <p style="text-align: center; color: #6c757d; margin-bottom: 2rem;">Access and manage various administrative lists and configurations</p>

    <div class="lists-grid">
        <!-- People & Roles Section -->
        <div class="list-section">
            <h3 class="section-title">People & Roles</h3>
            <div class="list-items">
                <a href="{% url 'program_list' %}" class="list-item">
                    <div class="list-item-content">
                        <div class="list-item-title">EOPCN Team List</div>
                        <div class="list-item-description">Manage EOPCN team members and departments</div>
                    </div>
                </a>
                
                <a href="{% url 'supervisor_list' %}" class="list-item">
                    <div class="list-item-content">
                        <div class="list-item-title">Supervisors List</div>
                        <div class="list-item-description">View and manage supervisor information</div>
                    </div>
                </a>
                
                <a href="{% url 'staff_roles_list' %}" class="list-item">
                    <div class="list-item-content">
                        <div class="list-item-title">Staff Roles</div>
                        <div class="list-item-description">Configure available staff roles and positions</div>
                    </div>
                </a>
                
                <a href="{% url 'position_list' %}" class="list-item">
                    <div class="list-item-content">
                        <div class="list-item-title">Positions List</div>
                        <div class="list-item-description">Manage organizational positions</div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Programs & Services Section -->
        <div class="list-section">
            <h3 class="section-title">Programs & Services</h3>
            <div class="list-items">
                <a href="{% url 'service_list' %}" class="list-item">
                    <div class="list-item-content">
                        <div class="list-item-title">Program List</div>
                        <div class="list-item-description">Manage programs and service offerings</div>
                    </div>
                </a>
                
                <a href="{% url 'assignments_in_clinic_list' %}" class="list-item">
                    <div class="list-item-content">
                        <div class="list-item-title">Clinic Assignments</div>
                        <div class="list-item-description">View clinic assignment configurations</div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Staff Management Section -->
        <div class="list-section">
            <h3 class="section-title">Staff Management</h3>
            <div class="list-items">
                <a href="{% url 'staff_leaves' %}" class="list-item">
                    <div class="list-item-content">
                        <div class="list-item-title">Staff Leaves</div>
                        <div class="list-item-description">Track and manage staff leave requests</div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Communication Section -->
        <div class="list-section">
            <h3 class="section-title">Communication</h3>
            <div class="list-items">
                <a href="{% url 'email_groups_list' %}" class="list-item">
                    <div class="list-item-content">
                        <div class="list-item-title">Email Groups</div>
                        <div class="list-item-description">Manage email distribution groups</div>
                    </div>
                </a>
                
                <a href="{% url 'email_recipients_list' %}" class="list-item">
                    <div class="list-item-content">
                        <div class="list-item-title">Email Recipients</div>
                        <div class="list-item-description">Configure email recipient settings</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.lists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 1.5rem;
}

.list-section {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
    text-align: center;
}

.list-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.list-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.list-item:hover {
    background: #e9ecef;
    border-color: #0067b1;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(0,0,0,0.1);
    text-decoration: none;
    color: inherit;
}

.list-item-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    width: 40px;
    text-align: center;
    flex-shrink: 0;
}

.list-item-content {
    flex: 1;
}

.list-item-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.list-item-description {
    font-size: 0.875rem;
    color: #6c757d;
    line-height: 1.4;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .lists-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .list-section {
        padding: 1rem;
    }
    
    .list-item {
        padding: 0.75rem;
    }
    
    .list-item-icon {
        font-size: 1.25rem;
        margin-right: 0.75rem;
        width: 35px;
    }
}

@media (max-width: 480px) {
    .list-item {
        flex-direction: column;
        text-align: center;
    }
}
</style>
{% endblock %}
