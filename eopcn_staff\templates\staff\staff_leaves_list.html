{% extends "base.html" %}

{% block content %}

<style>
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.column {
  float: left;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.row::after {
  content: "";
  clear: both;
  display: table;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
  table-layout: fixed;
}

th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #f2f2f2;
}

h2 {
  text-align: center;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  cursor: pointer;
  border-radius: 4px;
}

.green-button {
  background-color: #5dbea3;
}

.green-button:hover {
  background-color: darkgreen;
}

.dataTables_filter, .dataTables_length {
  padding-bottom: 10px;
}

.dataTables_filter {
  float: left !important;
  text-align: left !important;
}

.dataTables_length {
  float: right !important;
  text-align: right !important;
}

@media screen and (max-width: 650px) {
  .column {
    width: 100%;
    display: block;
  }
}
</style>

<h2>Staff Leaves List</h2>

{% if request.GET.active_leave == 'true' %}
    <p>Filtering by leave type: <strong>Active Leaves</strong></p>
{% endif %}



<div class="row">
  <div class="column">
    <table id="pcndatatable" class="display">
      <thead>
        <tr>
            <th>Staff On Leave</th>
            <th>Covering Staff</th>
            <th>Leave Type</th>
            <th>Leave Start Date</th>
            <th>Return Date</th>
        </tr>
    </thead>    
    <tbody>
        {% for leave in staff_leaves %}
        <tr>
            <td>
                {% if leave.staff %}
                    <a href="{% url 'staff_detail' leave.staff.staff_id %}">
                        {{ leave.staff.first_name }} {{ leave.staff.last_name }}
                    </a>
                {% else %}
                    -
                {% endif %}
            </td>
            <td>
                {% if leave.coverages %}
                    {% for coverage in leave.coverages %}
                        {% if coverage.covering_staff %}
                            <a href="{% url 'staff_detail' coverage.covering_staff.staff_id %}">
                                {{ coverage.covering_staff.first_name }} {{ coverage.covering_staff.last_name }}
                                ({{ coverage.coverage_start_date }} - {{ coverage.coverage_end_date }})
                            </a>
                            <br>
                                {% if coverage.coverage_type %}
                                    <br><strong>Coverage Type:</strong> {{ coverage.coverage_type }}
                                {% endif %}
                          </div>
                        {% endif %}
                    {% endfor %}
                {% else %}
                    Not Assigned
                {% endif %}
            </td>
            <td>{{ leave.leave_type.leave_type_name }}</td>
            <td>{{ leave.leave_start_date }}</td>
            <td>{{ leave.return_date|default:"-" }}</td>
        </tr>
        {% endfor %}
    </tbody>
    </table>
  </div>
</div>

{% load static %}
<script src="{% static 'eopcn_staff/js/datatables.js' %}"></script>

{% endblock %}
