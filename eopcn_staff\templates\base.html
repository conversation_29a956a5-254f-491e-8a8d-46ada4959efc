<!DOCTYPE html>
<html lang="en">
{% load static %}
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}EOPCN Operational Database{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <!-- DataTables FixedHeader CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/fixedheader/3.1.9/css/fixedHeader.dataTables.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #0067b1;  /* Change the color here */
            color: white;
            padding: 10px 0;
            text-align: center;
        }
        header h1 {
            font-family: Arial, sans-serif; /* Apply Arial font to the title */
        }
        nav {
            margin: 10px;
            padding: 10px;
        }
        footer {
            background-color: #f9f9f9; /* Light grey background */
            padding: 10px; /* Padding around the text */
            text-align: center; /* Center text alignment */
            margin-top: auto; /* Pushes the footer to the bottom */
        }

        .container {
            flex: 1; /* Makes the container take up available space */
        }

        .back-button {
            background-color: #008CBA;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .back-button:hover {
            background-color: #006e92;
        }

        nav a {
            margin-right: 10px; /* Adds spacing between links */
            text-decoration: none; /* Removes underline for links */
        }
        
        .btn {
            padding: 5px 10px;
            border-radius: 4px;
        }
        
        /* Navigation buttons styling */
        .nav-buttons {
            display: flex;
            align-items: center;
            margin-right: 10px;
        }
        
        .nav-btn {
            background-color: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.25);
            border-radius: 3px;
            color: white;
            cursor: pointer;
            font-size: 13px;
            margin-right: 8px;
            padding: 5px 10px;
            display: flex;
            align-items: center;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
        }
        
        .nav-btn:hover {
            background-color: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.35);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }
        
        .nav-btn:active {
            background-color: rgba(255, 255, 255, 0.1);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .nav-btn svg {
            margin-right: 5px;
            fill: currentColor;
            width: 12px;
            height: 12px;
            opacity: 0.9;
        }
        
        /* Header layout with navigation buttons */
        .header-container {
            position: relative;
            background-color: #0067b1;
            padding: 10px 0;
            text-align: center; /* Center the logo */
        }
        
        /* Navigation buttons styling */
        .nav-buttons {
            position: absolute;
            bottom: 10px;
            left: 20px;
            display: flex;
            align-items: center;
            z-index: 10;
        }
        
        .nav-btn {
            background-color: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.25);
            border-radius: 3px;
            color: white;
            cursor: pointer;
            font-size: 13px;
            margin-right: 8px;
            padding: 5px 10px;
            display: flex;
            align-items: center;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
        }
        
        .nav-btn:hover {
            background-color: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.35);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }
        
        .nav-btn:active {
            background-color: rgba(255, 255, 255, 0.1);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .nav-btn svg {
            margin-right: 5px;
            fill: currentColor;
            width: 12px;
            height: 12px;
            opacity: 0.9;
        }
        
        /* Logo container to ensure it stays centered */
        .logo-container {
            display: inline-block;
            position: relative;
            z-index: 5;
        }
        
        /* Navigation styling */
    .main-nav {
        background-color: #f8f9fa;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        margin: 0;
        padding: 0;
    }
    
    .nav-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 20px;
        max-width: none; /* Remove max-width constraint */
        margin: 0;
        width: 100%; /* Ensure full width */
    }
    
    .nav-links {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 5px;
        justify-content: flex-start; /* Explicitly left-align */
        flex: 1; /* Take up available space */
    }
    
    .nav-link {
        padding: 8px 12px;
        border-radius: 4px;
        text-decoration: none;
        color: #333;
        font-weight: 500;
        transition: background-color 0.2s, color 0.2s;
    }
    
    .nav-link:hover {
        background-color: rgba(0, 103, 177, 0.1);
        color: #0067b1;
        text-decoration: none;
    }
    
    .nav-separator {
        display: none;  /* Hide the separators */
    }
    
    .user-section {
        display: flex;
        align-items: center;
        justify-content: flex-end; /* Right-align user section */
        flex-shrink: 0; /* Prevent shrinking */
        margin-left: auto; /* Push to the right edge */
        padding-right: 20px; /* Add right padding to create space from screen edge */
    }
    
    .welcome-text {
        margin-right: 15px;
        color: #555;
        font-size: 14px;
        white-space: nowrap; /* Prevent text wrapping */
    }
    
    .logout-link {
        padding: 6px 12px;
        border-radius: 4px;
        background-color: #f1f1f1;
        color: #333;
        text-decoration: none;
        font-size: 14px;
        transition: background-color 0.2s;
        white-space: nowrap; /* Prevent text wrapping */
    }
    
    .logout-link:hover {
        background-color: #e0e0e0;
        text-decoration: none;
    }
    
    @media (max-width: 992px) {
        .nav-links {
            gap: 2px;
        }
        
        .nav-link {
            padding: 6px 10px;
            font-size: 14px;
        }
    }
    
    @media (max-width: 768px) {
        .nav-container {
            flex-direction: column;
            align-items: stretch;
        }
        
        .nav-links {
            margin-bottom: 10px;
            justify-content: center;
        }
        
        .user-section {
            width: 100%;
            justify-content: center;
            padding-top: 10px;
            border-top: 1px solid #eee;
            margin-left: 0;
        }
    }
    </style>

    <!-- Include jQuery first -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Include DataTables JS after jQuery -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <!-- DataTables FixedHeader JS -->
    <script src="https://cdn.datatables.net/fixedheader/3.1.9/js/dataTables.fixedHeader.min.js"></script>

    <!-- DataTables Buttons CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.7.1/css/buttons.dataTables.min.css">

    <!-- DataTables Buttons JS and dependencies -->
    <script src="https://cdn.datatables.net/buttons/1.7.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/1.7.1/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/1.7.1/js/buttons.print.min.js"></script>
    
</head>
<body>
    <header class="header-container">
        <!-- Navigation buttons on the left -->
        <div class="nav-buttons">
            <button class="nav-btn" onclick="goBack()" title="Go back to the previous page">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24">
                    <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                </svg>
                Back
            </button>
            <button class="nav-btn" onclick="goForward()" title="Go forward to the next page">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24">
                    <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                </svg>
                Forward
            </button>
        </div>
        
        <!-- Centered logo -->
        <div class="logo-container">
            <img src="{{ logo_url }}" alt="EOPCN Logo" style="width: 225px; height: auto;">
        </div>
    </header>    

    <nav class="main-nav">
        <div class="nav-container">
            <div class="nav-links">
                <a href="{% url 'home' %}" class="nav-link">Home</a>
                <span class="nav-separator">|</span>
                <a href="{% url 'list_staff' %}" class="nav-link">Staff</a>
                <span class="nav-separator">|</span>
                <a href="{% url 'physician_list' %}" class="nav-link">Primary Care Providers</a>
                <span class="nav-separator">|</span>
                <a href="{% url 'physician_panel_details' %}" class="nav-link">Primary Care Provider Panels</a>
                <span class="nav-separator">|</span>
                <a href="{% url 'physician_panel_master' %}" class="nav-link">Panel Master</a>
                <span class="nav-separator">|</span>
                <a href="{% url 'clinic_physician_list' %}" class="nav-link">Primary Care Providers In Clinics</a>
                <span class="nav-separator">|</span>
                <a href="{% url 'clinic_list' %}" class="nav-link">Clinics</a>
                <span class="nav-separator">|</span>
                <a href="{% url 'clinic_staff_list' %}" class="nav-link">Clinic Staff</a>
                <span class="nav-separator">|</span>
                <a href="{% url 'admin:index' %}" class="nav-link">Admin</a>
            </div>
            
            <div class="user-section">
                <span class="welcome-text">
                    Welcome, {{ request.user.get_full_name|default:request.user.username }}!
                </span>
                <a href="{% url 'logout' %}" class="logout-link">Logout</a>
            </div>
        </div>
    </nav>
    
    <div>
        {% block content %}
        <!-- Content from child templates goes here -->
        {% endblock %}
    </div>
    <footer>
        <p>&copy; 2024 EOPCN Operational Database</p>
    </footer>
{% comment %} 
   <!-- Include jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Include DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script>
        $(document).ready(function() {
            $('table').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "pageLength": 50,
                "order": [[0, 'asc']],
                "dom": '<"top"fp><"clear">rt<"bottom"l><"clear">'
            });
        });
    </script> {% endcomment %}

    <style>
        .dataTables_paginate {
            padding-bottom: 10px;
        }
    </style>
    
    <script>
        // Function to navigate back in browser history
        function goBack() {
            window.history.back();
        }
        
        // Function to navigate forward in browser history
        function goForward() {
            window.history.forward();
        }
    </script>
</body>
</html>
