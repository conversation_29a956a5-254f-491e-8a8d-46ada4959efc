#!/usr/bin/env python
"""
Comprehensive test script for all forms and pages in the EOPCN Operations App
"""
import os
import sys
import django
import requests
from datetime import datetime, timedelta

# Setup Django environment
sys.path.append('c:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'EOPCNOpApp.settings')
django.setup()

from django.test import Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.conf import settings
from eopcn_staff.models import *
from eopcn_staff.forms import *

class FormAndPageTester:
    def __init__(self):
        # Add 'testserver' to ALLOWED_HOSTS for testing
        if 'testserver' not in settings.ALLOWED_HOSTS:
            settings.ALLOWED_HOSTS.append('testserver')

        self.client = Client()
        self.base_url = 'http://localhost:8000'
        self.test_results = {}
        self.errors = []
        
    def log_result(self, test_name, success, message=""):
        """Log test results"""
        self.test_results[test_name] = {
            'success': success,
            'message': message,
            'timestamp': datetime.now()
        }
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
    def test_url_accessibility(self, url_name, url_kwargs=None, expected_status=200):
        """Test if a URL is accessible"""
        try:
            if url_kwargs:
                url = reverse(url_name, kwargs=url_kwargs)
            else:
                url = reverse(url_name)

            response = self.client.get(url)

            # For authentication-protected pages, 302 redirect to login is expected and OK
            if response.status_code == 302 and '/oauth2/login' in response.url:
                success = True
                message = f"Status: {response.status_code} (Redirect to login - Authentication required)"
            else:
                success = response.status_code == expected_status
                message = f"Status: {response.status_code}"

                if not success and response.status_code == 404:
                    message += " (URL not found)"
                elif not success and response.status_code == 500:
                    message += " (Server error)"

            self.log_result(f"URL: {url_name}", success, message)
            return success, response

        except Exception as e:
            self.log_result(f"URL: {url_name}", False, f"Exception: {str(e)}")
            return False, None
            
    def test_form_rendering(self, form_class, form_name):
        """Test if a form can be instantiated and rendered"""
        try:
            form = form_class()
            form_html = str(form)
            success = len(form_html) > 0
            message = f"Fields: {len(form.fields)}"
            
            self.log_result(f"Form: {form_name}", success, message)
            return success, form
            
        except Exception as e:
            self.log_result(f"Form: {form_name}", False, f"Exception: {str(e)}")
            return False, None

    def test_main_pages(self):
        """Test main navigation pages"""
        print("\n=== Testing Main Pages ===")
        
        main_urls = [
            ('home', None),
            ('list_staff', None),
            ('physician_list', None),
            ('clinic_list', None),
            ('physician_panel_details', None),
            ('physician_panel_master', None),
            ('clinic_physician_list', None),
            ('staff_leaves', None),
            ('lists', None),
            ('service_list', None),
            ('program_list', None),
            ('staff_roles_list', None),
            ('seating_map', None),
            ('clinic_staff_list', None),
            ('email_groups_list', None),
            ('email_recipients_list', None),
        ]
        
        for url_name, kwargs in main_urls:
            self.test_url_accessibility(url_name, kwargs)

    def test_add_forms_pages(self):
        """Test add form pages"""
        print("\n=== Testing Add Form Pages ===")
        
        add_urls = [
            ('add_staff_form', None),
            ('add_physician', None),
            ('add_clinic', None),
            ('add_service', None),
            ('add_program', None),
            ('add_staff_role', None),
            ('add_position', None),
            ('add_supervisor', None),
            ('add_email_group', None),
            ('add_email_recipient', None),
        ]
        
        for url_name, kwargs in add_urls:
            self.test_url_accessibility(url_name, kwargs)

    def test_forms_instantiation(self):
        """Test all form classes"""
        print("\n=== Testing Form Instantiation ===")
        
        forms_to_test = [
            (AddStaffForm, "AddStaffForm"),
            (StaffAssignmentForm, "StaffAssignmentForm"),
            (StaffLeaveForm, "StaffLeaveForm"),
            (StaffAllocationForm, "StaffAllocationForm"),
            (PhysicianForm, "PhysicianForm"),
            (ClinicForm, "ClinicForm"),
            (ClinicNoteForm, "ClinicNoteForm"),
            (PositionForm, "PositionForm"),
            (ServiceForm, "ServiceForm"),
            (ProgramForm, "ProgramForm"),
            (StaffRoleForm, "StaffRoleForm"),
            (StaffSupervisorForm, "StaffSupervisorForm"),
            (CommentForm, "CommentForm"),
            (EmailGroupForm, "EmailGroupForm"),
            (EmailRecipientForm, "EmailRecipientForm"),
            (EmailGroupMembershipForm, "EmailGroupMembershipForm"),
            (StaffLocationContactForm, "StaffLocationContactForm"),
            (StaffCoverageForm, "StaffCoverageForm"),
        ]
        
        for form_class, form_name in forms_to_test:
            self.test_form_rendering(form_class, form_name)

    def test_server_accessibility(self):
        """Test if the Django server is running and accessible"""
        print("\n=== Testing Server Accessibility ===")
        
        try:
            response = requests.get(self.base_url, timeout=5)
            success = response.status_code == 200
            message = f"Status: {response.status_code}"
            self.log_result("Server Accessibility", success, message)
            return success
        except requests.exceptions.ConnectionError:
            self.log_result("Server Accessibility", False, "Connection refused - server not running")
            return False
        except Exception as e:
            self.log_result("Server Accessibility", False, f"Exception: {str(e)}")
            return False

    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Comprehensive Forms and Pages Test")
        print("=" * 60)
        
        # Test server first
        if not self.test_server_accessibility():
            print("❌ Server is not accessible. Please start the Django server first.")
            return
            
        # Run all test categories
        self.test_main_pages()
        self.test_add_forms_pages()
        self.test_forms_instantiation()
        self.test_detail_pages_with_sample_data()
        self.test_edit_pages_with_sample_data()

        # Print summary
        self.print_summary()

    def test_detail_pages_with_sample_data(self):
        """Test detail pages with existing data"""
        print("\n=== Testing Detail Pages ===")

        try:
            # Test staff detail if staff exists
            staff = Staff.objects.first()
            if staff:
                self.test_url_accessibility('staff_detail', {'pk': staff.pk})
            else:
                self.log_result("Staff Detail", False, "No staff data found")

            # Test physician detail if physician exists
            physician = Physician.objects.first()
            if physician:
                self.test_url_accessibility('physician_detail', {'physician_id': physician.pk})
            else:
                self.log_result("Physician Detail", False, "No physician data found")

            # Test clinic detail if clinic exists
            clinic = Clinic.objects.first()
            if clinic:
                self.test_url_accessibility('clinic_detail', {'clinic_id': clinic.pk})
            else:
                self.log_result("Clinic Detail", False, "No clinic data found")

            # Test email group detail if email group exists
            email_group = EmailGroup.objects.first()
            if email_group:
                self.test_url_accessibility('email_group_detail', {'group_id': email_group.pk})
            else:
                self.log_result("Email Group Detail", False, "No email group data found")

        except Exception as e:
            self.log_result("Detail Pages", False, f"Exception: {str(e)}")

    def test_edit_pages_with_sample_data(self):
        """Test edit pages with existing data"""
        print("\n=== Testing Edit Pages ===")

        try:
            # Test staff edit if staff exists
            staff = Staff.objects.first()
            if staff:
                self.test_url_accessibility('edit_profile', {'pk': staff.pk})
            else:
                self.log_result("Staff Edit", False, "No staff data found")

            # Test physician edit if physician exists
            physician = Physician.objects.first()
            if physician:
                self.test_url_accessibility('edit_physician', {'physician_id': physician.pk})
            else:
                self.log_result("Physician Edit", False, "No physician data found")

            # Test clinic edit if clinic exists
            clinic = Clinic.objects.first()
            if clinic:
                self.test_url_accessibility('edit_clinic', {'clinic_id': clinic.pk})
            else:
                self.log_result("Clinic Edit", False, "No clinic data found")

            # Test service edit if service exists
            service = Service.objects.first()
            if service:
                self.test_url_accessibility('edit_service', {'pk': service.pk})
            else:
                self.log_result("Service Edit", False, "No service data found")

            # Test program edit if program exists
            program = Program.objects.first()
            if program:
                self.test_url_accessibility('edit_program', {'pk': program.pk})
            else:
                self.log_result("Program Edit", False, "No program data found")

        except Exception as e:
            self.log_result("Edit Pages", False, f"Exception: {str(e)}")

    def print_summary(self):
        """Print test results summary"""
        print("\n" + "=" * 60)
        print("📊 Test Results Summary:")

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests

        print(f"\nTotal Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if failed_tests > 0:
            print(f"\n⚠️ Failed Tests:")
            for test_name, result in self.test_results.items():
                if not result['success']:
                    print(f"   • {test_name}: {result['message']}")

        print(f"\n📋 Recommendations:")
        if failed_tests == 0:
            print("🎉 All tests passed! Your forms and pages are working correctly.")
        else:
            print("1. Check failed URLs for missing data or configuration issues")
            print("2. Verify database has sample data for testing detail/edit pages")
            print("3. Check Django URL patterns for any missing routes")
            print("4. Review form field configurations for any errors")

if __name__ == "__main__":
    tester = FormAndPageTester()
    tester.run_all_tests()
