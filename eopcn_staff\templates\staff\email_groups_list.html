{% extends "base.html" %}

{% block content %}
<style>
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
}

.column {
  float: left;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.row::after {
  content: "";
  clear: both;
  display: table;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #f2f2f2;
}

h2 {
  text-align: center;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 4px;
}

.button1 {
    background-color: #008CBA;
}

.button:hover {
    background-color: #006e92;
}

.green-button {
  background-color: #5dbea3;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;
  display: inline-block;
}

.green-button:hover {
    background-color: darkgreen;
}

.link-button {
    display: inline-block;
    padding: 5px 10px;
    font-size: 12px;
    background-color: #73b3f8;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    margin-right: 5px;
}

.link-button:hover {
    background-color: #0056b3;
}

.inactive {
  background-color: #f0f0f0;
  color: #888888;
}
</style>

<h2>Email Groups</h2>

{% if messages %}
    <script>
        {% for message in messages %}
            alert("{{ message }}");
        {% endfor %}
    </script>
{% endif %}

<a href="{% url 'add_email_group' %}" class="button green-button">+ Add New Email Group</a>
<a href="{% url 'email_recipients_list' %}" class="button button1">Manage Recipients</a>

<div class="row">
  <div class="column">
    <table id="emailGroupsTable" class="display">
      <thead>
        <tr>
            <th>Name</th>
            <th>Description</th>
            <th>Active</th>
            <th>Member Count</th>
            <th>Created</th>
            <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for group in email_groups %}
          <tr {% if not group.is_active %}class="inactive"{% endif %}>
            <td>{{ group.name }}</td>
            <td>{{ group.description|default:"" }}</td>
            <td>{% if group.is_active %}Yes{% else %}No{% endif %}</td>
            <td>{{ group.get_active_recipients.count }}</td>
            <td>{{ group.date_created|date:"M d, Y" }}</td>
            <td>
                <a href="{% url 'email_group_detail' group.group_id %}" class="link-button">View</a>
                <a href="{% url 'edit_email_group' group.group_id %}" class="link-button">Edit</a>
            </td>
          </tr>
        {% empty %}
          <tr>
            <td colspan="6">No email groups found.</td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<!-- Include jQuery and DataTables -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>

<script>
$(document).ready(function() {
    $('#emailGroupsTable').DataTable({
        "pageLength": 25,
        "order": [[ 0, "asc" ]]
    });
});
</script>

{% endblock %}
