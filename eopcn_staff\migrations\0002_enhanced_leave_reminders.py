# Generated migration for enhanced leave reminder system

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('eopcn_staff', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='LeaveReminderType',
            fields=[
                ('type_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('reminder_type', models.CharField(choices=[('before_leave', 'Before Leave Starts'), ('during_leave', 'During Leave'), ('before_return', 'Before Return Date'), ('overdue_return', 'Overdue Return'), ('custom', 'Custom Reminder')], max_length=20)),
                ('days_offset', models.IntegerField(help_text='Days before/after leave start (negative for before, positive for after)')),
                ('is_active', models.BooleanField(default=True)),
                ('description', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'leave_reminder_types',
            },
        ),
        migrations.CreateModel(
            name='LeaveReminder',
            fields=[
                ('reminder_id', models.AutoField(primary_key=True, serialize=False)),
                ('scheduled_datetime', models.DateTimeField()),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('sent_datetime', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('recipients', models.TextField(help_text='JSON list of email addresses')),
                ('custom_email_address', models.EmailField(blank=True, max_length=254, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.CharField(blank=True, max_length=50, null=True)),
                ('custom_email_group', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='eopcn_staff.EmailGroup')),
                ('reminder_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='eopcn_staff.LeaveReminderType')),
                ('staff_leave', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to='eopcn_staff.StaffLeave')),
            ],
            options={
                'db_table': 'leave_reminders',
            },
        ),
        migrations.AlterUniqueTogether(
            name='leavereminder',
            unique_together={('staff_leave', 'reminder_type')},
        ),
        # Insert default reminder types
        migrations.RunSQL(
            """
            INSERT INTO leave_reminder_types (name, reminder_type, days_offset, is_active, description) VALUES
            ('Leave Starting Soon', 'before_leave', -3, 1, 'Reminder sent 3 days before leave starts'),
            ('Return Date Verification', 'before_return', -1, 1, 'Reminder to verify return date 1 day before return'),
            ('Leave Status Check', 'during_leave', 7, 1, 'Check on leave status during extended leave'),
            ('Overdue Return Check', 'overdue_return', 1, 1, 'Check when staff member has not returned as scheduled');
            """,
            reverse_sql="DELETE FROM leave_reminder_types WHERE name IN ('Leave Starting Soon', 'Return Date Verification', 'Leave Status Check', 'Overdue Return Check');"
        ),
    ]
