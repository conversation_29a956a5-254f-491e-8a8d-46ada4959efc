#!/usr/bin/env python
"""
Check existing reminders and their timezone conversions
"""
import os
import sys
import django

# Setup Django environment
sys.path.append('c:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'EOPCNOpApp.settings')
django.setup()

from django.utils import timezone
import pytz
from eopcn_staff.models import StaffLeave

def check_existing_reminders():
    """Check all existing reminders and show their timezone conversions"""
    print("=== Checking Existing Leave Reminders ===")
    
    mst = pytz.timezone('US/Mountain')
    
    # Get all leaves with reminders
    leaves_with_reminders = StaffLeave.objects.filter(
        reminder_datetime__isnull=False
    ).order_by('reminder_datetime')
    
    print(f"Found {leaves_with_reminders.count()} leaves with reminders:")
    print()
    
    for leave in leaves_with_reminders:
        staff = leave.staff
        reminder_utc = leave.reminder_datetime
        reminder_mst = reminder_utc.astimezone(mst)
        
        # Check if reminder is due
        now_mst = timezone.now().astimezone(mst)
        is_due = reminder_mst <= now_mst
        is_sent = leave.reminder_sent
        
        status = "🔴 DUE" if is_due and not is_sent else "✅ SENT" if is_sent else "⏰ PENDING"
        
        print(f"Leave ID: {leave.leave_id}")
        print(f"Staff: {staff.first_name} {staff.last_name}")
        print(f"Stored (UTC): {reminder_utc}")
        print(f"Display (MST): {reminder_mst.strftime('%Y-%m-%d %I:%M %p %Z')}")
        print(f"Status: {status}")
        print(f"Sent: {is_sent}")
        print("-" * 50)

def show_timezone_info():
    """Show current timezone information"""
    print("=== Current Timezone Information ===")
    
    now_utc = timezone.now()
    mst = pytz.timezone('US/Mountain')
    now_mst = now_utc.astimezone(mst)
    
    print(f"Current UTC: {now_utc.strftime('%Y-%m-%d %I:%M %p %Z')}")
    print(f"Current MST: {now_mst.strftime('%Y-%m-%d %I:%M %p %Z')}")
    print(f"UTC Offset: {now_mst.utcoffset()}")
    print()

if __name__ == "__main__":
    show_timezone_info()
    check_existing_reminders()
    
    print("\n=== Summary ===")
    print("The timezone fix ensures that:")
    print("1. When you enter '12:26 PM' in the form, it's treated as MST time")
    print("2. It gets stored as the correct UTC time (18:26 if MST is UTC-6)")
    print("3. When you edit the leave, it displays back as '12:26 PM' MST")
    print("4. Reminders are triggered at the correct local time")
