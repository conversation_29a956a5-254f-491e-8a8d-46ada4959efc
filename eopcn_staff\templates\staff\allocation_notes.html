<!-- Allocations for this Role -->
<h5>Allocations for this Role</h5>
<div class="allocation-formset">
    {{ form.nested.management_form }} <!-- Management form for nested allocations -->
    {% for allocation_form in form.nested.forms %}

            <!-- Display Individual Form Errors -->
        {% if allocation_form.errors %}
        <div class="alert alert-danger">
            <ul>
            {% for field, errors in allocation_form.errors.items %}
                <li>{{ field }}: {{ errors|join:", " }}</li>
            {% endfor %}
            </ul>
        </div>
        {% endif %}
    
        <div class="allocation-form-row">
            <!-- Centralized/Decentralized -->
            <div class="form-group">
                <label>{{ allocation_form.centralized_vs_ric.label }}</label><br>
                {% for radio in allocation_form.centralized_vs_ric %}
                    <div class="form-check form-check-inline">
                        {{ radio.tag }}
                        <label class="form-check-label">{{ radio.choice_label }}</label>
                    </div>
                {% endfor %}
            </div>

            <!-- Days of the week -->
            <div class="form-group">
                <label>Allocated days:</label><br>
                <div class="form-check form-check-inline">
                    {{ allocation_form.monday }} {{ allocation_form.monday.label_tag }}
                </div>
                <div class="form-check form-check-inline">
                    {{ allocation_form.tuesday }} {{ allocation_form.tuesday.label_tag }}
                </div>
                <div class="form-check form-check-inline">
                    {{ allocation_form.wednesday }} {{ allocation_form.wednesday.label_tag }}
                </div>
                <div class="form-check form-check-inline">
                    {{ allocation_form.thursday }} {{ allocation_form.thursday.label_tag }}
                </div>
                <div class="form-check form-check-inline">
                    {{ allocation_form.friday }} {{ allocation_form.friday.label_tag }}
                </div>
            </div>

            <!-- Clinic Field -->
            <div class="form-group">
                {{ allocation_form.clinic.label_tag }} 
                {{ allocation_form.clinic }}
            </div>

            <!-- Assignment in Clinic Field -->
            <div class="form-group">
                {{ allocation_form.assignment_in_clinic.label_tag }} 
                {{ allocation_form.assignment_in_clinic }}
            </div>

            <!-- Program Field -->
            <div class="form-group">
                {{ allocation_form.program.label_tag }} 
                {{ allocation_form.program }}
            </div>

            <!-- FTE -->
            <div class="form-group">
                {{ allocation_form.fte.label_tag }} 
                {{ allocation_form.fte }}
            </div>

            <!-- Start Date -->
            <div class="form-group">
                {{ allocation_form.start_date.label_tag }} 
                {{ allocation_form.start_date }}
            </div>

            <!-- End Date -->
            <div class="form-group">
                {{ allocation_form.end_date.label_tag }} 
                {{ allocation_form.end_date }}
            </div>
        </div>
    {% endfor %}