#!/usr/bin/env python
"""
Create a test reminder that will trigger in 2 minutes with a real email address
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
sys.path.append('c:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'EOPCNOpApp.settings')
django.setup()

from django.utils import timezone
import pytz
from eopcn_staff.models import StaffLeave, StaffLeaveTypes, Staff

def create_test_reminder():
    """Create a test reminder that will trigger in 2 minutes"""
    try:
        # Get a staff member and leave type
        staff = Staff.objects.first()
        leave_type = StaffLeaveTypes.objects.first()
        
        if not staff or not leave_type:
            print("❌ Need staff member and leave type in database")
            return
        
        # Calculate time 2 minutes from now in MST
        mst = pytz.timezone('US/Mountain')
        now_mst = timezone.now().astimezone(mst)
        reminder_time_mst = now_mst + timedelta(minutes=2)
        
        print(f"Creating reminder for: {reminder_time_mst.strftime('%Y-%m-%d %I:%M %p %Z')}")
        
        # Convert to UTC for storage
        reminder_time_utc = reminder_time_mst.astimezone(pytz.UTC)
        
        # Create the test leave
        test_leave = StaffLeave.objects.create(
            staff=staff,
            leave_type=leave_type,
            leave_start_date=(now_mst + timedelta(days=7)).date(),
            return_date=(now_mst + timedelta(days=14)).date(),
            reminder_datetime=reminder_time_utc,
            reminder_sent=False,
            reminder_email_address='<EMAIL>',  # Use your real email
            date_created=timezone.now(),
            created_by='test_system',
            date_modified=timezone.now(),
            modified_by='test_system'
        )
        
        print(f"✅ Created test leave {test_leave.leave_id}")
        print(f"📧 Will send reminder to: <EMAIL>")
        print(f"⏰ Reminder time (UTC): {reminder_time_utc}")
        print(f"⏰ Reminder time (MST): {reminder_time_mst.strftime('%Y-%m-%d %I:%M %p %Z')}")
        
        print("\n🚀 To test:")
        print("1. Wait 2 minutes")
        print("2. Run: python manage.py send_leave_reminders")
        print("3. Check your email!")
        
        print("\n🔄 Or run the test scheduler:")
        print("python test_reminder_scheduler.py")
        
        return test_leave
        
    except Exception as e:
        print(f"❌ Error creating test reminder: {e}")
        return None

if __name__ == "__main__":
    print("📧 Creating Test Reminder")
    print("=" * 40)
    create_test_reminder()
