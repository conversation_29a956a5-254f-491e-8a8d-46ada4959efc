{% extends "base.html" %}
{% load static %}
{% load custom_tags %}

{% block title %}{{ supervisor.staff.first_name }} {{ supervisor.staff.last_name }}'s Team{% endblock %}

{% block content %}
<style>
  .program-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 30px;
  }

  .program-group {
    flex: 1 1 calc(50% - 20px); /* Two groups per row */
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  .program-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
    border-bottom: 2px solid #3f3f3f; /* Darker grey divider line */
    padding-bottom: 5px;
  }
  .role-title {
    font-size: 16px;
    font-weight: bold;
    margin: 15px 0 5px 0;
    color: #444;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
  }
  
  .staff-count {
    font-size: 14px;
    color: #666;
    font-weight: normal;
  }

  /* Adjusted table width to make it more compact */
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
    font-size: 16px;
    text-align: left;
    max-width: 100%;
  }

  th, td {
    padding: 8px;
    border: 1px solid #ddd;
  }

  th {
    background-color: #f4f4f4;
  }

  tr:nth-child(even) {
    background-color: #f9f9f9;
  }

  tr:hover {
    background-color: #f1f1f1;
  }

  .staff-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .supervisor-photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 10px;
  }

  .button {
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 4px;
  }

  .button1 {
    background-color: #6c757d;
  }

  .button1:hover {
    background-color: #5a6268;
  }

  .toggle-container {
    margin: 20px 0;
    text-align: left;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 15px;
  }

  .inactive-row {
    opacity: 0.6;
    background-color: #f0f0f0;
  }
  
  .program-summary {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
  }
  
  .program-tag {
    background-color: #e9ecef;
    color: #495057;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 14px;
  }

  .inactive-program {
    opacity: 0.7;
    background-color: #f5f5f5;
  }
  
  .inactive-program-tag {
    background-color: #f0f0f0;
    color: #888888;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 14px;
  }

  .inactive-role {
    opacity: 0.7;
    background-color: #f8f8f8;
    border-left: 3px solid #ccc;
  }
  
  .role-tag {
    display: inline-block;
    background-color: #e2e8f0;
    color: #4a5568;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin: 2px;
  }
  
  .inactive-role-tag {
    background-color: #f0f0f0;
    color: #888888;
  }

  /* No staff message styling */
  .no-staff {
    text-align: center;
    padding: 20px;
    font-style: italic;
    color: #777;
  }
  
  .on-leave-badge {
    display: inline-block;
    background-color: #ffc107; /* amber color */
    color: #212529;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 8px;
    white-space: nowrap;
  }
  
  .leave-info {
    font-size: 11px;
    color: #555;
    display: block;
    margin-top: 3px;
  }
  
  .status-details {
    font-size: 11px;
    color: #666;
    display: block;
    margin-top: 3px;
  }
  
  .inactive-status {
    background-color: #f0f0f0;
    color: #888;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
  }

  .position-number {
    font-size: 12px;
    color: #666;
    background-color: #f0f0f0;
    padding: 1px 5px;
    border-radius: 3px;
    margin-left: 5px;
    display: inline-block;
  }

  /* Supervisor hierarchy visualization styles */
  .supervisor-hierarchy {
    text-align: center;
    margin: 20px auto; /* Reduced top margin from 40px */
    max-width: 1200px;
  }
  
  .hierarchy-title {
    color: #0067b1; /* Changed to match the blue header color */
    font-size: 24px;
    margin-bottom: 15px; /* Reduced from 20px */
    text-transform: uppercase;
    letter-spacing: 1px;
  }
  
  .org-tree {
    position: relative;
    padding: 15px 0; /* Reduced padding */
  }
  
  .supervisor-node {
    display: inline-block;
    position: relative;
    margin: 0 auto 30px;
    z-index: 2;
  }
  
  .supervisor-card {
    background-color: #0067b1; /* Changed to match the blue header color */
    color: white;
    border-radius: 20px;
    padding: 15px 25px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    position: relative;
    display: inline-block;
    min-width: 220px; /* Slightly increased width */
  }
  
  .supervisor-card img {
    width: 100px; /* Increased from 80px */
    height: 100px; /* Increased from 80px */
    border-radius: 50%;
    border: 4px solid white;
    display: block;
    margin: 0 auto 15px;
    object-fit: cover;
  }
  
  .supervisor-card h3 {
    margin: 0;
    font-size: 22px; /* Increased font size */
    font-weight: 600;
  }
  
  .supervisor-card p {
    margin: 8px 0 0; /* Increased top margin */
    font-size: 16px; /* Increased font size */
  }
  
  /* Staff nodes organization by program/role */
  .staff-nodes {
    display: flex;
    flex-wrap: wrap;
    margin-top: 50px; /* Reduced from 80px */
    position: relative;
    justify-content: center;
  }
  
  .program-group {
    margin: 0 20px 40px;
    text-align: center;
  }
  
  .program-label {
    background-color: #f0f0f0;
    border-radius: 15px;
    padding: 8px 15px;
    margin-bottom: 25px;
    display: inline-block;
    font-weight: bold;
    color: #444;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .role-group {
    margin-bottom: 30px;
  }
  
  .role-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    border-bottom: 1px dashed #ccc;
    padding-bottom: 5px;
    font-weight: 600;
  }
  
  .role-staff-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
  }
  
  .staff-card {
    background-color: #f0f0f0;
    border-radius: 15px;
    padding: 10px;
    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
    width: 150px;
    height: 160px; /* Increased height to accommodate status badges */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s;
    position: relative; /* For positioning status badges */
    margin: 0 5px 10px; /* Added margin between cards */
  }
  
  .staff-card:hover {
    transform: translateY(-5px);
  }
  
  .staff-card.active-staff {
    border-left: 4px solid #50C3A5;
  }
  
  .staff-card.inactive-staff {
    opacity: 0.7;
  }
  
  .staff-card img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 8px;
  }
  
  .staff-card h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.2;
    color: #333;
  }
  
  .staff-card h4 a {
    color: #333;
    text-decoration: none;
  }
  
  .staff-card h4 a:hover {
    text-decoration: underline;
    color: #0056b3;
  }
  
  .staff-card p {
    margin: 3px 0 0;
    font-size: 12px;
    color: #666;
    line-height: 1.2;
  }
  
  .status-dates {
    font-size: 10px;
    color: #666;
    margin-top: 3px;
    font-style: italic;
  }
  
  /* Status badges for hierarchy visualization */
  .vis-status-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 10px;
    border-radius: 10px;
    padding: 2px 6px;
    color: white;
    font-weight: bold;
  }
  
  .vis-on-leave {
    background-color: #ffc107;
    color: #212529;
  }
  
  .vis-inactive {
    background-color: #dc3545;
  }
  
  .vis-position-number {
    font-size: 10px;
    color: #666;
    margin-top: 2px;
  }

  /* Connection lines */
  .org-tree::before {
    content: "";
    position: absolute;
    top: 130px;
    left: 50%;
    width: 2px;
    height: 60px;
    background-color: #ccc;
  }
  
  .connector-wrapper {
    position: absolute;
    top: 190px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #ccc;
  }
  
  .connector-line {
    position: absolute;
    top: 190px;
    width: 2px;
    background-color: #ccc;
    height: 50px;
  }
  
  /* Enhanced supervisor hierarchy visualization styles */
  .supervisor-card {
    background-color: #50C3A5;
    color: white;
    border-radius: 20px;
    padding: 15px 25px; /* Increased padding */
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    position: relative;
    display: inline-block;
    min-width: 200px; /* Increased min-width */
  }
  
  .supervisor-card img {
    width: 80px; /* Increased size from 60px */
    height: 80px; /* Increased size from 60px */
    border-radius: 50%;
    border: 4px solid white; /* Slightly thicker border */
    display: block;
    margin: 0 auto 15px; /* Increased bottom margin */
    object-fit: cover;
  }
  
  .supervisor-card h3 {
    margin: 0;
    font-size: 22px; /* Increased font size */
    font-weight: 600;
  }
  
  .supervisor-card p {
    margin: 8px 0 0; /* Increased top margin */
    font-size: 16px; /* Increased font size */
  }
  
  /* Staff nodes organization by program/role */
  .staff-nodes {
    display: flex;
    flex-wrap: wrap;
    margin-top: 80px; /* Increased space from supervisor */
    position: relative;
    justify-content: center;
  }
  
  .program-group {
    margin: 0 20px 40px;
    text-align: center;
  }
  
  .program-label {
    background-color: #f0f0f0;
    border-radius: 15px;
    padding: 8px 15px;
    margin-bottom: 25px;
    display: inline-block;
    font-weight: bold;
    color: #444;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .role-group {
    margin-bottom: 30px;
  }
  
  .role-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    border-bottom: 1px dashed #ccc;
    padding-bottom: 5px;
    font-weight: 600;
  }
  
  .role-staff-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
  }
  
  /* Leave type badge for visualization */
  .leave-type-badge {
    position: absolute;
    top: 30px; /* Position below the inactive/on-leave badge */
    right: 8px;
    font-size: 9px;
    border-radius: 10px;
    padding: 2px 6px;
    background-color: #17a2b8;
    color: white;
    font-weight: bold;
  }
  
  /* Program summary in supervisor card */
  .supervisor-programs-summary {
    margin-top: 10px;
    font-size: 12px;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
  }
  
  .supervisor-program-tag {
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    margin: 2px;
    font-size: 11px;
  }
  .inactive-badge {
    display: inline-block;
    background-color: #dc3545; /* red color */
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 8px;
    white-space: nowrap;
  }
    .assignment-ended-badge {
    display: inline-block;
    background-color: #6c757d; /* grey color */
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 8px;
    white-space: nowrap;
  }
  
  .future-end-badge {
    display: inline-block;
    background-color: #17a2b8; /* info blue color */
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 8px;
    white-space: nowrap;
  }
  
  /* Container for the main content to center it better */
  .content-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  /* Adjust table width to not stretch across whole screen */
  .program-group {
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
  }
  
  /* Improved name cell to be more prominent */
  .name-cell {
    font-weight: 500;
  }
  
  .name-cell a {
    color: #0067b1;
    text-decoration: none;
  }
  
  .name-cell a:hover {
    text-decoration: underline;
  }
  
  /* Status display as plain text */
  .status-text {
    font-size: 11px;
    color: #666;
    margin-top: 3px;
  }
  
  .active-text {
    color: #28a745;
  }
  
  .inactive-text {
    color: #dc3545;
  }

  .team-button {
    display: inline-block;
    background-color: #0067b1;
    color: white;
    font-size: 11px;
    padding: 3px 8px;
    border-radius: 3px;
    text-decoration: none;
    margin-left: 8px;
    white-space: nowrap;
    cursor: pointer;
  }
  
  .team-button:hover {
    background-color: #00508c;
    text-decoration: none;
    color: white;
  }
  
  /* Nested team styles */
  .nested-team {
    display: none;
    margin-top: 10px;
    border-left: 3px solid #50C3A5;
    padding-left: 15px;
  }
  
  .nested-team-title {
    font-size: 14px;
    font-weight: bold;
    color: #50C3A5;
    margin-bottom: 8px;
  }
  
  .nested-staff-list {
    margin-left: 10px;
  }
  
  .nested-staff-item {
    padding: 5px;
    margin-bottom: 5px;
    background-color: #f8f8f8;
    border-radius: 3px;
    display: flex;
    align-items: center;
  }
  
  .nested-staff-photo {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
  }
  
  .nested-staff-name {
    flex-grow: 1;
  }
  
  .nested-staff-name a {
    color: #0067b1;
    text-decoration: none;
  }
  
  .nested-staff-name a:hover {
    text-decoration: underline;
  }
  
  .nested-staff-position {
    font-size: 11px;
    color: #666;
    margin-left: 10px;
  }
  
  .nested-inactive-staff {
    opacity: 0.6;
    background-color: #f0f0f0;
  }

  .nested-team-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }
  
  .jump-to-team {
    background-color: #50C3A5;
    color: white;
    font-size: 11px;
    padding: 3px 8px;
    border-radius: 3px;
    text-decoration: none;
    white-space: nowrap;
  }
  
  .jump-to-team:hover {
    background-color: #3a9278;
    text-decoration: none;
    color: white;
  }

  .supervisor-position {
    font-size: 16px;
    color: #666;
    font-weight: normal;
  }
  
  /* Updated toggle container styles for better alignment */
  .toggle-container {
    margin: 20px 0;
    text-align: left;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 15px;
  }
</style>

<!-- Back to Staff List Button - Updated link -->
<a href="{% url 'list_staff' %}" class="button button1">Back to Staff List</a>

<!-- Content Container for better centering -->
<div class="content-container">
  <!-- Supervisor Section -->
  <div style="text-align: center; margin-top: 20px;">    {% if supervisor_photo_url %}
      <img src="{{ supervisor_photo_url }}" alt="{{ supervisor.staff.first_name }}" class="supervisor-photo">
    {% else %}
      <img src="{% static 'staff_photos/Blank-team-member-photo-800x800_R1RZez6.jpg' %}" alt="No Photo" class="supervisor-photo">
    {% endif %}
    <h2>
      <a href="{% url 'staff_detail' supervisor.staff.pk %}" style="color: #0067b1; text-decoration: none;">
        {{ supervisor.staff.first_name }} {{ supervisor.staff.last_name }}
      </a>
    </h2>
    <h3>{{ supervisor_role }}</h3>
    <p>Program(s) overseen by this supervisor:</p>
    <div class="program-summary">
      {% for program_name, roles in grouped_programs.items %}
        {% if program_has_active_staff|get_item:program_name %}
          <span class="program-tag program-tag-{{ program_name|slugify }}">{{ program_name }}</span>
        {% else %}
          <span class="inactive-program-tag program-tag-{{ program_name|slugify }}">{{ program_name }}</span>
        {% endif %}
      {% endfor %}
    </div>
  </div>
</div>

<!-- Move toggle with precise alignment to match tables below -->
<div class="content-container">
  <!-- Toggle aligned with table headers - positioned to match table edge -->
  <div style="padding-left: 150px; margin-bottom: 5px; margin-top: 20px;">
    <label style="font-size: 17px; display: flex; align-items: center;">
      <input type="checkbox" id="toggleActive" checked style="margin-right: 8px;" /> 
      Show only active staff and roles
    </label>
  </div>
  <!-- Grouped Programs -->  <div class="program-row">
    {% for program_name, roles in grouped_programs.items %}
    <div class="program-group {% if not program_has_active_staff|get_item:program_name %}inactive-program{% endif %} program-container-{{ program_name|slugify }}">
      <div class="program-title">{{ program_name }} <span class="staff-count">({{ roles|count_active_roles }} active)</span></div>
        {% for role_name, assignments in roles.items %}      <div class="role-section {% if not role_has_active_staff|get_item:program_name|get_item:role_name %}inactive-role{% endif %}">
        <div class="role-title">{{ role_name }} <span class="staff-count">({{ assignments|count_active_staff }} active)</span></div>
        <table>
          <thead>            <tr>
              <th>Photo</th>
              <th>Name</th>
              <th>Status</th>
            </tr>
          </thead>          <tbody>
            {% for assignment in assignments %}
            {% now "Y-m-d" as today_date %}
            <tr class="{% if not assignment.staff.currently_active or assignment.end_date and assignment.end_date|date:'Y-m-d' < today_date %}inactive-row{% endif %}">
              <td>
                {% if assignment.staff.photo_url %}
                  <img src="{{ assignment.staff.photo_url }}" alt="{{ assignment.staff.first_name }}" class="staff-photo">
                {% else %}
                  No Photo
                {% endif %}
              </td>
              <td class="name-cell">
                <a href="{% url 'staff_detail' assignment.staff.pk %}">
                  {{ assignment.staff.first_name }} {{ assignment.staff.last_name }}
                </a>
                {% if assignment.staff.is_supervisor %}
                  <span class="team-button" onclick="toggleTeam('team-{{ assignment.staff.supervisor_id }}')">View Team</span>
                  
                  <!-- Nested team container -->
                  <div id="team-{{ assignment.staff.supervisor_id }}" class="nested-team">
                    <div class="nested-team-header">
                      <div class="nested-team-title">{{ assignment.staff.first_name }}'s Team</div>
                      <a href="{% url 'supervisor_staff' assignment.staff.supervisor_id %}" class="jump-to-team">Full Team Page</a>
                    </div>
                    <div class="nested-staff-list">
                      {% if assignment.staff.supervised_staff %}
                        {% for supervised in assignment.staff.supervised_staff %}
                          <!-- Only grey out staff if they have ended assignments (with end dates) -->
                          <div class="nested-staff-item {% if supervised.has_ended_assignment %}nested-inactive-staff{% endif %}">
                            {% if supervised.photo_url %}
                              <img src="{{ supervised.photo_url }}" alt="{{ supervised.first_name }}" class="nested-staff-photo">
                            {% else %}
                              <div class="nested-staff-photo" style="background-color: #eee; display: flex; align-items: center; justify-content: center;">
                                <span style="font-size: 10px;">No Photo</span>
                              </div>
                            {% endif %}
                            <div class="nested-staff-name">
                              <a href="{% url 'staff_detail' supervised.staff_id %}">
                                {{ supervised.first_name }} {{ supervised.last_name }}
                              </a>
                              {% if supervised.has_ended_assignment %}
                                <span style="font-size: 10px; color: #dc3545;">(Inactive)</span>
                              {% endif %}
                              {% if supervised.on_leave %}
                                <span style="font-size: 10px; color: #ffc107;">(On Leave)</span>
                              {% endif %}
                            </div>
                            {% if supervised.position_number %}
                              <div class="nested-staff-position">{{ supervised.position_number }}</div>
                            {% endif %}
                          </div>
                        {% endfor %}
                      {% else %}
                        <div style="font-style: italic; color: #777;">No staff currently reporting to this supervisor</div>
                      {% endif %}
                    </div>
                  </div>
                {% endif %}                {% if assignment.staff.position_number %}
                  <span class="position-number">Position: {{ assignment.staff.position_number }}</span>
                {% endif %}                
                {% if not assignment.staff.currently_active %}
                  <span class="inactive-badge">Inactive</span>
                {% endif %}
                
                {% if assignment.end_date %}
                  {% now "Y-m-d" as today_date %}
                  {% if assignment.end_date|date:"Y-m-d" < today_date %}
                    <span class="assignment-ended-badge">Assignment Ended</span>
                  {% else %}
                    <span class="future-end-badge">Assignment Ends: {{ assignment.end_date|date:"M d, Y" }}</span>
                  {% endif %}
                {% endif %}
                
                {% if assignment.staff.on_leave %}
                  <span class="on-leave-badge">On Leave</span>
                  <span class="leave-info">
                    {{ assignment.staff.leave_info.leave_type }} 
                    (Since: {{ assignment.staff.leave_info.leave_start_date|date:"M d, Y" }})
                    {% if assignment.staff.leave_info.return_date %}
                      <br>(Returns: {{ assignment.staff.leave_info.return_date|date:"M d, Y" }})
                    {% else %}
                      <br><em>No return date specified</em>
                    {% endif %}
                  </span>
                {% endif %}
              </td>              <td>
                {% now "Y-m-d" as today_date %}
                {% if assignment.staff.currently_active and not assignment.end_date or assignment.end_date and assignment.end_date|date:"Y-m-d" >= today_date %}
                  <span class="status-text active-text">Active</span>
                {% else %}
                  <span class="status-text inactive-text">Inactive</span>
                {% endif %}
                <span class="status-details">
                  {% if assignment.start_date %}
                    Started role: {{ assignment.start_date|date:"M d, Y" }}
                  {% elif assignment.staff.start_date %}
                    Started: {{ assignment.staff.start_date|date:"M d, Y" }}
                  {% endif %}
                  
                  {% if assignment.end_date %}
                    {% if assignment.end_date|date:"Y-m-d" < today_date %}
                      <br>Assignment ended: {{ assignment.end_date|date:"M d, Y" }}
                    {% else %}
                      <br>Assignment ends: {{ assignment.end_date|date:"M d, Y" }}
                    {% endif %}
                  {% elif assignment.staff.end_date %}
                    <br>Left: {{ assignment.staff.end_date|date:"M d, Y" }}
                  {% endif %}
                </span>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      {% empty %}
      <div class="no-staff">No staff with specific roles in this program.</div>
      {% endfor %}
    </div>
    {% empty %}
    <div class="no-staff">No staff members found for this supervisor.</div>
    {% endfor %}
  </div>
</div>

<script>  // Toggle functionality for active/inactive staff, roles, and programs
  document.getElementById('toggleActive').addEventListener('change', function () {
    const showActive = this.checked;
    
    // Toggle individual inactive staff rows based on staff.currently_active or assignment end date
    const inactiveRows = document.querySelectorAll('tr.inactive-row');
    inactiveRows.forEach(row => {
      row.style.display = showActive ? 'none' : '';
    });
    
    // Toggle inactive roles
    const inactiveRoles = document.querySelectorAll('.inactive-role');
    inactiveRoles.forEach(role => {
      role.style.display = showActive ? 'none' : '';
    });
    
    // Toggle entire inactive program containers
    const inactiveProgramContainers = document.querySelectorAll('.inactive-program');
    inactiveProgramContainers.forEach(container => {
      container.style.display = showActive ? 'none' : '';
    });
    
    // Toggle program tags in the summary section
    const inactiveProgramTags = document.querySelectorAll('.inactive-program-tag');
    inactiveProgramTags.forEach(tag => {
      tag.style.display = showActive ? 'none' : '';
    });
    
    // Toggle inactive staff in nested team lists
    const nestedInactiveStaff = document.querySelectorAll('.nested-inactive-staff');
    nestedInactiveStaff.forEach(item => {
      item.style.display = showActive ? 'none' : '';
    });
  });

  // Trigger the toggle on page load to hide inactive rows and programs
  document.getElementById('toggleActive').dispatchEvent(new Event('change'));

  // Function to toggle team visibility
  function toggleTeam(teamId) {
    const teamElement = document.getElementById(teamId);
    if (teamElement.style.display === 'block') {
      teamElement.style.display = 'none';
    } else {
      teamElement.style.display = 'block';
    }
  }
</script>
{% endblock %}
