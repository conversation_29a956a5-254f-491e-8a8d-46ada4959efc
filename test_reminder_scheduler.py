#!/usr/bin/env python
"""
Simple test scheduler that checks for reminders every 2 minutes
Use this for testing - run it in the background while testing reminders
"""
import os
import sys
import subprocess
import time
from datetime import datetime

# Setup paths
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.append(PROJECT_ROOT)

def run_reminder_check():
    """Run the reminder check command"""
    try:
        os.chdir(PROJECT_ROOT)
        
        cmd = [sys.executable, 'manage.py', 'send_leave_reminders']
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        if result.returncode == 0:
            print(f"[{timestamp}] ✅ Reminder check completed successfully")
            if result.stdout:
                # Only show important lines
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'Processed' in line or 'Sent' in line or 'Failed' in line:
                        print(f"[{timestamp}]    {line}")
        else:
            print(f"[{timestamp}] ❌ Reminder check failed")
            print(f"[{timestamp}]    Error: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print(f"[{timestamp}] ⏰ Reminder check timed out")
    except Exception as e:
        print(f"[{timestamp}] 💥 Error: {e}")

def main():
    """Main scheduler loop"""
    print("🚀 Starting Test Reminder Scheduler")
    print("📧 Checking for reminders every 2 minutes...")
    print("⏹️  Press Ctrl+C to stop")
    print("=" * 50)
    
    try:
        while True:
            run_reminder_check()
            print(f"😴 Sleeping for 2 minutes...")
            print()
            time.sleep(120)  # Wait 2 minutes
            
    except KeyboardInterrupt:
        print("\n🛑 Scheduler stopped by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")

if __name__ == "__main__":
    main()
