{% extends "base.html" %}

{% block content %}

<style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
    }
    
    .column {
      float: left;
      width: 100%;
      padding: 16px;
      box-sizing: border-box;
    }
    
    .row::after {
      content: "";
      clear: both;
      display: table;
    }
    
    table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 16px;
    }
    
    th, td {
      border: 1px solid #dddddd;
      text-align: left;
      padding: 8px;
    }
    
    tr:nth-child(even) {
      background-color: #f2f2f2;
    }
    
    h2 {
      text-align: center;
    }
    
    .button {
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      cursor: pointer;
      border-radius: 4px;
    }
    
    .button1 {
        background-color: #008CBA;
    }
    
    .button:hover {
        background-color: #006e92;
    }
    
    .link-button {
        display: inline-block;
        padding: 5px 10px;
        font-size: 12px;
        background-color: #73b3f8;
        color: white;
        text-decoration: none;
        border-radius: 3px;
        margin-right: 5px;
    }
    
    .link-button:hover {
        background-color: #0056b3;
    }
    
    .dataTables_filter, .dataTables_length {
        padding-bottom: 10px;
    }
    
    .dataTables_filter {
        float: left !important;
        text-align: left !important;
    }
    
    .dataTables_length {
        float: right !important;
        text-align: right !important;
    }
    
    .button-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .center-dropdown {
        flex: 1;
        text-align: center;
    }
    
    .left-buttons {
        flex: 0 0 auto;
    }
    
    @media screen and (max-width: 650px) {
      .column {
        width: 100%;
        display: block;
      }
      .button-container {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    .greyed-out {
        background-color: #f0f0f0;
        color: #888888;
      }

    </style>

    <h2>Supervisor List</h2>
    {% if messages %}
        <script>
            {% for message in messages %}
                alert("{{ message }}");
            {% endfor %}
        </script>
    {% endif %}
    
    <div class="button-container">
        <div class="left-buttons">
            <a href="{% url 'add_supervisor' %}" class="btn btn-secondary">+ Add New Supervisor</a>
        </div>
        <label>
            <input type="checkbox" id="toggleActive" checked /> Show only active positions
        </label>
    </div>
    
    <table id="pcndatatable" class="display">
        <thead>
            <tr>
                <th>Supervisor Name</th>
                <th>Role</th>
                <th>Program</th>
                <th>Active Status</th>
                <th>Date Created</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for supervisor in supervisors %}
            <tr class="{% if not supervisor.staff.active_assignments %}greyed-out role-ended{% endif %}">
                <td>{{ supervisor.staff.first_name }} {{ supervisor.staff.last_name }}</td>
                <td>
                    {% if supervisor.staff.active_assignments %}
                        {{ supervisor.staff.active_assignments.0.role.role_name }}
                    {% else %}
                        No Active Assignment
                    {% endif %}
                </td>
                <td>
                    {% if supervisor.staff.active_assignments and supervisor.staff.active_assignments.0.service %}
                        {{ supervisor.staff.active_assignments.0.service.service_name }}
                    {% else %}
                        No Service Assigned
                    {% endif %}
                </td>
                <td>
                    {% if supervisor.staff.active_assignments %}
                        Active
                    {% else %}
                        Inactive
                    {% endif %}
                </td>
                <td>{{ supervisor.date_created|date:"M d, Y" }}</td>
                <td>
                    <a href="{% url 'edit_supervisor' supervisor.supervisor_id %}" class="btn btn-primary">Edit</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    {% load static %}
    <script src="{% static 'eopcn_staff/js/datatables.js' %}"></script>
    


{% endblock %}
    