-- Query to find active clinics as of March 31, 2025
SELECT 
    c.clinic_id,
    c.clinic_name, 
    c.street_address,
    c.floor_unit_room,
    c.city,
    c.business_phone,
    c.include_on_eopcn_website,
    c.primary_contact,
    c.primary_contact_role,
    c.primary_contact_phone,
    c.primary_contact_email,    c.clinic_website,
    COUNT(DISTINCT CASE 
        WHEN (p.date_active_in_clinic <= '2025-03-31' AND 
             (p.date_left_clinic IS NULL OR p.date_left_clinic > '2025-03-31'))
        THEN p.physician_id 
        ELSE NULL 
    END) AS active_physician_count_march_2025
FROM 
    mh_clinics c
LEFT JOIN 
    mh_clinics_physicians p
ON 
    c.clinic_id = p.clinic_id
GROUP BY 
    c.clinic_id, 
    c.clinic_name, 
    c.street_address,
    c.floor_unit_room,
    c.city,
    c.business_phone,
    c.include_on_eopcn_website,
    c.primary_contact,
    c.primary_contact_role,
    c.primary_contact_phone,
    c.primary_contact_email,
    c.clinic_website;

-- To just get a count of active clinics (those with at least one active physician)
SELECT COUNT(*) AS active_clinic_count_march_2025
FROM (
    SELECT 
        c.clinic_id,        COUNT(DISTINCT CASE 
            WHEN (p.date_active_in_clinic <= '2025-03-31' AND 
                 (p.date_left_clinic IS NULL OR p.date_left_clinic > '2025-03-31'))
            THEN p.physician_id 
            ELSE NULL 
        END) AS active_physician_count_march_2025
    FROM 
        mh_clinics c
    LEFT JOIN 
        mh_clinics_physicians p
    ON 
        c.clinic_id = p.clinic_id
    GROUP BY 
        c.clinic_id
) AS clinics_with_counts
WHERE active_physician_count_march_2025 > 0;
