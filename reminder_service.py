#!/usr/bin/env python
"""
Windows service for running leave reminders automatically
This runs continuously and checks for reminders at scheduled times
"""
import os
import sys
import time
import schedule
import subprocess
import logging
from datetime import datetime

# Setup paths
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
sys.path.append(PROJECT_ROOT)

# Setup logging
log_file = os.path.join(PROJECT_ROOT, 'logs', 'reminder_service.log')
os.makedirs(os.path.dirname(log_file), exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def run_reminders():
    """Run the reminder system"""
    try:
        logger.info("Running scheduled leave reminders...")
        
        os.chdir(PROJECT_ROOT)
        cmd = [sys.executable, 'schedule_leave_reminders.py']
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=600  # 10 minute timeout
        )
        
        if result.returncode == 0:
            logger.info("Leave reminders completed successfully")
            if result.stdout:
                logger.info(f"Output: {result.stdout}")
        else:
            logger.error(f"Leave reminders failed: {result.stderr}")
            
    except Exception as e:
        logger.error(f"Error running reminders: {e}")

def main():
    """Main service loop"""
    logger.info("Starting Leave Reminder Service")
    logger.info("Scheduling daily reminders for 9:00 AM")
    
    # Schedule the job to run daily at 9:00 AM
    schedule.every().day.at("09:00").do(run_reminders)
    
    # You can add multiple times if needed:
    # schedule.every().day.at("13:00").do(run_reminders)  # 1 PM
    # schedule.every().day.at("17:00").do(run_reminders)  # 5 PM
    
    logger.info("Service started. Press Ctrl+C to stop.")
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
            
    except KeyboardInterrupt:
        logger.info("Service stopped by user")
    except Exception as e:
        logger.error(f"Service error: {e}")

if __name__ == "__main__":
    # Install schedule if not present
    try:
        import schedule
    except ImportError:
        print("Installing required package...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'schedule'])
        import schedule
    
    main()
