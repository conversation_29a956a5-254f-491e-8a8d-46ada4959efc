#!/usr/bin/env python
"""
Comprehensive test script for the enhanced leave reminder system
"""
import os
import sys
import django
from datetime import datetime, timedelta, date

# Setup Django environment
sys.path.append('c:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'EOPCNOpApp.settings')
django.setup()

from django.utils import timezone
import pytz
from eopcn_staff.models import StaffLeave, StaffLeaveTypes, Staff, EmailGroup

def test_legacy_system():
    """Test the legacy reminder system"""
    print("=== Testing Legacy Reminder System ===")

    try:
        # Get a staff member to test with
        staff = Staff.objects.first()
        if not staff:
            print("No staff members found in database")
            return None

        print(f"Using staff: {staff.first_name} {staff.last_name}")

        # Get a leave type
        leave_type = StaffLeaveTypes.objects.first()
        if not leave_type:
            print("No leave types found in database")
            return None

        print(f"Using leave type: {leave_type.leave_type_name}")

        # Create a test leave with reminder in 1 minute
        mst = pytz.timezone('US/Mountain')
        now_mst = timezone.now().astimezone(mst)
        reminder_time = now_mst + timedelta(minutes=1)

        test_leave = StaffLeave.objects.create(
            staff=staff,
            leave_type=leave_type,
            leave_start_date=now_mst.date() + timedelta(days=7),
            return_date=now_mst.date() + timedelta(days=14),
            reminder_datetime=reminder_time.astimezone(pytz.UTC),
            reminder_sent=False,
            reminder_email_address='<EMAIL>',
            date_created=timezone.now(),
            created_by='test_user',
            date_modified=timezone.now(),
            modified_by='test_user'
        )

        print(f"✅ Created legacy test leave with ID: {test_leave.leave_id}")
        print(f"   Reminder set for: {reminder_time} MST")
        return test_leave

    except Exception as e:
        print(f"❌ Error creating legacy test leave: {e}")
        return None

def test_new_system():
    """Test the new reminder system"""
    print("\n=== Testing New Reminder System ===")

    try:
        # Import new models (may not exist if migrations haven't run)
        from eopcn_staff.models import LeaveReminder, LeaveReminderType

        # Check if reminder types exist
        reminder_types = LeaveReminderType.objects.all()
        print(f"✅ Found {reminder_types.count()} reminder types")

        for rt in reminder_types:
            print(f"   - {rt.name} ({rt.get_reminder_type_display()}): {rt.days_offset} days")

        # Check existing reminders
        pending_reminders = LeaveReminder.objects.filter(status='pending')
        print(f"✅ Found {pending_reminders.count()} pending new reminders")

        return True

    except ImportError as e:
        print(f"⚠️  New reminder system not available: {e}")
        print("   Run migrations to enable the new system")
        return False
    except Exception as e:
        print(f"❌ Error testing new system: {e}")
        return False

def test_email_groups():
    """Test email group functionality"""
    print("\n=== Testing Email Groups ===")

    try:
        email_groups = EmailGroup.objects.filter(is_active=True)
        print(f"✅ Found {email_groups.count()} active email groups")

        for group in email_groups[:3]:  # Show first 3
            emails = group.get_active_emails()
            print(f"   - {group.name}: {len(emails)} members")

        return True

    except Exception as e:
        print(f"❌ Error testing email groups: {e}")
        return False

def check_pending_reminders():
    """Check for pending reminders in both systems"""
    print("\n=== Checking Pending Reminders ===")

    # Legacy system
    mst = pytz.timezone('US/Mountain')
    now_mst = timezone.now().astimezone(mst)

    legacy_leaves = StaffLeave.objects.filter(
        reminder_datetime__isnull=False,
        reminder_sent=False
    )

    print(f"Legacy system: {legacy_leaves.count()} pending reminders")
    for leave in legacy_leaves[:3]:  # Show first 3
        reminder_dt_mst = leave.reminder_datetime.astimezone(mst)
        is_due = reminder_dt_mst <= now_mst
        status = "🔴 DUE" if is_due else "⏰ PENDING"
        print(f"   {status} Leave {leave.leave_id}: {reminder_dt_mst}")

    # New system
    try:
        from eopcn_staff.models import LeaveReminder
        new_reminders = LeaveReminder.objects.filter(status='pending')
        print(f"New system: {new_reminders.count()} pending reminders")

        for reminder in new_reminders[:3]:  # Show first 3
            scheduled_mst = reminder.scheduled_datetime.astimezone(mst)
            is_due = reminder.is_due()
            status = "🔴 DUE" if is_due else "⏰ PENDING"
            print(f"   {status} {reminder.reminder_type.name}: {scheduled_mst}")

    except ImportError:
        print("New system: Not available (run migrations)")

def run_comprehensive_test():
    """Run all tests"""
    print("🚀 Starting Comprehensive Leave Reminder System Test")
    print("=" * 60)

    results = {
        'legacy_system': test_legacy_system() is not None,
        'new_system': test_new_system(),
        'email_groups': test_email_groups(),
    }

    check_pending_reminders()

    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")

    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name.replace('_', ' ').title()}: {status}")

    total_passed = sum(results.values())
    total_tests = len(results)

    print(f"\nOverall: {total_passed}/{total_tests} tests passed")

    if total_passed == total_tests:
        print("🎉 All tests passed! The reminder system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

    print("\n📋 Next Steps:")
    print("1. Run migrations if new system is not available:")
    print("   python manage.py migrate")
    print("2. Test sending reminders:")
    print("   python manage.py send_leave_reminders --dry-run")
    print("3. Create automatic reminders:")
    print("   python manage.py send_leave_reminders --create-auto-reminders")
    print("4. Set up scheduled task:")
    print("   python schedule_leave_reminders.py")

if __name__ == "__main__":
    run_comprehensive_test()
