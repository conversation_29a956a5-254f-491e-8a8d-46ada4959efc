{% extends "base.html" %}

{% block title %}Staff List{% endblock %}

{% block content %}
<h1>Staff List</h1>

{% if staff_members %}
    <h2>Search Results</h2>
    <table id="staff_table" class="display">
        <thead>
            <tr>
                <th>Name</th>
                <th>Start Date</th>
                <th>Currently Active</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for staff in staff_members %}
                <tr>
                    <td>{{ staff.staff_name }}</td>
                    <td>{{ staff.start_date }}</td>
                    <td>{{ staff.currently_active }}</td>
                    <td>
                        <a href="{% url 'edit_staff' staff.pk %}">Edit</a> | 
                        <a href="{% url 'delete_staff' staff.pk %}">Delete</a>
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
{% endif %}

<!-- Include DataTables CSS and JS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">
<script type="text/javascript" charset="utf8" src="https://code.jquery.com/jquery-3.5.1.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>

<script>
    $(document).ready(function() {
        $('#staff_table').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "pageLength": 50,
            "order": [[0, 'asc']],
            "dom": '<"top"f>rt<"bottom"lp><"clear">',
            "bDestroy": true
        });
    });
</script>

<style>
    body {
      font-family: arial, sans-serif;
      margin: 0;
      padding: 0;
    }

    .column {
      float: left;
      width: 100%;
      padding: 16px;
      box-sizing: border-box;
    }

    .row::after {
      content: "";
      clear: both;
      display: table;
    }

    table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 16px;
    }

    th, td {
      border: 1px solid #dddddd;
      text-align: left;
      padding: 8px;
    }

    tr:nth-child(even) {
      background-color: #dddddd;
    }

    h2 {
      text-align: center;
    }

    /* Align the search box to the left */
    .dataTables_filter {
      float: left !important;
      text-align: left !important;
    }

    .button {
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 4px;
    }

    .button1 {
        background-color: #0067b1;
    }

    .button:hover {
        background-color: #015081;
    }

    @media screen and (max-width: 650px) {
      .column {
        width: 100%;
        display: block;
      }
    }
</style>

{% endblock %}
