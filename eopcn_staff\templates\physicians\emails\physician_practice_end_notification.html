<!DOCTYPE html>
<html>
<head>
    <title>Primary Care Provider's Medical Practice End Date Information</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        table { width:70%; border-collapse: separate; border-spacing: 2px; }
        td { padding: 5px; }
        .label { text-align: right; width: 15%; }
        .value { background-color: #DDEBF7; width: 55%; }
    </style>
</head>
<body>
    {% load custom_filters %}
    <h1>Primary Care Provider's Medical Practice End Date Information</h1>
    <table border="0" cellspacing="2" style="width:70%">
        <tr>
            <td class="label">Name:</td>
            <td class="value">{{ physician.title }} {{ physician.last_name }}, {{ physician.first_name }}</td>
        </tr>
        <tr>
            <td class="label">Primary Care Provider is:</td>
            <td class="value">{{ physician.reason_for_leaving }}</td>
        </tr>
        <tr>
            <td class="label">End date:</td>
            <td class="value">{{ physician.date_no_longer_practicing }}</td>
        </tr>
    </table>
    
    {% if physician.comment %}
    <table border="0" cellspacing="2" style="width:70%">
        <tr>
            <td class="label">Additional details:</td>
            <td class="value">{{ physician.comment }}</td>
        </tr>
    </table>
    {% endif %}
    
    <br>
    <br>
    <p>Submitted by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).</p>
    {% if details_url %}
    <p>
        <a href="{{ details_url }}">View record in operational database</a>
    </p>
    {% endif %}
    <p>Best regards,</p>
    <p>EOPCN Automation Admin</p>
</body>
</html>
