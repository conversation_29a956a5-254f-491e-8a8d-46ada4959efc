{% extends "base.html" %}

{% block title %}Add New Clinic{% endblock %}

{% block content %}
{% load static %}
<link rel="stylesheet" type="text/css" href="{% static 'eopcn_staff/css/staff_form.css' %}">

<div class="form-container">
    <h2 class="subheading">Add New Clinic</h2>
    
    <form method="post">
        {% csrf_token %}
        
        <!-- Clinic Information Section -->
        <h3>Clinic Information</h3>
        <hr>
        
        <div class="form-group">
            <label for="{{ form.clinic_name.id_for_label }}">Clinic Name*:</label>
            {{ form.clinic_name }}
            {% if form.clinic_name.errors %}
            <div class="error">{{ form.clinic_name.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.med_group_or_site.id_for_label }}">Medical Group/Site:</label>
            {{ form.med_group_or_site }}
        </div>
        
        <div class="form-group">
            <label for="{{ form.street_address.id_for_label }}">Street Address:</label>
            {{ form.street_address }}
        </div>
        
        <div class="form-group">
            <label for="{{ form.floor_unit_room.id_for_label }}">Floor/Unit/Room:</label>
            {{ form.floor_unit_room }}
        </div>
        
        <div class="form-group">
            <label for="{{ form.city.id_for_label }}">City:</label>
            {{ form.city }}
        </div>
        
        <div class="form-group">
            <label for="{{ form.province.id_for_label }}">Province:</label>
            {{ form.province }}
        </div>
        
        <div class="form-group">
            <label for="{{ form.postal_code.id_for_label }}">Postal Code:</label>
            {{ form.postal_code }}
        </div>
        
        <div class="form-group">
            <label for="{{ form.business_phone.id_for_label }}">Business Phone:</label>
            {{ form.business_phone }}
        </div>
        
        <div class="form-group">
            <label for="{{ form.extension.id_for_label }}">Extension:</label>
            {{ form.extension }}
        </div>
        
        <div class="form-group">
            <label for="{{ form.fax.id_for_label }}">Fax:</label>
            {{ form.fax }}
        </div>
        
        <div class="form-group">
            <label for="{{ form.clinic_website.id_for_label }}">Clinic Website:</label>
            {{ form.clinic_website }}
        </div>
        
        <div class="form-group">
            <label for="{{ form.clinic_emr.id_for_label }}">Clinic EMR:</label>
            {{ form.clinic_emr }}
        </div>
        
        <div class="form-group">
            <label for="{{ form.pia_number.id_for_label }}">PIA Number:</label>
            {{ form.pia_number }}
        </div>
        
        <div class="checkbox-group">
            {{ form.include_on_eopcn_website }}
            <label for="{{ form.include_on_eopcn_website.id_for_label }}">Include on EOPCN Website</label>
        </div>
</div>

<div class="form-container">
    <!-- Contact Information Section -->
    <h3>Contact Information</h3>
    <hr>
    
    <h4>Primary Contact</h4>
    
    <div class="form-group">
        <label for="{{ form.primary_contact_first_name.id_for_label }}">First Name:</label>
        {{ form.primary_contact_first_name }}
        {% if form.primary_contact_first_name.errors %}
        <div class="error">{{ form.primary_contact_first_name.errors }}</div>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label for="{{ form.primary_contact_last_name.id_for_label }}">Last Name:</label>
        {{ form.primary_contact_last_name }}
        {% if form.primary_contact_last_name.errors %}
        <div class="error">{{ form.primary_contact_last_name.errors }}</div>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label for="{{ form.primary_contact_role.id_for_label }}">Role:</label>
        {{ form.primary_contact_role }}
    </div>
    
    <div class="form-group">
        <label for="{{ form.primary_contact_phone.id_for_label }}">Phone:</label>
        {{ form.primary_contact_phone }}
    </div>
    
    <div class="form-group">
        <label for="{{ form.primary_contact_ext.id_for_label }}">Extension:</label>
        {{ form.primary_contact_ext }}
    </div>
    
    <div class="form-group">
        <label for="{{ form.primary_contact_email.id_for_label }}">Email:</label>
        {{ form.primary_contact_email }}
    </div>
    
    <h4>Alternate Contact</h4>
    
    <div class="form-group">
        <label for="{{ form.alternate_contact_first_name.id_for_label }}">First Name:</label>
        {{ form.alternate_contact_first_name }}
        {% if form.alternate_contact_first_name.errors %}
        <div class="error">{{ form.alternate_contact_first_name.errors }}</div>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label for="{{ form.alternate_contact_last_name.id_for_label }}">Last Name:</label>
        {{ form.alternate_contact_last_name }}
        {% if form.alternate_contact_last_name.errors %}
        <div class="error">{{ form.alternate_contact_last_name.errors }}</div>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label for="{{ form.alternate_contact_role.id_for_label }}">Role:</label>
        {{ form.alternate_contact_role }}
    </div>
    
    <div class="form-group">
        <label for="{{ form.alternate_contact_phone.id_for_label }}">Phone:</label>
        {{ form.alternate_contact_phone }}
    </div>
    
    <div class="form-group">
        <label for="{{ form.alternate_contact_ext.id_for_label }}">Extension:</label>
        {{ form.alternate_contact_ext }}
    </div>
    
    <div class="form-group">
        <label for="{{ form.alternate_contact_email.id_for_label }}">Email:</label>
        {{ form.alternate_contact_email }}
    </div>
</div>

<!-- Email Group Widget -->
<div class="form-container">
    {% with form=form %}
        {% include 'staff/email_group_widget.html' %}
    {% endwith %}
</div>

<!-- Submit Buttons -->
<div class="form-container">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <button type="submit" class="btn btn-primary">Add Clinic</button>
        <a href="{% url 'clinic_list' %}" class="btn-secondary">Cancel</a>
    </div>
</div>
    </form>
{% endblock %}
