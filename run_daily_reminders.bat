@echo off
REM Daily Staff Leave Reminder Scheduler
REM This batch file runs the reminder system automatically

cd /d "C:\Users\<USER>\Py test\Python-testing\EOPCNOpApp"

:loop
REM Get current time
for /f "tokens=1-3 delims=:" %%a in ('time /t') do (
    set hour=%%a
    set minute=%%b
)

REM Remove leading space from hour if present
set hour=%hour: =%

REM Check if it's 9 AM (you can change this time)
if "%hour%"=="09" if "%minute%"=="00" (
    echo Running daily leave reminders at %time%
    python schedule_leave_reminders.py
    echo Reminders sent. Waiting until tomorrow...
    timeout /t 3600 /nobreak >nul
)

REM Wait 1 minute before checking again
timeout /t 60 /nobreak >nul
goto loop
