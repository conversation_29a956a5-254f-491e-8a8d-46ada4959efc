<!-- Email Group Selection Widget -->
<div class="email-notification-group">
    <div class="form-group mb-2">
        <!-- Email Guidance Message -->
        <div class="email-guidance-message mb-3">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>Email Notification Guidelines:</strong>
                <br>
                Please email the leadership team for <strong>new information</strong> to keep us synchronized and informed.
                However, for <strong>minor updates</strong> or <strong>fixing mistakes</strong>, there's no need to email the leadership team about those changes.
            </div>
        </div>

        <!-- Email Team Checkbox -->
        <div class="custom-control custom-checkbox mb-2">
            {% if form.email_leadership %}
                <input type="checkbox" class="custom-control-input" id="{{ form.email_leadership.id_for_label }}" name="{{ form.email_leadership.name }}" {% if form.email_leadership.value %}checked{% endif %}>
                <label class="custom-control-label" for="{{ form.email_leadership.id_for_label }}">{{ form.email_leadership.label }}</label>
            {% endif %}
        </div>
        
        <!-- Email Group Selection -->
        <label for="{{ form.email_group.id_for_label }}">{{ form.email_group.label }}:</label>
        {{ form.email_group }}
        {% if form.email_group.help_text %}
            <small class="form-text text-muted">{{ form.email_group.help_text }}</small>
        {% endif %}
        {% if form.email_group.errors %}
            <div class="alert alert-danger">
                {{ form.email_group.errors }}
            </div>
        {% endif %}
    </div>
    
    <!-- Email Group Info Display -->
    <div id="email-group-info" class="email-group-info mt-2" style="display: none;">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Selected Email Group Details</h6>
            </div>
            <div class="card-body">
                <div id="email-group-details">
                    <!-- Dynamic content will be loaded here -->
                </div>
                <div class="mt-2">
                    <a id="manage-email-groups-link" href="{% url 'email_groups_list' %}" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-external-link-alt"></i> Manage Email Groups
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.email-notification-group {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 15px;
    margin-bottom: 20px;
}

.email-guidance-message .alert {
    margin-bottom: 0;
    padding: 12px 15px;
    font-size: 0.9em;
    border-left: 4px solid #17a2b8;
}

.email-guidance-message .alert i {
    margin-right: 8px;
    color: #17a2b8;
}

.email-group-info {
    margin-top: 10px;
}

.email-group-info .card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.email-group-info .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
}

.email-group-info .card-body {
    padding: 0.75rem;
}

.member-list {
    max-height: 150px;
    overflow-y: auto;
    margin-top: 10px;
}

.member-item {
    padding: 2px 0;
    font-size: 0.9em;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Try multiple selectors to find the email group field
    const emailGroupSelect = document.querySelector('.email-group-selector') || 
                            document.querySelector('select[name="email_group"]') ||
                            document.querySelector('#id_email_group');
    const emailGroupInfo = document.getElementById('email-group-info');
    const emailGroupDetails = document.getElementById('email-group-details');
    const manageLink = document.getElementById('manage-email-groups-link');
    
    if (emailGroupSelect) {
        emailGroupSelect.addEventListener('change', function() {
            const selectedGroupId = this.value;
            
            if (selectedGroupId) {
                // Show loading state
                emailGroupInfo.style.display = 'block';
                emailGroupDetails.innerHTML = '<div class="loading-spinner"></div> Loading group details...';
                
                // Fetch group information using hardcoded URL with /eopcn/ prefix
                fetch(`/eopcn/email-groups/${selectedGroupId}/info/`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        emailGroupDetails.innerHTML = `<div class="alert alert-warning">${data.error}</div>`;
                    } else {
                        let membersHtml = '';
                        if (data.members && data.members.length > 0) {
                            membersHtml = '<div class="member-list"><strong>Members:</strong><ul class="list-unstyled mt-2">';
                            data.members.forEach(member => {
                                membersHtml += `<li class="member-item">• ${member.name} (${member.email})</li>`;
                            });
                            if (data.has_more_members) {
                                membersHtml += `<li class="member-item"><em>... and ${data.member_count - data.members.length} more members</em></li>`;
                            }
                            membersHtml += '</ul></div>';
                        } else {
                            membersHtml = '<div class="text-muted">No active members in this group.</div>';
                        }
                        
                        emailGroupDetails.innerHTML = `
                            <div>
                                <strong>${data.name}</strong>
                                <p class="text-muted mb-2">${data.description}</p>
                                <div class="mb-2">
                                    <span class="badge badge-info">${data.member_count} member${data.member_count !== 1 ? 's' : ''}</span>
                                </div>
                                ${membersHtml}
                            </div>
                        `;
                        
                        // Update manage link
                        manageLink.href = data.manage_url;
                    }
                })
                .catch(error => {
                    console.error('Error fetching email group info:', error);
                    emailGroupDetails.innerHTML = '<div class="alert alert-danger">Error loading group details. Please try again.</div>';
                });
            } else {
                // Hide info if no group selected
                emailGroupInfo.style.display = 'none';
            }
        });
        
        // Check if there's already a selected value on page load
        if (emailGroupSelect.value) {
            emailGroupSelect.dispatchEvent(new Event('change'));
        }
    }
});
</script>
