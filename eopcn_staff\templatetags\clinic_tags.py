from django import template

register = template.Library()

@register.filter
def change_for(changes, clinic_name):
    """
    Given a list of change_items (with .allocation_name, .changes) or
    a dict, return the matching .changes dict or {}.
    """
    if not changes:
        return {}
    # If changes is a dict (like physician fields), not clinic list
    if isinstance(changes, dict):
        return {}
    # Otherwise iterate
    for change_item in changes:
        # support both objects and dicts
        name = getattr(change_item, 'allocation_name',
                       change_item.get('allocation_name', None))
        if name == clinic_name:
            return getattr(change_item, 'changes',
                           change_item.get('changes', {})) or {}
    return {}
