<!DOCTYPE html>
<html>
<head>
    <title>Leave Record Updated for {{ staff_member.first_name }} {{ staff_member.last_name }}</title>
</head>
<body>
    <h2>Leave Record Updated for {{ staff_member.first_name }} {{ staff_member.last_name }}</h2>

    <p>Hello,</p>

    <p>The leave record for <strong>{{ staff_member.first_name }} {{ staff_member.last_name }}</strong> has been updated.</p>

    <p>Here are the updated leave details:</p>
    <h3>Leave Details</h3>
    <ul>
        {% if staff_leave.leave_type %}
        <li><strong{% if changes.leave_type %} style="background-color:#fff8c6;"{% endif %}>Leave Type:</strong>
            {% if changes.leave_type %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.leave_type.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.leave_type.new_value }}</span>
            {% else %}
                {{ staff_leave.leave_type }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_leave.leave_start_date %}
        <li><strong{% if changes.leave_start_date %} style="background-color:#fff8c6;"{% endif %}>Start Date:</strong>
            {% if changes.leave_start_date %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.leave_start_date.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.leave_start_date.new_value }}</span>
            {% else %}
                {{ staff_leave.leave_start_date }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_leave.return_date %}
        <li><strong{% if changes.return_date %} style="background-color:#fff8c6;"{% endif %}>End Date:</strong>
            {% if changes.return_date %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.return_date.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.return_date.new_value }}</span>
            {% else %}
                {{ staff_leave.return_date }}
            {% endif %}
        </li>
        {% endif %}
    </ul>



    <h3>Staff Coverage Details</h3>
    {% if staff_coverage %}
        <ul>
            {% for coverage in staff_coverage %}
                <li>
                    <strong>Covering Staff:</strong> {{ coverage.covering_staff|default:"None" }}<br>
                    {% if coverage.coverage_type %}
                    <strong>Coverage Type:</strong> {{ coverage.coverage_type }}<br>
                    {% endif %}
                    <strong>Coverage Period:</strong> {{ coverage.coverage_start_date|date:"Y-m-d"|default:"Not specified" }} to {{ coverage.coverage_end_date|date:"Y-m-d"|default:"Not specified" }}
                </li>
            {% endfor %}
        </ul>
    {% else %}
        <ul>
            <li>
                <i>No covering staff assigned.</i>
            </li>
        </ul>
    {% endif %}

    {% if comment %}
    <h3>Additional Comments</h3>
    <p><strong>Comment:</strong> <span style="background-color:#DDEBF7">{{ comment }}</span></p>
    {% endif %}

    {% if details_url %}
    <p>
        <a href="{{ details_url }}">View record in operational database</a>
    </p>
    {% endif %}
    <p>
        Submitted by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).
    </p>

    <p>Best regards,</p>
    <p>EOPCN Automation Admin</p>
</body>
</html>
