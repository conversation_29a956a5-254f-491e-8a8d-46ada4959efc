from django import template
from django.utils import timezone

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """
    Get an item from a dictionary using the key.
    This filter is needed for accessing dictionary values in templates
    by keys that may be variables.
    
    Usage: {{ my_dict|get_item:key_var }}
    """
    return dictionary.get(key)

@register.filter
def dictsumvalues(dictionary, attr=None):
    """
    Sum the lengths of all values in a dictionary.
    
    If attr is "length", returns the sum of the len() of each value.
    
    Usage: {{ my_dict|dictsumvalues:"length" }}
    """
    if attr == "length":
        return sum(len(value) for value in dictionary.values())
    return 0

@register.filter
def count_active_staff(assignments):
    """
    Count only active staff members in an assignment list.
    A staff member is considered active if:
    1. They are currently active (staff.currently_active is True)
    2. Their assignment has no end date OR the end date is in the future
    
    Usage: {{ assignments|count_active_staff }}
    """
    current_date = timezone.now().date()
    active_count = 0
    
    for assignment in assignments:
        if assignment.staff.currently_active and (assignment.end_date is None or assignment.end_date > current_date):
            active_count += 1
            
    return active_count

@register.filter
def count_active_roles(roles_dict):
    """
    Count total active staff across all roles in a program.
    
    Usage: {{ roles_dict|count_active_roles }}
    """
    current_date = timezone.now().date()
    total_active = 0
    
    for assignments in roles_dict.values():
        for assignment in assignments:
            if assignment.staff.currently_active and (assignment.end_date is None or assignment.end_date > current_date):
                total_active += 1
                
    return total_active
