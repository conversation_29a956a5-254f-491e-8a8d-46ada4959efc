{% extends "base.html" %}

{% block title %}Physician Panel Processing Results{% endblock %}

{% block content %}
<style>
body {
  font-family: arial, sans-serif;
  margin: 20px;
  padding: 0;
}
.container {
  max-width: 95%;
  margin: auto;
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}
h2, h3 {
  text-align: center;
  margin-bottom: 20px;
}
table {
  border-collapse: collapse;
  width: 100%;
  margin-top: 15px;
  margin-bottom: 25px;
}
th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}
th {
  background-color: #f2f2f2;
}
tr:nth-child(even) {
  background-color: #f9f9f9;
}
.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin-top: 20px;
  cursor: pointer;
  border-radius: 4px;
  background-color: #008CBA; /* Blue */
}
.button:hover {
  background-color: #006e92;
}
.messages {
    list-style: none;
    padding: 0;
    margin-bottom: 20px;
}
.messages li {
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
}
.messages li.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}
.messages li.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}
.messages li.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
.messages li.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}
</style>

<div class="container">
    <h2>Physician Panel Data Processing Results</h2>

    {% if messages %}
    <ul class="messages">
        {% for message in messages %}
        <li class="{{ message.tags }}">{{ message }}</li>
        {% endfor %}
    </ul>
    {% endif %}

    {% if processing_complete %}
        <h3>Summary</h3>
        <p><strong>{{ master_insert_count }}</strong> records were inserted into the <code>AH_physician_fourcut_panel_MASTER</code> table.</p>

        {% if unmapped_physicians %}
            <h3>Unmapped Physicians</h3>
            <p>The following physicians from the uploaded file could not be matched to an existing Physician ID in <code>mh_physicians</code>. These records were <strong>not</strong> inserted into the MASTER table. Please review, update the <code>mh_physicians</code> or <code>AH_physician_name_mapping</code> tables as needed, and re-process if necessary.</p>
            <table>
                <thead>
                    <tr>
                        {% for col_name in unmapped_columns %}
                            <th>{{ col_name }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for row in unmapped_physicians %}
                        <tr>
                            {% for cell in row %}
                                <td>{{ cell|default_if_none:"N/A" }}</td>
                            {% endfor %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <p>All physicians were successfully mapped and processed.</p>
        {% endif %}

        <h3>Final Staging Table Data Review</h3>
        <p>This is the state of the <code>AH_staging_physician_panel</code> table after all processing steps. Records with a <code>physician_id</code> were inserted into the MASTER table.</p>
        {% if final_staged_data %}
            <table>
                <thead>
                    <tr>
                        {% for col_name in final_staged_columns %}
                            <th>{{ col_name }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for row in final_staged_data %}
                        <tr>
                            {% for cell in row %}
                                <td>{{ cell|default_if_none:"NULL" }}</td>
                            {% endfor %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <p>No data found in the staging table for final review.</p>
        {% endif %}

        <a href="{% url 'physician_list' %}" class="button">Return to Physician List</a>
        <a href="{% url 'physician_panel_import' %}" class="button" style="background-color: #5cb85c; margin-left:10px;">Import Another File</a>

    {% elif not processing_complete and not messages %} 
        <p>No processing results to display. Please start by <a href="{% url 'physician_panel_import' %}">uploading a file</a>.</p>
    {% endif %}
</div>

{% endblock %}
