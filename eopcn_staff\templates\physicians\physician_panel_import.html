{% extends "base.html" %}

{% block title %}Import Physician Panel Data{% endblock %}

{% block content %}
<style>
body {
  font-family: arial, sans-serif;
  margin: 20px;
  padding: 0;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
}

.container {
  max-width: 800px;
  margin: auto;
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.steps ol {
  list-style-type: decimal;
  padding-left: 20px;
}

.steps li {
  margin-bottom: 10px;
  line-height: 1.6;
}

.file-upload-section {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background: #fff;
}

.file-upload-section h3 {
  margin-top: 0;
}

.file-upload-section p {
  font-size: 0.9em;
  color: #555;
}

.file-upload-section input[type="file"] {
  display: block;
  margin-bottom: 15px;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin-top: 10px;
  cursor: pointer;
  border-radius: 4px;
  background-color: #008CBA; /* Blue */
}

.button:hover {
  background-color: #006e92;
}

.note {
    font-style: italic;
    color: #777;
    margin-top: 5px;
}
</style>

<div class="container">
    <h2>Import Physician Panel Data</h2>

    <div class="steps">
        <h3>Import Process Overview:</h3>
        <ol>
            <li>
                <strong>Upload Data:</strong> You will upload an Excel file. The data from this file will be loaded into a temporary staging area.
                <p class="note">The Excel file's first sheet must have the headers <code>Last_Name</code>, <code>First_Name</code>, and <code>Total_Panel</code> in the first row.</p>
            </li>
            <li>
                <strong>Prepare Names:</strong> Physician names (<code>Last_Name</code>, <code>First_Name</code>) from your file will be combined into a standard format (e.g., "Doe, Jane").
            </li>
            <li>
                <strong>Name Standardization (if applicable):</strong> The combined names will be checked against an internal mapping table. If a known variation of a name exists, it will be standardized to the official EOPCN full name.
            </li>
            <li>
                <strong>Identify Physicians:</strong> Standardized names will be matched with the main physician database (<code>mh_physicians</code>) to retrieve their unique EOPCN <code>physician_id</code>.
            </li>
            <li>
                <strong>Import Panel Data:</strong> The <code>physician_id</code>, standardized <code>physician_name</code>, <code>Total_Panel_Size</code> from your file, along with the current report month and year, will be recorded in the <code>AH_physician_fourcut_panel_MASTER</code> table.
            </li>
        </ol>
    </div>

    <div class="file-upload-section">
        <h3>Upload Excel File</h3>
        <p>Please ensure your Excel file is formatted correctly before uploading. The first sheet should contain the data, with the first row being headers: <code>Last_Name</code>, <code>First_Name</code>, <code>Total_Panel</code>.</p>
        <form method="post" action="{% url 'process_physician_panel_upload' %}" enctype="multipart/form-data">
            {% csrf_token %}
            
            <div style="margin-bottom: 15px;">
                <label for="panel_file" style="display: block; margin-bottom: 5px; font-weight: bold;">Select Excel File:</label>
                <input type="file" name="panel_file" id="panel_file" accept=".xls,.xlsx" required>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label for="report_month" style="display: block; margin-bottom: 5px; font-weight: bold;">Report Month:</label>
                <select name="report_month" id="report_month" required style="padding: 5px; width: 150px;">
                    <option value="">Select Month</option>
                    <option value="1">January</option>
                    <option value="2">February</option>
                    <option value="3">March</option>
                    <option value="4">April</option>
                    <option value="5">May</option>
                    <option value="6">June</option>
                    <option value="7">July</option>
                    <option value="8">August</option>
                    <option value="9">September</option>
                    <option value="10">October</option>
                    <option value="11">November</option>
                    <option value="12">December</option>
                </select>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label for="report_year" style="display: block; margin-bottom: 5px; font-weight: bold;">Report Year:</label>
                <input type="number" name="report_year" id="report_year" required style="padding: 5px; width: 150px;" min="2015" max="2050" placeholder="e.g. 2025">
            </div>
            
            <button type="submit" class="button">Upload and Process File</button>
        </form>
    </div>
    
    <div style="margin-top: 20px; text-align: center;">
        <a href="{% url 'physician_name_mapping_list' %}" class="button button-blue">Manage Physician Name Mappings</a>
    </div>
</div>

{% endblock %}
