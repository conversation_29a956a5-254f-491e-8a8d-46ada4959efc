{% extends "base.html" %}

{% block title %}Edit Staff Member{% endblock %}

{% block content %}
{% load form_extras %}
{% load static %}

<div class="form-container">
    <h2 class="subheading">Role Assignment</h2>
    
    <!-- Page Purpose and Instructions -->
    <div class="info-section">
        <h3>About This Form</h3>
        <p>This section creates a new role assignment within the EOPCN organization - defining the staff member's hired position, supervisor, and FTE allocation.</p>
        
        <div class="important-notes">
            <h4>Important Guidelines:</h4>
            <ul>
                <li><strong>Single Active Role:</strong> Typically, only one role should be active at a time per staff member. Exception: Some contract employees may have multiple active contracts simultaneously, though this is rare.</li>
                <li><strong>Position Numbers:</strong> Only available position numbers are listed. When a position becomes inactive or ends, it becomes available for reassignment.</li>
                <li><strong>Supervisor Assignment:</strong> The supervisor field can be updated later if supervision changes.</li>
                <li><strong>Role Status:</strong> New assignments are typically set as active unless the staff member is starting at a future date.</li>
            </ul>
        </div>
    </div>

    <form id="editStaffForm" method="post" enctype="multipart/form-data">
        {% csrf_token %}

        <!-- Display Errors for Staff Form -->
        {% if form.errors %}
            <div class="alert alert-danger">
                <ul>
                {% for field, errors in form.errors.items %}
                    <li>{{ field }}: {{ errors|join:", " }}</li>
                {% endfor %}
                </ul>
            </div>
        {% endif %}
    </div>
        
    <div class="form-wrapper">
        <div class="form-container">
            <!-- Staff Role Assignment Section -->
            <h3>Add a New Staff Role Assignment</h3>
            <hr>
            <div class="form-group">
                {{ form.role.label_tag }} 
                {{ form.role }}
            </div>
            <div class="helper-text">
                <small class="form-text text-muted">ℹ️ Missing a role? <a href="{% url 'staff_roles_list' %}" target="_blank">Add roles here</a></small>
            </div>

            <div class="form-group">
                {{ form.supervisor.label_tag }} 
                {{ form.supervisor }}
            </div>
            <div class="helper-text">
                <small class="form-text text-muted">ℹ️ Missing a supervisor? <a href="{% url 'supervisor_list' %}" target="_blank">Add supervisors here</a></small>
            </div>
    
            <div class="form-group">
                {{ form.position.label_tag }} 
                {{ form.position }}
            </div>
            <div class="helper-text">
                <small class="form-text text-muted">ℹ️ Only available position numbers are listed. Missing a position? <a href="{% url 'position_list' %}" target="_blank">Check position availability here</a></small>
            </div>
    
            <div class="form-group">
                {{ form.permanent_vs_temporary.label_tag }}<br>
                {% for radio in form.permanent_vs_temporary %}
                    <div class="form-check form-check-inline">
                        {{ radio.tag }}
                        <label class="form-check-label">{{ radio.choice_label }}</label>
                    </div>
                {% endfor %}
            </div>        
    
            <div class="form-group">
                {{ form.role_fte.label_tag }} 
                {{ form.role_fte }}
            </div>
    
            <div class="form-group">
                {{ form.service.label_tag }} 
                {{ form.service }}
            </div>
            <div class="helper-text">
                <small class="form-text text-muted">ℹ️ Missing a program/team? <a href="{% url 'program_list' %}" target="_blank">Add programs here</a></small>
            </div>
    
            <div class="form-group">
                {{ form.start_date.label_tag }} 
                {{ form.start_date }}
            </div>
            
            <div class="form-group">
                {{ form.currently_active.label_tag }} 
                {{ form.currently_active }}
            </div>
            <div class="helper-text">
                <small class="form-text text-muted">ℹ️ Typically only one role should be active per staff member</small>
            </div>

        </div>
    </div>
    
<!-- Email Group Widget -->
<div class="form-container">
    {% with form=form %}
        {% include 'staff/email_group_widget.html' %}
    {% endwith %}
</div>

<div class="form-container">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <button type="submit" class="btn btn-primary mt-3">Save</button>
        <a href="{% url 'staff_detail' staff.pk %}" class="btn-secondary mt-3">Back to Staff Details</a>
    </div>
</div>
    </form>

    <script src="{% static 'eopcn_staff/js/edit_staff.js' %}"></script>

<style>
    .form-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .button-container {
        flex: 1;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border-radius: 8px;
        margin-top: 0px;
        margin-bottom: 20px;
    }

    .save-container {
        display: flex;
        justify-content: center;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .form-group {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .form-group label {
        width: 200px;
        margin-right: 10px;
        text-align: left;
    }

    .form-group input, 
    .form-group select, 
    .form-group textarea {
        flex: 1;
        padding: 5px;
        max-width: 100%;
    }

    button {
        width: auto;
        padding: 10px 20px;
    }

    a.btn-secondary {
        margin-left: 10px;
        padding: 10px 20px;
        text-decoration: none;
        background-color: #6c757d;
        color: white;
        border-radius: 4px;
    }

    .allocation-form-row {
        margin-bottom: 20px;
    }

    hr {
        margin: 10px 0;
        border: 0;
        border-top: 1px solid #ccc;
    }

    .allocation-heading {
    text-align: left; /* Centers the text horizontally */
    font-weight: bold;  /* Optional: makes the text bold */
    margin: 10px 0;     /* Adds vertical margin around the text */
    }

    .subheading {
    text-align: center; /* Center alignment */
    }

    .info-section {
        background-color: #e8f4fd;
        border: 1px solid #b8daff;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .info-section h3 {
        color: #004085;
        margin-bottom: 10px;
    }

    .important-notes {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 12px;
        margin-top: 15px;
    }

    .important-notes h4 {
        color: #856404;
        margin-bottom: 8px;
    }

    .important-notes ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .important-notes li {
        margin-bottom: 5px;
    }

    .form-text {
        font-size: 0.875em;
        color: #6c757d;
        margin-left: 10px;
    }

    .helper-text {
        margin-left: 210px;
        margin-top: -10px;
        margin-bottom: 15px;
    }

</style>

{% endblock %}