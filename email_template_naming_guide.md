# EOPCN Operations App - Email Template Naming Guide

**Quick Reference for Developers**

## 📁 Template Location Structure

```
eopcn_staff/templates/
├── staff/emails/           (Staff-related email notifications)
├── physicians/emails/      (Physician-related email notifications)
└── clinics/emails/         (Clinic-related email notifications)
```

## 🏷️ Naming Convention

### **Pattern:** `[entity]_[action]_notification.html`

### **Actions:**
- **`add`** - For new record notifications
- **`update`** - For record update notifications
- **`leave_add`** - For new leave notifications
- **`leave_update`** - For leave update notifications
- **`leave_reminder`** - For leave reminder notifications
- **`[specific_action]`** - For special actions (e.g., `practice_end`, `membership_end`)

## 📋 Current Email Templates

### **Staff Templates (`staff/emails/`)**
| Template Name | Purpose |
|---------------|---------|
| `staff_add_notification.html` | New staff member added |
| `staff_assignment_add_notification.html` | New staff assignment created |
| `staff_assignment_update_notification.html` | Staff assignment updated |
| `staff_allocation_update_notification.html` | Staff allocation updated |
| `staff_contact_update_notification.html` | Staff contact information updated |
| `staff_profile_update_notification.html` | Staff profile updated |
| `staff_leave_add_notification.html` | New staff leave created |
| `staff_leave_update_notification.html` | Staff leave updated |
| `staff_leave_reminder_notification.html` | Staff leave reminder |

### **Physician Templates (`physicians/emails/`)**
| Template Name | Purpose |
|---------------|---------|
| `physician_add_notification.html` | New physician added |
| `physician_update_notification.html` | Physician information updated |
| `physician_clinic_update_notification.html` | Physician clinic associations updated |
| `physician_practice_end_notification.html` | Physician no longer practicing |
| `physician_membership_end_notification.html` | Physician EOPCN membership ended |
| `physician_membership_end_group2_notification.html` | Physician membership end (group 2) |

### **Clinic Templates (`clinics/emails/`)**
| Template Name | Purpose |
|---------------|---------|
| `clinic_add_notification.html` | New clinic added |
| `clinic_update_notification.html` | Clinic information updated |

## 💡 Usage Examples

### **In Django Views:**
```python
# Staff notifications
send_email(subject, context, None, 'staff/emails/staff_add_notification.html', email_group)
send_email(subject, context, None, 'staff/emails/staff_leave_reminder_notification.html', email_group)

# Physician notifications
send_email(subject, context, None, 'physicians/emails/physician_add_notification.html', email_group)
send_email(subject, context, None, 'physicians/emails/physician_update_notification.html', email_group)

# Clinic notifications
send_email(subject, context, None, 'clinics/emails/clinic_add_notification.html', email_group)
```

### **In Management Commands:**
```python
send_email(subject, context, recipients, 'staff/emails/staff_leave_reminder_notification.html')
```

## 🔄 Adding New Email Templates

### **Step 1: Choose Location**
- Staff-related → `staff/emails/`
- Physician-related → `physicians/emails/`
- Clinic-related → `clinics/emails/`

### **Step 2: Follow Naming Convention**
- Format: `[entity]_[action]_notification.html`
- Examples:
  - `staff_termination_notification.html`
  - `physician_certification_update_notification.html`
  - `clinic_closure_notification.html`

### **Step 3: Update Django Code**
- Add template reference in appropriate view function
- Update `get_email_list_for_template()` if needed
- Test thoroughly

### **Step 4: Update Documentation**
- Add entry to this guide
- Update the main reorganization report

## ✅ Benefits of This Convention

1. **Self-Documenting:** Template name clearly indicates purpose
2. **Consistent:** All email templates follow the same pattern
3. **Organized:** Templates grouped by functional area
4. **Searchable:** Easy to find templates using naming pattern
5. **Maintainable:** Clear structure for future development

## 🚨 Important Notes

- **Always use full path** when referencing templates in Django code
- **Test thoroughly** after adding or modifying email templates
- **Follow the naming convention** to maintain consistency
- **Update this guide** when adding new templates
- **All email templates must end with** `_notification.html`

---

**Last Updated:** June 10, 2025  
**Maintained by:** EOPCN Development Team
