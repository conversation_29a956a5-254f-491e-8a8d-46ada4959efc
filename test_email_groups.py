#!/usr/bin/env python
"""
Test script to validate email group functionality
"""
import os
import sys
import django

# Setup Django environment
sys.path.append('c:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'eopcn_staff_project.settings')
django.setup()

from eopcn_staff.models import EmailGroup, EmailRecipient
from eopcn_staff.forms import AddStaffForm, StaffAssignmentForm, StaffLeaveForm, CommentForm, PhysicianForm, ClinicForm

def test_email_group_functionality():
    """Test the email group functionality"""
    print("Testing Email Group Functionality")
    print("=" * 50)
    
    # Test 1: Check if EmailGroup model works
    try:
        email_groups = EmailGroup.objects.filter(is_active=True)
        print(f"✓ Found {email_groups.count()} active email groups")
        
        for group in email_groups[:3]:  # Show first 3 groups
            recipients = group.get_active_recipients()
            print(f"  - {group.name}: {recipients.count()} recipients")
    except Exception as e:
        print(f"✗ Error accessing email groups: {e}")
        return False
    
    # Test 2: Check if forms have the enhanced email_group field
    try:
        form_classes = [AddStaffForm, StaffAssignmentForm, StaffLeaveForm, CommentForm, PhysicianForm, ClinicForm]
        for form_class in form_classes:
            form = form_class()
            if 'email_group' in form.fields:
                field = form.fields['email_group']
                print(f"✓ {form_class.__name__} has enhanced email_group field")
                print(f"  Help text: {field.help_text[:50]}...")
                print(f"  CSS classes: {field.widget.attrs.get('class', 'None')}")
            else:
                print(f"✗ {form_class.__name__} missing email_group field")
    except Exception as e:
        print(f"✗ Error testing forms: {e}")
        return False
      # Test 3: Check if templates exist
    template_files = [
        'staff/email_group_widget.html',
        'clinics/add_clinic.html',
        'staff/add_physician.html',
        'staff/add_staff_leave.html'
    ]
      # Test 4: Check if email group widget uses correct URL
    try:
        widget_path = 'c:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\templates\\staff\\email_group_widget.html'
        with open(widget_path, 'r') as f:
            widget_content = f.read()
        
        # Extract the fetch URL
        import re
        fetch_url_pattern = r'fetch\(`(.*?)`'
        match = re.search(fetch_url_pattern, widget_content)
        
        if match:
            found_url = match.group(1)
            print(f"✓ Found URL in widget: {found_url}")
            
            if '/eopcn/email-groups/' in found_url:
                print(f"✓ Email group widget uses correct URL path with /eopcn/ prefix")
            else:
                print(f"✗ Email group widget has incorrect URL path missing /eopcn/ prefix")
        else:
            print("✗ Could not find fetch URL in the widget file")
            
        # Check if the URL is defined in urls.py
        urls_path = 'c:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\urls.py'
        with open(urls_path, 'r') as f:
            urls_content = f.read()
        
        if "path('email-groups/<int:group_id>/info/', views.get_email_group_info" in urls_content:
            print(f"✓ URL endpoint defined in urls.py as 'email-groups/<int:group_id>/info/'")
            print(f"! Note: This will be accessed as '/eopcn/email-groups/<id>/info/' because of the root URLconf")
        else:
            print(f"✗ URL endpoint for email group info not found in urls.py")
            
    except Exception as e:
        print(f"✗ Error checking email group widget URLs: {e}")
    
    for template_file in template_files:
        template_path = f'c:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp\\eopcn_staff\\templates\\{template_file}'
        if os.path.exists(template_path):
            print(f"✓ Template exists: {template_file}")
        else:
            print(f"✗ Template missing: {template_file}")
    
    print("\nTest completed successfully!")
    return True

if __name__ == '__main__':
    try:
        test_email_group_functionality()
    except Exception as e:
        print(f"Critical error: {e}")
        sys.exit(1)
