{% extends "base.html" %}

{% block title %}Clinic Staff Allocations{% endblock %}

{% block content %}
{% load custom_filters %}

<style>
body {
  font-family: arial, sans-serif;
  margin: 0;
  padding: 0;
}

.column {
  float: left;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.row::after {
  content: "";
  clear: both;
  display: table;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #dddddd;
}

h2 {
  text-align: center;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 4px;
}

.button1 {
    background-color: #008CBA;
}

.button:hover {
    background-color: #006e92;
}

.dataTables_filter, .dataTables_length {
    padding-bottom: 10px;
}

.dataTables_filter {
    float: left !important;
    text-align: left !important;
}

.dataTables_length {
    float: right !important;
    text-align: right !important;
}

.greyed-out {
    background-color: #f0f0f0;
    color: #888888;
  }
  
</style>

<h2>Clinic Staff Allocations</h2>

<label>
    <input type="checkbox" id="toggleActive" checked/> Show only active staff allocations
</label>

<label>
    <input type="checkbox" id="toggleActivePhysicians" checked/> Show only clinics with active primary care providers
</label>

<div class="row">
  <div class="column">
    <table id="pcndatatable" class="display">
      <thead>
        <tr>
            <th>Clinic Name</th>
            <th>Active Primary Care Providers</th>
            <th>Staff First Name</th>
            <th>Staff Last Name</th>
            <th>Staff Role</th>
            <th>Assignment In Clinic</th>
            <th>FTE In Clinic</th>
            <th>Mon</th>
            <th>Tue</th>
            <th>Wed</th>
            <th>Thu</th>
            <th>Fri</th>
            <th>Primary Clinic Contact</th>
            <th>Primary Contact Role</th>
            <th>Primary Contact Phone</th>
            <th>Start Date</th>
            <th>End Date</th>
        </tr>
    </thead>    
    <tbody>
        {% for staff in clinic_staff %}
        <tr class="{% if staff.end_date %}greyed-out role-ended{% endif %}">
            <td>
                {% if staff.clinic_id and staff.clinic_id != '' and staff.clinic_id != 0 %}
                    <a href="{% url 'clinic_detail' staff.clinic_id %}">{{ staff.clinic_name }}</a>
                {% else %}
                    {{ staff.clinic_name }}
                {% endif %}
            </td>
            <td>{{ staff.active_physicians }}</td>
            <td>
                {% if staff.staff_id %}
                    <a href="{% url 'staff_detail' staff.staff_id %}">{{ staff.first_name }}</a>
                {% else %}
                    {{ staff.first_name }}
                {% endif %}
            </td>
            <td>
                {% if staff.staff_id %}
                    <a href="{% url 'staff_detail' staff.staff_id %}">{{ staff.last_name }}</a>
                {% else %}
                    {{ staff.last_name }}
                {% endif %}
            </td>            
            <td>{{ staff.staff_role|default_if_none:"" }}</td>
            <td>{{ staff.assignment_in_clinic|default_if_none:"" }}</td>
            <td>{{ staff.fte|default_if_none:"" }}</td>
            <td>{% if staff.monday is not None %}{{ staff.monday|yesno:"Yes," }}{% endif %}</td>
            <td>{% if staff.tuesday is not None %}{{ staff.tuesday|yesno:"Yes," }}{% endif %}</td>
            <td>{% if staff.wednesday is not None %}{{ staff.wednesday|yesno:"Yes," }}{% endif %}</td>
            <td>{% if staff.thursday is not None %}{{ staff.thursday|yesno:"Yes," }}{% endif %}</td>
            <td>{% if staff.friday is not None %}{{ staff.friday|yesno:"Yes," }}{% endif %}</td>
            <td>{{ staff.primary_contact|default_if_none:"" }}</td>
            <td>{{ staff.primary_contact_role|default_if_none:"" }}</td>
            <td>{{ staff.primary_contact_phone|phone_format|default_if_none:"" }}</td>
            <td>{{ staff.start_date|date:"M. d, Y" }}</td>
            <td>{{ staff.end_date|date:"M. d, Y" }}</td>
        </tr>
        {% endfor %}
    </tbody>       
    </table>
  </div>
</div>


    <!-- Load DataTables -->
    {% load static %}
    <script src="{% static 'eopcn_staff/js/datatables.js' %}"></script>

    <!-- Custom Filter for Active Physicians -->
    <script>
        $(document).ready(function() {
            var table = $('#pcndatatable').DataTable();

            // Custom filter to hide rows where Active Primary Care Providers = 0
            $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                var showWithActivePhysicians = $('#toggleActivePhysicians').is(':checked');
                var activePhysicians = parseInt(data[1]) || 0; // Assuming column index 1 is Active Primary Care Providers
                
                if (showWithActivePhysicians && activePhysicians === 0) {
                    return false;
                }
                return true;
            });

            // Trigger the filter on toggle change
            $('#toggleActivePhysicians').on('change', function() {
                table.draw();
            });

            // Apply filter at initialization
            table.draw();
        });
    </script>

{% endblock %}
