{% extends "base.html" %}

{% block title %}Edit Staff Member{% endblock %}

{% block content %}
{% load static %}

<div class="form-container">
    <h2 class="subheading">Edit Staff Member's Assignment Allocations</h2>
    
    <!-- Page Purpose and Instructions -->
    <div class="info-section">
        <h3>About This Form</h3>
        <p>This form manages how a staff member's total FTE is allocated across different teams, clinics, and days of the week within their role assignment.</p>
        
        <div class="important-notes">
            <h4>Important Guidelines:</h4>
            <ul>
                <li><strong>Allocation Types:</strong> If staff are centralized, you'll see centralized teams. If RIC/Decentralized, you'll see member clinics that are supported.</li>
                <li><strong>Clinic Listings:</strong> All clinics are populated from the Clinics page - if a clinic is missing, it needs to be added there first.</li>
                <li><strong>End Date Usage:</strong> Only update end date when staff have:
                    <ul>
                        <li>Left the organization</li>
                        <li>Moved to a different role</li>
                        <li>Stopped supporting that specific team/clinic</li>
                    </ul>
                </li>
                <li><strong>Staff on Leave:</strong> Do NOT add an end date for staff on leave. Only uncheck "Currently Active" - staff retain their allocation but are temporarily not active.</li>
                <li><strong>Currently Active Status:</strong> Should be unchecked if there's an end date OR if staff are on leave (without end date).</li>
            </ul>
        </div>
    </div>

    <form id="editAllocationForm" method="post">
        {% csrf_token %}
        <!-- Render the assignment formset management form -->
        {{ allocation_formset.management_form }}

        <!-- Display Errors for Allocation Formset -->
        {% if allocation_formset.non_form_errors %}
        <div class="alert alert-danger">
            <ul>
                {% for error in allocation_formset.non_form_errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}
    </div>

    <div class="form-container">
        <p>
            You are editing allocations for:<br>
            Name: <strong>{{ assignment.staff.first_name }} {{ assignment.staff.last_name }}</strong><br>
            Role: <strong>{{ assignment.role.role_name }}</strong><br>
            Supervisor: <strong>{{ assignment.supervisor.first_name }} {{ assignment.supervisor.last_name }}</strong>
        </p>
    </div>

    <div class="form-container">
    {% comment %} Loop through each assignment form in the formset {% endcomment %}
    {% for allocation_form in allocation_formset %}
        <h4 class="subheading">Allocation {{ forloop.counter }}</h4>
        <hr>
            {{ allocation_form.staff_allocation_id }}

            <!-- Centralized/Decentralized -->
            <div class="allocation-form-row">
                <div class="form-group">
                    <label>{{ allocation_form.centralized_vs_ric.label }}</label><br>
                    {% for radio in allocation_form.centralized_vs_ric %}
                    <div class="form-check form-check-inline">
                        {{ radio.tag }}
                        <label class="form-check-label">{{ radio.choice_label }}</label>
                    </div>
                    {% endfor %}
                </div>

            <!-- Days of the week -->
            <div class="form-group">
                <label>Allocated days:</label><br>
                <div class="form-check form-check-inline">
                    {{ allocation_form.monday }} {{ allocation_form.monday.label_tag }}
                </div>
                <div class="form-check form-check-inline">
                    {{ allocation_form.tuesday }} {{ allocation_form.tuesday.label_tag }}   
                </div>
                <div class="form-check form-check-inline">
                    {{ allocation_form.wednesday }} {{ allocation_form.wednesday.label_tag }}
                </div>
                <div class="form-check form-check-inline">
                    {{ allocation_form.thursday }} {{ allocation_form.thursday.label_tag }}
                </div>
                <div class="form-check form-check-inline">
                    {{ allocation_form.friday }} {{ allocation_form.friday.label_tag }}
                </div>
            </div>

            <!-- Clinic Field -->
            <div class="form-group">
                {{ allocation_form.clinic.label_tag }} 
                {{ allocation_form.clinic }}
            </div>
            <div class="helper-text clinic-helper-{{ forloop.counter0 }}" style="display: none;">
                <small class="form-text text-muted">ℹ️ Missing a clinic? <a href="{% url 'clinic_list' %}" target="_blank">Add clinics here</a></small>
            </div>

            <!-- Assignment in Clinic Field -->
            <div class="form-group">
                {{ allocation_form.assignment_in_clinic.label_tag }} 
                {{ allocation_form.assignment_in_clinic }}
            </div>

            <!-- Allocation Delivery -->
            <div class="form-group">
                {{ allocation_form.allocation_type.label_tag }} 
                {{ allocation_form.allocation_type }}
            </div>

            <!-- Program Field -->
            <div class="form-group">
                {{ allocation_form.program.label_tag }} 
                {{ allocation_form.program }}
            </div>
            <div class="helper-text program-helper-{{ forloop.counter0 }}" style="display: none;">
                <small class="form-text text-muted">ℹ️ Missing a team? <a href="{% url 'program_list' %}" target="_blank">Add teams here</a></small>
            </div>

            <!-- FTE -->
            <div class="form-group">
                {{ allocation_form.fte.label_tag }} 
                {{ allocation_form.fte }}
            </div>

            <!-- Start Date -->
            <div class="form-group">
                {{ allocation_form.start_date.label_tag }} 
                {{ allocation_form.start_date }}
            </div>

            <!-- End Date -->
            <div class="form-group">
                {{ allocation_form.end_date.label_tag }} 
                {{ allocation_form.end_date }}
            </div>
            <div class="helper-text">
                <small class="form-text text-muted">⚠️ Only set when staff leaves organization, changes roles, or stops supporting this team/clinic</small>
            </div>

            <!-- Currently Active-->
            <div class="form-group">
                {{ allocation_form.currently_active.label_tag }}
                {{ allocation_form.currently_active }}
            </div>
            <div class="helper-text">
                <small class="form-text text-muted">ℹ️ Uncheck for staff on leave (no end date) or when allocation has ended</small>
            </div>

            {% if allocation_form.DELETE %}
            <div class="form-group delete-checkbox">
                {{ allocation_form.DELETE.label_tag }}
                {{ allocation_form.DELETE }}
                <small class="text-muted">Check to remove this allocation</small>
            </div>
            {% endif %}

        </div>
        {% endfor %}
    </div>
</div>

<div class="form-container">
    {% with form=comment_form %}
        {% include 'staff/email_group_widget.html' %}
    {% endwith %}
</div>

<div class="form-container">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <button type="submit" class="btn btn-primary">Save</button>
        <a href="{% url 'staff_detail' assignment.staff.pk %}" class="btn-secondary mt-3">Back to Staff Details</a>
    </div>
</div>
</form>
</div>


            {% comment %}
        


    <!-- Loop through each assignment form in the formset -->
    {% for assignment_form in allocation_formset %}
    <hr>
        <div class="assignment-form">
            <!-- Display Assignment Form Fields -->
            {{ assignment_form.as_p }}

            <!-- Allocations for this Role -->
            <div class="allocation-formset">
                <!-- Display Errors for this Assignment Form -->
                {% if assignment_form.non_field_errors %}
                <div class="alert alert-danger">
                    <ul>
                        {% for error in assignment_form.non_field_errors %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}

                <!-- Loop through each allocation form in the assignment form -->
                {% for allocation_form in assignment_form.allocation_formset %}
                <div class="allocation-form-row">
                    <!-- Centralized/Decentralized -->
                    <div class="form-group">
                        <label>{{ allocation_form.centralized_vs_ric.label }}</label><br>
                        {% for radio in allocation_form.centralized_vs_ric %}
                        <div class="form-check form-check-inline">
                            {{ radio.tag }}
                            <label class="form-check-label">{{ radio.choice_label }}</label>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Days of the week -->
                    <div class="form-group">
                        <label>Allocated days:</label><br>
                        <div class="form-check form-check-inline">
                            {{ allocation_form.monday }} {{ allocation_form.monday.label_tag }}
                        </div>
                        <div class="form-check form-check-inline">
                            {{ allocation_form.tuesday }} {{ allocation_form.tuesday.label_tag }}   
                        </div>
                        <div class="form-check form-check-inline">
                            {{ allocation_form.wednesday }} {{ allocation_form.wednesday.label_tag }}
                        </div>
                        <div class="form-check form-check-inline">
                            {{ allocation_form.thursday }} {{ allocation_form.thursday.label_tag }}
                        </div>
                        <div class="form-check form-check-inline">
                            {{ allocation_form.friday }} {{ allocation_form.friday.label_tag }}
                        </div>
                    </div>

                    <!-- Clinic Field -->
                    <div class="form-group">
                        {{ allocation_form.clinic.label_tag }} 
                        {{ allocation_form.clinic }}
                    </div>

                    <!-- Assignment in Clinic Field -->
                    <div class="form-group">
                        {{ allocation_form.assignment_in_clinic.label_tag }} 
                        {{ allocation_form.assignment_in_clinic }}
                    </div>

                    <!-- Program Field -->
                    <div class="form-group">
                        {{ allocation_form.program.label_tag }} 
                        {{ allocation_form.program }}
                    </div>

                    <!-- FTE -->
                    <div class="form-group">
                        {{ allocation_form.fte.label_tag }} 
                        {{ allocation_form.fte }}
                    </div>

                    <!-- Start Date -->
                    <div class="form-group">
                        {{ allocation_form.start_date.label_tag }} 
                        {{ allocation_form.start_date }}
                    </div>

                    <!-- End Date -->
                    <div class="form-group">
                        {{ allocation_form.end_date.label_tag }} 
                        {{ allocation_form.end_date }}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    {% endfor %}

    <button type="submit" class="btn btn-primary">Save</button>
    {% comment %} <button type="submit" class="btn btn-primary" onclick="return confirm('Are you sure you want to save these changes?');">Save</button> {% endcomment %}
</form>

<script src="{% static 'eopcn_staff/js/allocation_form_management.js' %}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to toggle helper text based on centralized_vs_ric selection
    function toggleHelperText() {
        {% for allocation_form in allocation_formset %}
        const centralizedRadios{{ forloop.counter0 }} = document.querySelectorAll('input[name="{{ allocation_form.centralized_vs_ric.html_name }}"]');
        const programHelper{{ forloop.counter0 }} = document.querySelector('.program-helper-{{ forloop.counter0 }}');
        const clinicHelper{{ forloop.counter0 }} = document.querySelector('.clinic-helper-{{ forloop.counter0 }}');
        
        centralizedRadios{{ forloop.counter0 }}.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'Centralized' && this.checked) {
                    programHelper{{ forloop.counter0 }}.style.display = 'block';
                    clinicHelper{{ forloop.counter0 }}.style.display = 'none';
                } else if (this.value === 'RIC/Decentralized' && this.checked) {
                    programHelper{{ forloop.counter0 }}.style.display = 'none';
                    clinicHelper{{ forloop.counter0 }}.style.display = 'block';
                } else {
                    programHelper{{ forloop.counter0 }}.style.display = 'none';
                    clinicHelper{{ forloop.counter0 }}.style.display = 'none';
                }
            });
            
            // Check initial state
            if (radio.checked && radio.value === 'Centralized') {
                programHelper{{ forloop.counter0 }}.style.display = 'block';
                clinicHelper{{ forloop.counter0 }}.style.display = 'none';
            } else if (radio.checked && radio.value === 'RIC/Decentralized') {
                programHelper{{ forloop.counter0 }}.style.display = 'none';
                clinicHelper{{ forloop.counter0 }}.style.display = 'block';
            }
        });
        {% endfor %}
    }
    
    toggleHelperText();
});
</script>

<style>
    .form-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .button-container {
        flex: 1;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border-radius: 8px;
        margin-top: 0px;
        margin-bottom: 20px;
    }

    .save-container {
        display: flex;
        justify-content: center;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .form-group {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .form-group label {
        width: 200px;
        margin-right: 10px;
        text-align: left;
    }

    .form-group input, 
    .form-group select, 
    .form-group textarea {
        flex: 1;
        padding: 5px;
        max-width: 100%;
    }

    button {
        width: auto;
        padding: 10px 20px;
    }

    a.btn-secondary {
        margin-left: 10px;
        padding: 10px 20px;
        text-decoration: none;
        background-color: #6c757d;
        color: white;
        border-radius: 4px;
    }

    .allocation-form-row {
        margin-bottom: 20px;
    }

    hr {
        margin: 10px 0;
        border: 0;
        border-top: 1px solid #ccc;
    }

    .allocation-heading {
    text-align: left; /* Centers the text horizontally */
    font-weight: bold;  /* Optional: makes the text bold */
    margin: 10px 0;     /* Adds vertical margin around the text */
    }

    .subheading {
    text-align: center; /* Center alignment */
    }

    .text-muted {
        color: #6c757d;
        font-size: 0.875em;
        font-style: italic;
    }

    .delete-checkbox {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        padding: 10px;
        border-radius: 3px;
        margin-bottom: 10px;
    }

    .info-section {
        background-color: #e8f4fd;
        border: 1px solid #b8daff;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .info-section h3 {
        color: #004085;
        margin-bottom: 10px;
    }

    .important-notes {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 12px;
        margin-top: 15px;
    }

    .important-notes h4 {
        color: #856404;
        margin-bottom: 8px;
    }

    .important-notes ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .important-notes li {
        margin-bottom: 5px;
    }

    .form-text {
        font-size: 0.875em;
        color: #6c757d;
        margin-left: 10px;
        font-style: normal;
    }

    .helper-text {
        margin-left: 210px;
        margin-top: -10px;
        margin-bottom: 15px;
    }

</style>

{% endblock %}