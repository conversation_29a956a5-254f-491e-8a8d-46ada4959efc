#!/usr/bin/env python
"""
Check if the test reminder was sent successfully
"""
import os
import sys
import django

# Setup Django environment
sys.path.append('c:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'EOPCNOpApp.settings')
django.setup()

from django.utils import timezone
import pytz
from eopcn_staff.models import StaffLeave

def check_test_reminder():
    """Check the status of our test reminder (Leave ID 85)"""
    try:
        # Look for the test reminder we just created
        test_leave = StaffLeave.objects.get(leave_id=85)
        
        mst = pytz.timezone('US/Mountain')
        reminder_mst = test_leave.reminder_datetime.astimezone(mst)
        
        print("🔍 TEST REMINDER STATUS")
        print("=" * 40)
        print(f"Leave ID: {test_leave.leave_id}")
        print(f"Staff: {test_leave.staff.first_name} {test_leave.staff.last_name}")
        print(f"Reminder Time: {reminder_mst.strftime('%Y-%m-%d %I:%M %p %Z')}")
        print(f"Email Address: {test_leave.reminder_email_address}")
        print(f"Reminder Sent: {'✅ YES' if test_leave.reminder_sent else '❌ NO'}")
        
        if test_leave.reminder_sent:
            print("\n🎉 SUCCESS! The reminder was sent by GitHub Actions!")
            print("📧 Check your <NAME_EMAIL>")
        else:
            print("\n⚠️  The reminder hasn't been sent yet.")
            print("This could mean:")
            print("- GitHub Actions hasn't run yet")
            print("- There was an error in the workflow")
            print("- The reminder time hasn't passed yet")
        
        return test_leave
        
    except StaffLeave.DoesNotExist:
        print("❌ Test reminder (Leave ID 85) not found")
        return None
    except Exception as e:
        print(f"❌ Error checking reminder: {e}")
        return None

def check_all_recent_reminders():
    """Check all recent reminders"""
    print("\n📋 ALL RECENT REMINDERS")
    print("=" * 40)
    
    recent_reminders = StaffLeave.objects.filter(
        reminder_datetime__isnull=False,
        created_by__in=['github_test', 'test_system', 'test_user']
    ).order_by('-date_created')[:5]
    
    if not recent_reminders:
        print("No recent test reminders found")
        return
    
    mst = pytz.timezone('US/Mountain')
    
    for leave in recent_reminders:
        reminder_mst = leave.reminder_datetime.astimezone(mst)
        status = "✅ SENT" if leave.reminder_sent else "⏰ PENDING"
        
        print(f"{status} Leave ID: {leave.leave_id}")
        print(f"   Staff: {leave.staff.first_name} {leave.staff.last_name}")
        print(f"   Time: {reminder_mst.strftime('%Y-%m-%d %I:%M %p %Z')}")
        print(f"   Email: {leave.reminder_email_address}")
        print(f"   Created: {leave.date_created.strftime('%Y-%m-%d %H:%M')}")
        print()

if __name__ == "__main__":
    print("🧪 CHECKING GITHUB ACTIONS RESULTS")
    print("=" * 50)
    
    test_leave = check_test_reminder()
    check_all_recent_reminders()
    
    print("\n🎯 NEXT STEPS:")
    print("=" * 40)
    if test_leave and test_leave.reminder_sent:
        print("✅ Everything worked! Check your email.")
        print("🔄 The daily automation is now ready.")
        print("📅 It will run automatically every day at 9 AM MST.")
    else:
        print("🔍 Check the GitHub Actions logs for details:")
        print("1. Go to your repository → Actions tab")
        print("2. Click on the latest workflow run")
        print("3. Click on 'send-reminders' job")
        print("4. Look for error messages in the logs")
        print("\n🔧 Or try running locally:")
        print("python manage.py send_leave_reminders")
