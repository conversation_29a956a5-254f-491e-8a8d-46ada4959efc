{% extends "base.html" %}

{% block title %}Clinic List{% endblock %}

{% block content %}
{% load custom_filters %}

<style>
body {
  font-family: arial, sans-serif;
  margin: 0;
  padding: 0;
}

.column {
  float: left;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.row::after {
  content: "";
  clear: both;
  display: table;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #dddddd;
}

h2 {
  text-align: center;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 4px;
}

.button1 {
    background-color: #008CBA;
}

.button:hover {
    background-color: #006e92;
}

.link-button {
    display: inline-block;
    padding: 5px 10px;
    font-size: 12px;
    background-color: #73b3f8;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    margin-right: 5px;
}

.link-button:hover {
    background-color: #0056b3;
}

.dataTables_filter, .dataTables_length {
    padding-bottom: 10px;
}

.dataTables_filter {
    float: left !important;
    text-align: left !important;
}

.dataTables_length {
    float: right !important;
    text-align: right !important;
}

@media screen and (max-width: 650px) {
  .column {
    width: 100%;
    display: block;
  }
}

.greyed-out {
  background-color: #f0f0f0;
  color: #888888;
}

/* Add this new style for the green button to match list_staff.html */
.green-button {
  background-color: #5dbea3;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;
  display: inline-block;
}

.green-button:hover {
  background-color: darkgreen;
}
</style>

<h2>Clinics</h2>

<!-- Process messages as JavaScript alerts instead of inline HTML -->
{% if messages %}
    <script>
        {% for message in messages %}
            alert("{{ message }}");
        {% endfor %}
    </script>
{% endif %}

<!-- Add New Clinic button -->
<a href="{% url 'add_clinic' %}" class="button green-button">+ Add New Clinic</a>

<div class="row">
  <div class="column">
    <table id="pcndatatable" class="display">
      <thead>
        <tr>
            <th>Clinic Name</th>
            <th>Address</th>
            <th>City</th>
            <th>Phone</th>
            <th>Primary Contact</th>
            <th>Primary Contact Role</th>
            <th>Primary Contact Phone</th>
            <th>Primary Contact Email</th>
            <th>Most Recent Clinic Note</th>
            <th>Website</th>
            <th>Active Physicians</th>
        </tr>
      </thead>    
      <tbody>
        {% for clinic in clinics %}
          <tr>
            <td><a href="{% url 'clinic_detail' clinic.clinic_id %}">{{ clinic.clinic_name }}</a></td>
            <td>{{ clinic.street_address|default_if_none:"" }}</td>
            <td>{{ clinic.city|default_if_none:"" }}</td>
            <td>{{ clinic.business_phone|phone_format|default_if_none:"" }}</td>
            <td>{{ clinic.primary_contact|default_if_none:"" }}</td>
            <td>{{ clinic.primary_contact_role|default_if_none:"" }}</td>
            <td>{{ clinic.primary_contact_phone|phone_format|default_if_none:"" }}</td>
            <td>{{ clinic.primary_contact_email|default_if_none:"" }}</td>
            <td data-order="{{ clinic.last_note_date|date:"Y-m-d" }}">
              {% if clinic.last_note_date %}
                {{ clinic.last_note_date|date:"M d, Y" }}
              {% else %}
                No notes
              {% endif %}
            </td>
            <td>{% if clinic.clinic_website %}<a href="{{ clinic.clinic_website }}" target="_blank">Website</a>{% endif %}</td>
            <td>{{ clinic.active_physician_count }}</td>
          </tr>
        {% endfor %}
      </tbody>    
    </table>
  </div>
</div>

{% load static %}
<script src="{% static 'eopcn_staff/js/datatables.js' %}"></script>

{% endblock %}
