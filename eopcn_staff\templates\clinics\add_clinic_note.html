{% extends "base.html" %}

{% block title %}Add Note for {{ clinic.clinic_name }}{% endblock %}

{% block content %}
<style>
    .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    
    .form-control {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
    }
    
    textarea.form-control {
        min-height: 200px;
    }
    
    .btn {
        display: inline-block;
        padding: 10px 15px;
        background-color: #4CAF50;
        color: white;
        text-decoration: none;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .btn:hover {
        background-color: #45a049;
    }
    
    .btn-secondary {
        background-color: #6c757d;
    }
    
    .btn-secondary:hover {
        background-color: #5a6268;
    }
    
    .page-header {
        margin-bottom: 20px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    
    /* Checkbox styles */
    .physicians-checkboxes {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
    }
    
    .physician-item {
        width: 33%;
        margin-bottom: 10px;
    }
    
    .physician-item label {
        font-weight: normal;
        display: flex;
        align-items: center;
    }
    
    .physician-item input {
        margin-right: 8px;
    }
    
    @media (max-width: 768px) {
        .physician-item {
            width: 50%;
        }
    }
    
    @media (max-width: 480px) {
        .physician-item {
            width: 100%;
        }
    }
    
    /* Section styling */
    .section {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid #eee;
        border-radius: 5px;
        background-color: #f9f9f9;
    }
    
    .section-header {
        margin-bottom: 15px;
        font-size: 1.2rem;
        font-weight: bold;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
    }
    
    /* Add styles for inactive physicians */
    .physician-item.inactive {
        opacity: 0.6;
        color: #666;
    }
    
    .physician-item.inactive label {
        color: #666;
    }
    
    .departure-date {
        font-size: 0.85em;
        color: #888;
        font-style: italic;
        margin-left: 5px;
    }
    
    /* Add styles to make physician links more obvious */
    .physician-item a {
        color: #0067b1;
        text-decoration: underline;
    }
    
    .physician-item a:hover {
        color: #004d84;
        text-decoration: underline;
    }
    
    .physician-item.inactive a {
        color: #888;
        text-decoration: underline;
    }
    
    .physician-item.inactive a:hover {
        color: #666;
        text-decoration: underline;
    }
</style>

<div class="container">
    <div class="page-header">
        <h1>Add Note for {{ clinic.clinic_name }}</h1>
    </div>

    <form method="post">
        {% csrf_token %}
        
        {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {{ form.non_field_errors }}
            </div>
        {% endif %}
        
        <div class="section">
            <div class="section-header">Note Details</div>
            
            <div class="form-group">
                <label for="{{ form.date_of_entry.id_for_label }}">Date of Entry:</label>
                {{ form.date_of_entry }}
                {% if form.date_of_entry.errors %}
                    <div class="alert alert-danger">{{ form.date_of_entry.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="{{ form.author_of_entry.id_for_label }}">Author:</label>
                <div class="form-control" style="background-color: #f8f9fa; color: #6c757d; border: 1px solid #ced4da;">
                    {{ form.author_of_entry.value|default:"Current User" }}
                </div>
                <!-- Hidden field to maintain form submission -->
                <input type="hidden" name="{{ form.author_of_entry.name }}" value="{{ form.author_of_entry.value }}">
                {% if form.author_of_entry.errors %}
                    <div class="alert alert-danger">{{ form.author_of_entry.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="{{ form.type_of_entry.id_for_label }}">Entry Type:</label>
                {{ form.type_of_entry }}
                {% if form.type_of_entry.errors %}
                    <div class="alert alert-danger">{{ form.type_of_entry.errors }}</div>
                {% endif %}
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">Meeting Attendees</div>
            
            <div class="form-group">
                <label for="{{ form.physicians_in_attendance.id_for_label }}">Primary Care Providers in Attendance:</label>
                <div class="physicians-checkboxes">
                    {% for physician in form.physicians_in_attendance.field.queryset %}
                        {% for cp in form.clinic_physicians %}
                            {% if cp.physician.physician_id == physician.physician_id %}
                                <div class="physician-item {% if cp.date_left_clinic %}inactive{% endif %}">
                                    <label>
                                        <input type="checkbox" 
                                               name="{{ form.physicians_in_attendance.name }}" 
                                               value="{{ physician.physician_id }}" 
                                               id="id_physicians_in_attendance_{{ forloop.counter0 }}">
                                        <a href="{% url 'physician_detail' physician.physician_id %}" target="_blank">
                                            {{ physician.last_name }}, {{ physician.first_name }}
                                        </a>
                                        {% if cp.date_left_clinic %}
                                            <span class="departure-date">(Left: {{ cp.date_left_clinic|date:"M d, Y" }})</span>
                                        {% endif %}
                                    </label>
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% empty %}
                        <p>No primary care providers associated with this clinic.</p>
                    {% endfor %}
                </div>
                {% if form.physicians_in_attendance.errors %}
                    <div class="alert alert-danger">{{ form.physicians_in_attendance.errors }}</div>
                {% endif %}
            </div>
            
            <div class="form-group">
                <label for="{{ form.other_attendees.id_for_label }}">Other Attendees:</label>
                <textarea id="{{ form.other_attendees.id_for_label }}" 
                          name="{{ form.other_attendees.name }}" 
                          class="form-control" 
                          style="min-height: 60px !important;"
                          rows="2" 
                          placeholder="Enter names of other staff or non-primary care provider attendees (one per line)">{{ form.other_attendees.value|default:'' }}</textarea>
                <small class="text-muted">Enter names of other staff or non-primary care provider attendees (one per line)</small>
                {% if form.other_attendees.errors %}
                    <div class="alert alert-danger">{{ form.other_attendees.errors }}</div>
                {% endif %}
            </div>
        </div>
        
        <div class="section">
            <div class="section-header">Note Content</div>
            
            <div class="form-group">
                <label for="{{ form.note.id_for_label }}">Note:</label>
                {{ form.note }}
                {% if form.note.errors %}
                    <div class="alert alert-danger">{{ form.note.errors }}</div>
                {% endif %}
            </div>
        </div>
        
        <div class="form-group">
            <button type="submit" class="btn">Save Note</button>
            <a href="{% url 'clinic_detail' clinic.clinic_id %}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>
{% endblock %}
