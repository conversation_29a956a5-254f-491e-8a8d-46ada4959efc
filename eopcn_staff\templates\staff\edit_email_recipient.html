{% extends "base.html" %}

{% block content %}
<h2>Edit Email Recipient</h2>

{% if messages %}
    <script>
        {% for message in messages %}
            alert("{{ message }}");
        {% endfor %}
    </script>
{% endif %}

<form method="post">
    {% csrf_token %}
    
    <div class="form-group">
        <label for="{{ form.name.id_for_label }}">Name:</label>
        {{ form.name }}
        {% if form.name.errors %}
            <div class="text-danger">{{ form.name.errors }}</div>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label for="{{ form.email.id_for_label }}">Email Address:</label>
        {{ form.email }}
        {% if form.email.errors %}
            <div class="text-danger">{{ form.email.errors }}</div>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label>
            {{ form.is_active }}
            Active
        </label>
        {% if form.is_active.errors %}
            <div class="text-danger">{{ form.is_active.errors }}</div>
        {% endif %}
    </div>

    <!-- Email Group Widget -->
    {% include 'staff/email_group_widget.html' %}

    <button type="submit" class="btn btn-primary">Update Recipient</button>
    <a href="{% url 'email_recipients_list' %}" class="btn btn-secondary">Cancel</a>
</form>

<div class="recipient-info">
    <h3>Group Memberships</h3>
    {% for membership in recipient.emailgroupmembership_set.all %}
        <p><strong>{{ membership.group.name }}</strong> - Added {{ membership.date_added|date:"M d, Y" }}</p>
    {% empty %}
        <p>Not a member of any groups.</p>
    {% endfor %}
</div>

<style>
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.btn {
    padding: 10px 15px;
    margin-right: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn:hover {
    opacity: 0.8;
}

.text-danger {
    color: #dc3545;
    font-size: 14px;
}

.recipient-info {
    margin-top: 30px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}
</style>

{% endblock %}
