from django import template
import calendar
import re

register = template.Library()

@register.filter
def phone_format(value):
    # Check if value is a string of numbers and has 10 digits
    if value and len(value) == 10 and value.isdigit():
        return f"({value[:3]}) {value[3:6]}-{value[6:]}"
    return value  # Return the original value if it doesn't match the expected format

@register.filter
def month_abbr(value):
    """
    Converts a numeric month (1-12) into its abbreviated month name (e.g., 10 -> "Oct").
    """
    try:
        value_int = int(value)
        if 1 <= value_int <= 12:
            return calendar.month_abbr[value_int]
    except (ValueError, TypeError):
        pass
    return ''

@register.filter
def count_active(items, field_name=None):
    """Count items that don't have a value in the specified field."""
    count = 0
    for item in items:
        if field_name == 'date_left_clinic':
            if not getattr(item, 'date_left_clinic', None):
                count += 1
        elif field_name == 'end_date':
            if not getattr(item, 'end_date', None):
                count += 1
    return count

@register.filter
def filter_valid_staff(staff_list):
    """Filter out invalid staff entries that have no staff_id or have N/A as name."""
    return [s for s in staff_list if s.staff_id and s.first_name and s.last_name and s.first_name != "N/A" and s.last_name != "N/A"]

@register.filter
def intcomma(value):
    """
    Adds commas to an integer or floating point number to make it easier to read.
    This is a simpler replacement for Django's built-in intcomma filter.
    """
    if value is None:
        return ''
    
    # Convert value to string and remove any existing commas
    orig = str(value)
    orig = orig.replace(',', '')
    
    # Check for decimal point
    decimal_point = orig.find('.')
    if decimal_point == -1:
        decimal_point = len(orig)
    
    # Add commas to the integer part
    s = orig[:decimal_point]
    if len(s) <= 3:
        result = s
    else:
        result = ''
        while s:
            result = s[-3:] + ',' + result if result else s[-3:]
            s = s[:-3]
    
    # Append decimal part if it exists
    if decimal_point < len(orig):
        result += orig[decimal_point:]
    
    return result