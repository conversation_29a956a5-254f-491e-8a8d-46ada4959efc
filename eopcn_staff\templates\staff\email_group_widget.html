﻿<!-- Email Group Selection Widget -->
<div class="email-notification-group">
    {% if form.email_leadership %}
    <!-- Email Guidance Message -->
    <div class="email-guidance-message mb-3">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>Email Notification Guidelines:</strong>
            <br>
            Please email the leadership team for <strong>new information</strong> to keep us synchronized and informed.
            However, for <strong>minor updates</strong> or <strong>fixing mistakes</strong>, there's no need to email the leadership team about those changes.
        </div>
    </div>

    <!-- Email Team Checkbox -->
    <div class="form-group mb-3">
        <div class="custom-control custom-checkbox">
            {{ form.email_leadership }}
            <label class="form-check-label font-weight-bold" for="{{ form.email_leadership.id_for_label }}">
                {{ form.email_leadership.label }}
            </label>
        </div>
    </div>
    {% endif %}

    <!-- Email Group Selection (conditionally visible) -->
    <div id="email-group-section" class="form-group mb-3" style="display: none;">
        <div class="email-group-label-section">
            <label for="{{ form.email_group.id_for_label }}" class="form-label font-weight-bold">{{ form.email_group.label }}:</label>
            {% if form.email_group.help_text %}
                <small class="form-text text-muted">{{ form.email_group.help_text }}</small>
            {% endif %}
        </div>
        <div class="email-group-select-container">
            {{ form.email_group }}
        </div>
        {% if form.email_group.errors %}
            <div class="alert alert-danger mt-2">
                {{ form.email_group.errors }}
            </div>
        {% endif %}
    </div>
    
    <!-- Email Group Info Display -->
    <div id="email-group-info" class="email-group-info" style="display: none;">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Selected Email Group Details</h6>
            </div>
            <div class="card-body">
                <div id="email-group-details">
                    <!-- Dynamic content will be loaded here -->
                </div>
                <div class="mt-3">
                    <a id="manage-email-groups-link" href="{% url 'email_groups_list' %}" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-external-link-alt"></i> Manage Email Groups
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Comment Section (conditionally visible) -->
    <div id="email-comment-section" class="form-group mb-3 email-comment-section" style="display: none;">
        <label for="{{ form.comment.id_for_label }}" class="form-label font-weight-bold">Email Comment:</label>
        {% if form.comment.help_text %}
            <small class="form-text text-muted d-block mb-2">{{ form.comment.help_text }}</small>
        {% endif %}
        <div class="email-comment-container">
            {{ form.comment }}
        </div>
        {% if form.comment.errors %}
            <div class="alert alert-danger mt-2">
                {{ form.comment.errors }}
            </div>
        {% endif %}
    </div>
</div>

<style>
.email-notification-group {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 20px;
    margin-bottom: 20px;
}

.email-guidance-message .alert {
    margin-bottom: 0;
    padding: 12px 15px;
    font-size: 0.9em;
    border-left: 4px solid #17a2b8;
}

.email-guidance-message .alert i {
    margin-right: 8px;
    color: #17a2b8;
}

.email-group-label-section {
    margin-bottom: 15px;
    width: 100%;
    display: block;
}

.email-group-select-container {
    margin-top: 0;
    clear: both;
    width: 100%;
    display: block;
}

.email-group-select-container select {
    width: 100%;
    max-width: 500px;
    padding: 8px 12px;
    font-size: 14px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    display: block;
}

.form-text {
    display: block !important;
    margin-bottom: 0 !important;
    margin-top: 6px !important;
    font-size: 0.875em;
    line-height: 1.4;
    color: #6c757d !important;
    width: 100%;
}

.form-label {
    margin-bottom: 0 !important;
    display: block !important;
    font-weight: 600;
    width: 100%;
}

.email-group-info {
    margin-top: 20px;
}

.email-group-info .card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.email-group-info .card-header {
    background-color: #e9ecef;
    border-bottom: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
}

.email-group-info .card-body {
    padding: 1rem;
}

.member-list {
    max-height: 150px;
    overflow-y: auto;
    margin-top: 15px;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 10px;
    background-color: #fff;
}

.member-item {
    padding: 3px 0;
    font-size: 0.9em;
    border-bottom: 1px solid #f8f9fa;
}

.member-item:last-child {
    border-bottom: none;
}

.loading-spinner {
    display: inline-block;
    width: 1.2rem;
    height: 1.2rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.email-comment-section {
    margin-top: 25px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.email-comment-container {
    margin-top: 8px;
}

.email-comment-container textarea {
    width: 100%;
    min-height: 100px;
    padding: 8px 12px;
    font-size: 14px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    resize: vertical;
}

.email-comment-container textarea:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .email-notification-group {
        padding: 15px;
    }
    
    .email-group-select-container select {
        max-width: 100%;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get references to elements
    const emailLeadershipCheckbox = document.querySelector('input[name="email_leadership"]') ||
                                   document.querySelector('#id_email_leadership');
    const emailGroupSection = document.getElementById('email-group-section');
    const emailCommentSection = document.getElementById('email-comment-section');
    const emailGroupSelect = document.querySelector('.email-group-selector') || 
                            document.querySelector('select[name="email_group"]') ||
                            document.querySelector('#id_email_group');
    const emailGroupInfo = document.getElementById('email-group-info');
    const emailGroupDetails = document.getElementById('email-group-details');
    const manageLink = document.getElementById('manage-email-groups-link');
    
    // Function to toggle email group section visibility
    function toggleEmailGroupSection() {
        if (emailLeadershipCheckbox && emailGroupSection && emailCommentSection) {
            if (emailLeadershipCheckbox.checked) {
                emailGroupSection.style.display = 'block';
                emailCommentSection.style.display = 'block';
            } else {
                emailGroupSection.style.display = 'none';
                emailCommentSection.style.display = 'none';
                emailGroupInfo.style.display = 'none';
                // Clear the selection when hiding
                if (emailGroupSelect) {
                    emailGroupSelect.value = '';
                }
            }
        }
    }
    
    // Add event listener for checkbox changes
    if (emailLeadershipCheckbox) {
        emailLeadershipCheckbox.addEventListener('change', toggleEmailGroupSection);
        // Check initial state
        toggleEmailGroupSection();
    }
    
    // Existing email group select functionality
    if (emailGroupSelect) {
        emailGroupSelect.addEventListener('change', function() {
            const selectedGroupId = this.value;
            
            if (selectedGroupId) {
                // Show loading state
                emailGroupInfo.style.display = 'block';
                emailGroupDetails.innerHTML = '<div class="loading-spinner"></div> Loading group details...';
                // Fetch group information using absolute URL
                const base_url = "{% url 'get_email_group_info' 0 %}".replace('/0/', `/${selectedGroupId}/`);
                fetch(base_url, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        emailGroupDetails.innerHTML = `<div class="alert alert-warning">${data.error}</div>`;
                    } else {
                        let membersHtml = '';
                        if (data.members && data.members.length > 0) {
                            membersHtml = '<div class="member-list"><strong>Members:</strong><div class="mt-2">';
                            data.members.forEach(member => {
                                membersHtml += `<div class="member-item">${member.name} (${member.email})</div>`;
                            });
                            if (data.has_more_members) {
                                membersHtml += `<div class="member-item"><em>... and ${data.member_count - data.members.length} more members</em></div>`;
                            }
                            membersHtml += '</div></div>';
                        } else {
                            membersHtml = '<div class="text-muted">No active members in this group.</div>';
                        }
                        
                        emailGroupDetails.innerHTML = `
                            <div>
                                <strong>${data.name}</strong>
                                <p class="text-muted mb-2">${data.description}</p>
                                <div class="mb-2">
                                    <span class="badge badge-info">${data.member_count} member${data.member_count !== 1 ? 's' : ''}</span>
                                </div>
                                ${membersHtml}
                            </div>
                        `;
                        
                        // Update manage link
                        manageLink.href = data.manage_url;
                    }
                })
                .catch(error => {
                    console.error('Error fetching email group info:', error);
                    emailGroupDetails.innerHTML = '<div class="alert alert-danger">Error loading group details. Please try again.</div>';
                });
            } else {
                // Hide info if no group selected
                emailGroupInfo.style.display = 'none';
            }
        });
        
        // Check if there's already a selected value on page load
        if (emailGroupSelect.value && emailLeadershipCheckbox && emailLeadershipCheckbox.checked) {
            emailGroupSelect.dispatchEvent(new Event('change'));
        }
    }
});
</script>
