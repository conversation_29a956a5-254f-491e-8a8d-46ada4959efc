document.addEventListener('DOMContentLoaded', function() {
    const addMoreBtn = document.getElementById('add-allocation');
    // Check if the "Add another allocation" button exists before attaching an event listener
    if (addMoreBtn) {
        addMoreBtn.addEventListener('click', add_new_form);
    }

    function add_new_form(event) {
        if (event) {
            event.preventDefault();
        }

        const formCopyTarget = document.getElementById('allocation-formset');
        const totalForms = document.getElementById('id_allocations-TOTAL_FORMS');
        const currentFormCount = parseInt(totalForms.value);

        // Clone the last form in the formset
        const lastForm = document.querySelector('.allocation-form-row:last-of-type');
        const newFormEl = lastForm.cloneNode(true);

        // Update form indices and clear data
        updateFormIndices(newFormEl, currentFormCount);

        // Increment the total number of forms in the management form
        totalForms.value = currentFormCount + 1;

        // Append the newly cloned form into the formset container
        formCopyTarget.appendChild(newFormEl);

        // Add numbering to the new form
        addNumbering(newFormEl, currentFormCount + 1);

        // Attach event listeners to the new form
        attachToggleEvents(currentFormCount);
    }

    function updateFormIndices(formEl, newIndex) {
        const formRegex = /allocations-(\d+|__prefix__)-/g;

        formEl.querySelectorAll('*').forEach(function(element) {
            if (element.name) {
                element.name = element.name.replace(formRegex, `allocations-${newIndex}-`);
            }
            if (element.id) {
                element.id = element.id.replace(formRegex, `allocations-${newIndex}-`);
            }
            if (element.getAttribute('for')) {
                element.setAttribute('for', element.getAttribute('for').replace(formRegex, `allocations-${newIndex}-`));
            }

            // Clear the value of input fields
            if (element.tagName === 'INPUT' || element.tagName === 'SELECT' || element.tagName === 'TEXTAREA') {
                if (element.type === 'checkbox' || element.type === 'radio') {
                    element.checked = false;
                } else if (element.type !== 'hidden' && element.name !== 'csrfmiddlewaretoken') {
                    element.value = '';
                }
            }
        });
    }

    function addNumbering(formEl, number) {
        const heading = formEl.querySelector('h4');
        if (heading) {
            heading.textContent = 'Allocation ' + number;
        }
    }

    function attachToggleEvents(formIndex) {
        const formPrefix = `allocations-${formIndex}`;

        // Select the radio buttons for this formset
        const centralizedVsRicRadios = document.querySelectorAll(`input[name="${formPrefix}-centralized_vs_ric"]`);

        // Select the fields to toggle for this formset by using dynamic IDs
        const clinicInput = document.getElementById(`id_${formPrefix}-clinic`);
        const assignmentInClinicInput = document.getElementById(`id_${formPrefix}-assignment_in_clinic`);
        const programInput = document.getElementById(`id_${formPrefix}-program`);

        // Select the FTE, Start Date, and Allocated Days fields
        const fteInput = document.getElementById(`id_${formPrefix}-fte`);
        const startDateInput = document.getElementById(`id_${formPrefix}-start_date`);
        const mondayInput = document.getElementById(`id_${formPrefix}-monday`);
        const tuesdayInput = document.getElementById(`id_${formPrefix}-tuesday`);
        const wednesdayInput = document.getElementById(`id_${formPrefix}-wednesday`);
        const thursdayInput = document.getElementById(`id_${formPrefix}-thursday`);
        const fridayInput = document.getElementById(`id_${formPrefix}-friday`);

        // Check if the inputs exist and get their parent .form-group elements
        const clinicField = clinicInput ? clinicInput.closest('.form-group') : null;
        const assignmentInClinicField = assignmentInClinicInput ? assignmentInClinicInput.closest('.form-group') : null;
        const programField = programInput ? programInput.closest('.form-group') : null;

        // Get parent containers for FTE, Start Date, and Allocated Days
        const fteField = fteInput ? fteInput.closest('.form-group') : null;
        const startDateField = startDateInput ? startDateInput.closest('.form-group') : null;
        const allocatedDaysField = mondayInput ? mondayInput.closest('.form-group') : null;  // Assuming all days are in the same group

        // Function to hide all fields
        function hideFields() {
            if (clinicField) clinicField.style.display = 'none';
            if (assignmentInClinicField) assignmentInClinicField.style.display = 'none';
            if (programField) programField.style.display = 'none';
            if (fteField) fteField.style.display = 'none';
            if (startDateField) startDateField.style.display = 'none';
            if (allocatedDaysField) allocatedDaysField.style.display = 'none';
        }

        // Function to toggle fields based on the selected radio button for this formset
        function toggleFields() {
            const selectedValue = document.querySelector(`input[name="${formPrefix}-centralized_vs_ric"]:checked`);
            if (selectedValue) {
                if (selectedValue.value === 'RIC/Decentralized') {
                    if (clinicField) clinicField.style.display = '';
                    if (assignmentInClinicField) assignmentInClinicField.style.display = '';
                    if (programField) programField.style.display = 'none';
                } else if (selectedValue.value === 'Centralized') {
                    if (clinicField) clinicField.style.display = 'none';
                    if (assignmentInClinicField) assignmentInClinicField.style.display = 'none';
                    if (programField) programField.style.display = '';
                }

                // Show FTE, Start Date, and Allocated Days for both options
                if (fteField) fteField.style.display = '';
                if (startDateField) startDateField.style.display = '';
                if (allocatedDaysField) allocatedDaysField.style.display = '';

            } else {
                hideFields();
            }
        }

        // Initially hide all fields when the form is added
        hideFields();

        // Toggle fields based on current selection
        toggleFields();

        // Attach change event listeners to radio buttons to toggle fields dynamically
        centralizedVsRicRadios.forEach(radio => {
            radio.addEventListener('change', toggleFields);
        });
    }

    function attachEventsToExistingForms() {
        const totalForms = parseInt(document.getElementById('id_allocations-TOTAL_FORMS').value);
        const allocationForms = document.querySelectorAll('.allocation-form-row');

        for (let i = 0; i < totalForms; i++) {
            attachToggleEvents(i);
        }

        allocationForms.forEach(function(formEl, index) {
        attachToggleEvents(index);
        attachResetEvent(formEl);
    });

    }

    function attachResetEvent(formEl) {
        const resetBtn = formEl.querySelector('.reset-form');
        if (resetBtn) {
            resetBtn.addEventListener('click', function() {
                const inputs = formEl.querySelectorAll('input, select, textarea');
                inputs.forEach(function(input) {
                    if (input.type === 'checkbox' || input.type === 'radio') {
                        input.checked = false;  // Uncheck all radio/checkboxes
                    } else if (input.type !== 'hidden' && input.name !== 'csrfmiddlewaretoken') {
                        input.value = '';  // Clear value for text/textarea/select fields
                    }
                });
            });
        }
    }

    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('reset', function() {
            // Re-initialize the form's state after reset
            resetFormState();
        });
    }

    function resetFormState() {
        // Hide fields and reset state for all forms
        const totalForms = parseInt(document.getElementById('id_allocations-TOTAL_FORMS').value);
        for (let i = 0; i < totalForms; i++) {
            attachToggleEvents(i);
        }
    }


    // Call the function to attach events to existing forms
    attachEventsToExistingForms();
});