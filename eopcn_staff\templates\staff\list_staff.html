{% extends "base.html" %}

{% block title %}Staff List{% endblock %}

{% block content %}
<style>
body {
  font-family: arial, sans-serif;
  margin: 0;
  padding: 0;
}

.column {
  float: left;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.row::after {
  content: "";
  clear: both;
  display: table;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #dddddd;
}

h2 {
  text-align: center;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 4px;
}

.button1 {
    background-color: #008CBA;
}

.button:hover {
    background-color: #006e92;
}

.link-button {
    display: inline-block;
    padding: 5px 10px;
    font-size: 12px;
    background-color: #73b3f8;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    margin-right: 5px;
}

.link-button:hover {
    background-color: #0056b3;
}

.dataTables_filter, .dataTables_length {
    padding-bottom: 10px;
}

.dataTables_filter {
    float: left !important;
    text-align: left !important;
}

.dataTables_length {
    float: right !important;
    text-align: right !important;
}

@media screen and (max-width: 650px) {
  .column {
    width: 100%;
    display: block;
  }
}

.green-button {
  background-color: #5dbea3;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;  /* Removes underline */
  display: inline-block;   /* Makes the link look like a button */
}

.green-button:hover {
    background-color: darkgreen;  /* Optional: Darken the button on hover */
}

.greyed-out {
  background-color: #f0f0f0;
  color: #888888;
}
</style>
</head>
<body>

<h2>Staff Members List</h2>
{% if messages %}
        <script>
            {% for message in messages %}
                alert("{{ message }}");
            {% endfor %}
        </script>
{% endif %}
<a href="{% url 'add_staff_form' %}" class="button green-button">+ Add New Staff</a>
<a href="{% url 'lists' %}" class="button button1">Staff Lists</a>

<label>
  <input type="checkbox" id="toggleActive" checked /> Show only active staff roles
</label>

<div class="row">
  <div class="column">
    <table id="pcndatatable" class="display">
      <thead>
        <tr>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Hire Date</th>
            <th>Role Name</th>
            <th>Position Number</th>
            <th>Status</th>
            <th>Program</th>
            <th>Supervisor Name</th> <!-- New column for full supervisor name -->
            <th>Role Start Date</th>
            <th>Role End Date</th>
            <th>FTE (Total)</th>
            <th>Email</th>
            <th>Role Currently Active</th>
            {% comment %} <th>Actions</th> {% endcomment %}
        </tr>
    </thead>    
    <tbody>
        {% for staff in staff_with_roles %}
        <tr class="{% if staff.role_currently_active == 0 %}greyed-out role-ended{% endif %}">
            <td><a href="{% url 'staff_detail' staff.staff_id %}">{{ staff.first_name }}</a></td>
            <td><a href="{% url 'staff_detail' staff.staff_id %}">{{ staff.last_name }}</a></td>
            <td data-order="{{ staff.start_date|date:"Y-m-d" }}">{{ staff.start_date|default_if_none:"" }}</td>
            <td>{{ staff.role_name|default:"" }}</td>
            <td>{{ staff.position_number|default_if_none:"" }}</td>
            <td>{{ staff.permanent_vs_temporary|default_if_none:"" }}</td>
            <td>{{ staff.service_name|default_if_none:"" }}</td>
            <td>
              {% if staff.supervisor_id %}
                <a href="{% url 'supervisor_staff' staff.supervisor_id %}">
                  {{ staff.supervisor_full_name|default_if_none:"" }}
                </a>
              {% else %}
                {{ staff.supervisor_full_name|default_if_none:"" }}
              {% endif %}
            </td> <!-- Use full supervisor name -->
            <td data-order="{{ staff.role_start_date|date:"Y-m-d" }}">{{ staff.role_start_date|default_if_none:"" }}</td>
            <td data-order="{{ staff.role_end_date|date:"Y-m-d" }}">{{ staff.role_end_date|default_if_none:"" }}</td>
            <td>{{ staff.role_fte|default_if_none:"" }}</td>
            <td>{{ staff.suggested_email }}</td>
            <td>{{ staff.role_currently_active|yesno:"Yes,No" }}</td>
            {% comment %} <td><a href="{% url 'edit_profile' staff.staff_id %}" class="link-button">Edit Profile</a>
            <a href="{% url 'edit_profile' staff.staff_id %}" class="link-button">Edit</a>
            <a href="{% url 'edit_profile' staff.staff_id %}" class="link-button">Edit</a></td> {% endcomment %}
          </tr>
        {% endfor %}
    </tbody>    
    </table>
  </div>
</div>


{% load static %}
<script src="{% static 'eopcn_staff/js/datatables.js' %}"></script>
  
  
{% endblock %}
