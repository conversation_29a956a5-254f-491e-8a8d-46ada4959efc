{% extends "base.html" %}
{% load static %}

{% block title %}Edit Clinics for {{ physician.last_name }}, {{ physician.first_name }}{% endblock %}

{% block content %}
<link rel="stylesheet" type="text/css" href="{% static 'eopcn_staff/css/staff_form.css' %}">

<div class="form-container">
    <h2 class="subheading">Edit Clinics for {{ physician.last_name }}, {{ physician.first_name }}</h2>
    
    <form method="post">
        {% csrf_token %}
        {{ formset.management_form }}
        
        <h3>Clinic Assignments</h3>
        <div class="required-legend">Fields marked with <span>*</span> are required</div>
        
        {% for form in formset %}
            <div class="clinic-form">
                {% for hidden in form.hidden_fields %}
                    {{ hidden }}
                {% endfor %}
                
                <!-- Add clinic association header with numbering and line above -->
                <hr class="association-divider">
                {% if forloop.counter > 1 %}
                    <h4>Clinic Association {{ forloop.counter }}</h4>
                {% else %}
                    <h4>Primary Clinic Association</h4>
                {% endif %}
                
                {% for field in form.visible_fields %}
                    {% if field.name != 'DELETE' and field.name != 'CPAR_Panel_ID' and field.name != 'active_CII' and field.name != 'active_CPAR' %}
                    <div class="form-group {% if field.field.required %}required-field{% endif %}">
                        {{ field.label_tag }}
                        {{ field }}
                        {% if field.help_text %}
                            <small class="form-text text-muted">{{ field.help_text }}</small>
                        {% endif %}
                        {% if field.errors %}
                            <div class="error-message">{{ field.errors }}</div>
                        {% endif %}
                    </div>
                    {% if field.name == 'clinic' %}
                    <div class="helper-text">
                        <small class="form-text text-muted">ℹ️ Missing a clinic? <a href="{% url 'clinic_list' %}" target="_blank">Add clinics here</a></small>
                    </div>
                    {% endif %}
                    {% endif %}
                {% endfor %}
                
                <!-- Add a section break for the new CPAR fields -->
                <div class="form-section-header">
                    <h4>CPAR Information</h4>
                </div>
                
                <!-- Manually render the new CPAR fields for better control -->
                <div class="form-group">
                    <label for="{{ form.CPAR_Panel_ID.id_for_label }}">CPAR Panel ID:</label>
                    {{ form.CPAR_Panel_ID }}
                    {% if form.CPAR_Panel_ID.errors %}
                        <div class="error-message">{{ form.CPAR_Panel_ID.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.active_CII.id_for_label }}">{{ form.active_CII.label }}:</label>
                    {{ form.active_CII }}
                    {% if form.active_CII.errors %}
                        <div class="error-message">{{ form.active_CII.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.active_CPAR.id_for_label }}">{{ form.active_CPAR.label }}:</label>
                    {{ form.active_CPAR }}
                    {% if form.active_CPAR.errors %}
                        <div class="error-message">{{ form.active_CPAR.errors }}</div>
                    {% endif %}
                </div>
                
                {% if form.instance.pk %}
                    <div class="form-group delete-checkbox">
                        {{ form.DELETE.label_tag }}
                        {{ form.DELETE }}
                        <small class="text-muted">Check to remove this clinic association</small>
                    </div>
                {% endif %}
            </div>
            {% if not forloop.last %}<hr>{% endif %}
        {% endfor %}
</div>

<!-- Email Group Widget -->
<div class="form-container">
    {% with form=comment_form %}
        {% include 'staff/email_group_widget.html' %}
    {% endwith %}
</div>

<!-- Save/Cancel Buttons -->
<div class="form-container">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <button type="submit" class="btn btn-primary">Save Changes</button>
        <a href="{% url 'edit_physician' physician.physician_id %}" class="btn-secondary">Edit Primary Care Provider Info</a>
    </div>
</div>
    </form>

<style>
    /* Add styling for required fields */
    .required-field label:after {
        content: " *";
        color: red;
        font-weight: bold;
    }
    
    /* Add a legend for required fields */
    .required-legend {
        margin-top: 10px;
        font-size: 0.9em;
        color: #666;
    }
    
    .required-legend span {
        color: red;
        font-weight: bold;
    }
    
    /* Ensure consistent form styling for all fields including CPAR */
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group > label {
        display: block;
        margin-bottom: 5px;
        font-weight: normal; /* Changed from bold to normal */
        color: #333;
        white-space: nowrap; /* Prevent label wrapping */
    }
    
    .form-group input[type="number"], 
    .form-group input[type="text"], 
    .form-group input[type="email"], 
    .form-group input[type="date"], 
    .form-group select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        box-sizing: border-box;
    }
    
    /* Add styling for the association divider - made darker */
    .association-divider {
        margin: 30px 0 20px 0;
        border: none;
        border-top: 3px solid #6c757d; /* Changed from 2px #dee2e6 to 3px #6c757d for darker appearance */
    }
    
    /* Remove the checkbox-row styling since we're using individual rows now */
    .form-group input[type="checkbox"] {
        margin: 0;
        width: auto;
    }
    
    /* Delete checkbox styling to match staff leave coverage formset */
    .delete-checkbox {
        background-color: #fff3cd !important;
        border: 1px solid #ffeaa7 !important;
        padding: 10px !important;
        border-radius: 3px !important;
        margin-bottom: 10px !important;
    }

    .delete-checkbox label {
        margin: 0 !important;
        font-weight: normal !important;
        color: #856404 !important;
    }
    
    .text-muted {
        color: #6c757d !important;
        font-size: 0.875em !important;
        font-style: italic !important;
        display: block !important;
        margin-top: 5px !important;
    }
    
    /* Form section header styling */
    .form-section-header {
        margin-top: 20px;
        margin-bottom: 15px;
    }
    
    .form-section-header h4 {
        color: #0067b1;
        border-bottom: 1px solid #ddd;
        padding-bottom: 5px;
        margin: 0;
    }

    .form-text {
        font-size: 0.875em;
        color: #6c757d;
        margin-left: 10px;
        font-style: normal;
    }

    .helper-text {
        margin-left: 210px;
        margin-top: -10px;
        margin-bottom: 15px;
    }
</style>
{% endblock %}