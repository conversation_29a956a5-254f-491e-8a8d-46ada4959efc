Stack trace:
Frame         Function      Args
0007FFFFAB00  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF9A00) msys-2.0.dll+0x2118E
0007FFFFAB00  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFADD8) msys-2.0.dll+0x69BA
0007FFFFAB00  0002100469F2 (00021028DF99, 0007FFFFA9B8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFAB00  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFAB00  00021006A545 (0007FFFFAB10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFADE0  00021006B9A5 (0007FFFFAB10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9D0440000 ntdll.dll
7FF9CF5E0000 KERNEL32.DLL
7FF9CDDC0000 KERNELBASE.dll
7FF9D0170000 USER32.dll
7FF9CDD90000 win32u.dll
7FF9CF990000 GDI32.dll
000210040000 msys-2.0.dll
7FF9CDB40000 gdi32full.dll
7FF9CD9D0000 msvcp_win.dll
7FF9CE170000 ucrtbase.dll
7FF9CFEB0000 advapi32.dll
7FF9CE2C0000 msvcrt.dll
7FF9CE700000 sechost.dll
7FF9CFC30000 RPCRT4.dll
7FF9CCE80000 CRYPTBASE.DLL
7FF9CDC70000 bcryptPrimitives.dll
7FF9CEEA0000 IMM32.DLL
