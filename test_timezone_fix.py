#!/usr/bin/env python
"""
Test script to verify timezone handling fix for leave reminders
"""
import os
import sys
import django
from datetime import datetime

# Setup Django environment
sys.path.append('c:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'EOPCNOpApp.settings')
django.setup()

from django.utils import timezone
import pytz
from eopcn_staff.models import StaffLeave, StaffLeaveTypes, <PERSON>

def test_timezone_conversion():
    """Test the timezone conversion logic"""
    print("=== Testing Timezone Conversion Fix ===")
    
    # Get timezone objects
    mst = pytz.timezone('US/Mountain')
    utc = pytz.UTC
    
    # Test case: User wants reminder at 12:26 PM MST today
    test_time_mst = datetime(2025, 6, 9, 12, 26, 0)  # 12:26 PM MST (naive)
    print(f"User input (naive, represents MST): {test_time_mst}")
    
    # This is what the fixed code should do:
    # 1. Treat naive datetime as MST time
    reminder_dt_mst = mst.localize(test_time_mst)
    print(f"Localized as MST: {reminder_dt_mst}")
    
    # 2. Convert to UTC for storage
    reminder_dt_utc = reminder_dt_mst.astimezone(utc)
    print(f"Converted to UTC for storage: {reminder_dt_utc}")
    
    # 3. When displaying back to user, convert UTC back to MST
    display_mst = reminder_dt_utc.astimezone(mst)
    print(f"Display back to user (MST): {display_mst}")
    
    # 4. Remove timezone for datetime-local input
    display_naive = display_mst.replace(tzinfo=None)
    print(f"For datetime-local input (naive): {display_naive}")
    
    print("\n" + "="*50)
    
    # Verify the conversion is correct
    expected_utc_hour = 12 + 7  # MST is UTC-7 in summer (MDT), UTC-6 in winter
    # Let's check what MST offset is right now
    now_mst = timezone.now().astimezone(mst)
    utc_offset_hours = now_mst.utcoffset().total_seconds() / 3600
    print(f"Current MST UTC offset: {utc_offset_hours} hours")
    
    expected_utc_hour = 12 - utc_offset_hours  # Subtract because MST is behind UTC
    print(f"12:26 PM MST should be {expected_utc_hour:02.0f}:26 UTC")
    print(f"Our conversion gives: {reminder_dt_utc.hour}:26 UTC")
    
    if abs(reminder_dt_utc.hour - expected_utc_hour) < 1:
        print("✅ Timezone conversion is CORRECT!")
    else:
        print("❌ Timezone conversion is WRONG!")
    
    return reminder_dt_utc

def test_with_actual_leave():
    """Test with an actual leave record"""
    print("\n=== Testing with Actual Leave Record ===")
    
    try:
        # Get a staff member and leave type
        staff = Staff.objects.first()
        leave_type = StaffLeaveTypes.objects.first()
        
        if not staff or not leave_type:
            print("❌ Need staff member and leave type in database")
            return
        
        # Create a test leave with the corrected timezone logic
        mst = pytz.timezone('US/Mountain')
        
        # Simulate user input: 12:26 PM MST today
        user_input_time = datetime(2025, 6, 9, 12, 26, 0)  # Naive datetime
        print(f"User wants reminder at: {user_input_time} (MST)")
        
        # Apply the fixed conversion logic
        reminder_dt_mst = mst.localize(user_input_time)
        reminder_dt_utc = reminder_dt_mst.astimezone(pytz.UTC)
        
        print(f"Will be stored as UTC: {reminder_dt_utc}")
        
        # Create the leave record
        test_leave = StaffLeave.objects.create(
            staff=staff,
            leave_type=leave_type,
            leave_start_date=datetime(2025, 6, 16).date(),
            return_date=datetime(2025, 6, 20).date(),
            reminder_datetime=reminder_dt_utc,
            reminder_sent=False,
            reminder_email_address='<EMAIL>',
            date_created=timezone.now(),
            created_by='timezone_test',
            date_modified=timezone.now(),
            modified_by='timezone_test'
        )
        
        print(f"✅ Created test leave {test_leave.leave_id}")
        
        # Now test reading it back
        retrieved_leave = StaffLeave.objects.get(leave_id=test_leave.leave_id)
        stored_utc = retrieved_leave.reminder_datetime
        print(f"Retrieved from DB (UTC): {stored_utc}")
        
        # Convert back to MST for display
        display_mst = stored_utc.astimezone(mst)
        print(f"Converted back to MST: {display_mst}")
        
        # Check if it matches original user input
        original_time = user_input_time.replace(tzinfo=mst.localize(user_input_time).tzinfo)
        if display_mst.replace(tzinfo=None) == user_input_time:
            print("✅ Round-trip conversion is CORRECT!")
        else:
            print("❌ Round-trip conversion is WRONG!")
            print(f"Expected: {user_input_time}")
            print(f"Got: {display_mst.replace(tzinfo=None)}")
        
        # Test the is_reminder_due method
        print(f"\nTesting is_reminder_due method:")
        print(f"Is reminder due? {retrieved_leave.is_reminder_due()}")
        
        # Clean up
        test_leave.delete()
        print("🧹 Cleaned up test leave")
        
    except Exception as e:
        print(f"❌ Error testing with actual leave: {e}")

def show_current_time_info():
    """Show current time information for debugging"""
    print("\n=== Current Time Information ===")
    
    now_utc = timezone.now()
    mst = pytz.timezone('US/Mountain')
    now_mst = now_utc.astimezone(mst)
    
    print(f"Current UTC time: {now_utc}")
    print(f"Current MST time: {now_mst}")
    print(f"MST offset from UTC: {now_mst.utcoffset()}")
    print(f"MST timezone name: {now_mst.tzname()}")

if __name__ == "__main__":
    print("🕐 Testing Leave Reminder Timezone Fix")
    print("="*60)
    
    show_current_time_info()
    test_timezone_conversion()
    test_with_actual_leave()
    
    print("\n" + "="*60)
    print("✅ Timezone testing completed!")
    print("\nTo test the fix:")
    print("1. Go to the leave form in your browser")
    print("2. Set a reminder time (e.g., 12:26 PM today)")
    print("3. Save the form")
    print("4. Check that the stored time is correct")
    print("5. Edit the leave again and verify the time displays correctly")
