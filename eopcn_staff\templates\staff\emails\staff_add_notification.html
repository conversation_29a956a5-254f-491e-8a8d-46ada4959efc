<!DOCTYPE html>
<html>
<head>
    <style>
        .table-container {
            width: 70%;
            table-layout: fixed;
        }
        .label-cell {
            text-align: right;
            width: 30%;
            padding: 4px;
            line-height: 1.2;
        }
        .value-cell {
            background-color: #DDEBF7;
            width: 70%;
            padding: 4px;
            line-height: 1.2;
        }
        .section-header {
            font-size: 18px;
            margin-top: 10px;
        }
    </style>
</head>
<body>

<h1>New Staff Member Information</h1>

<!-- Staff Information Section -->
<h2 class="section-header">Staff Details</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell">First Name:</td>
        <td class="value-cell">{{ staff.first_name }}</td>
    </tr>
    <tr>
        <td class="label-cell">Last Name:</td>
        <td class="value-cell">{{ staff.last_name }}</td>
    </tr>
    <tr>
        <td class="label-cell">Start Date:</td>
        <td class="value-cell">{{ staff.start_date }}</td>
    </tr>
    <tr>
        <td class="label-cell">End Date:</td>
        <td class="value-cell">{{ staff.end_date }}</td>
    </tr>
    <tr>
        <td class="label-cell">Suggested Email:</td>
        <td class="value-cell">{{ staff.suggested_email }}</td>
    </tr>
    <tr>
        <td class="label-cell">Currently Active:</td>
        <td class="value-cell">{{ staff.currently_active }}</td>
    </tr>
    <tr>
        <td class="label-cell">N95 Mask Size:</td>
        <td class="value-cell">{{ staff.n95_mask_size }}</td>
    </tr>
    <tr>
        <td class="label-cell">Computer Number:</td>
        <td class="value-cell">{{ staff.computer_number }}</td>
    </tr>
</table>

<!-- Assignment Information Section -->
<h2 class="section-header">Assignment Details</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell">Role:</td>
        <td class="value-cell">{{ assignment.role }}</td>
    </tr>
    <tr>
        <td class="label-cell">Supervisor:</td>
        <td class="value-cell">{{ assignment.supervisor }}</td>
    </tr>
    <tr>
        <td class="label-cell">Position Number:</td>
        <td class="value-cell">{{ assignment.position }}</td>
    </tr>
    <tr>
        <td class="label-cell">Employment Type:</td>
        <td class="value-cell">{{ assignment.permanent_vs_temporary }}</td>
    </tr>
    <tr>
        <td class="label-cell">Total FTE:</td>
        <td class="value-cell">{{ assignment.role_fte }}</td>
    </tr>
    <tr>
        <td class="label-cell">Service/Program:</td>
        <td class="value-cell">{{ assignment.service }}</td>
    </tr>
    <tr>
        <td class="label-cell">Start Date:</td>
        <td class="value-cell">{{ assignment.start_date }}</td>
    </tr>
    <tr>
        <td class="label-cell">End Date:</td>
        <td class="value-cell">{{ assignment.end_date }}</td>
    </tr>
</table>

<!-- Allocations Information Section -->
<h2 class="section-header">Allocation Details</h2>
{% if allocations %}
    {% for allocation in allocations %}
        {% if allocation.clinic or allocation.program or allocation.assignment_in_clinic or allocation.fte or allocation.centralized_vs_ric or allocation.monday or allocation.tuesday or allocation.wednesday or allocation.thursday or allocation.friday or allocation.start_date or allocation.end_date %}
            <h3>Allocation {{ forloop.counter }}</h3>
            <table border="0" cellspacing="2" class="table-container">
                    <tr>
                        <td class="label-cell">Clinic:</td>
                        <td class="value-cell">{{ allocation.clinic }}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Program/Team:</td>
                        <td class="value-cell">{{ allocation.program }}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Assignment in Clinic:</td>
                        <td class="value-cell">{{ allocation.assignment_in_clinic }}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">FTE Allocated:</td>
                        <td class="value-cell">{{ allocation.fte }}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Allocation Type:</td>
                        <td class="value-cell">{{ allocation.centralized_vs_ric }}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Monday:</td>
                        <td class="value-cell">{{ allocation.monday }}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Tuesday:</td>
                        <td class="value-cell">{{ allocation.tuesday }}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Wednesday:</td>
                        <td class="value-cell">{{ allocation.wednesday }}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Thursday:</td>
                        <td class="value-cell">{{ allocation.thursday }}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Friday:</td>
                        <td class="value-cell">{{ allocation.friday }}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">Start Date:</td>
                        <td class="value-cell">{{ allocation.start_date }}</td>
                    </tr>
                    <tr>
                        <td class="label-cell">End Date:</td>
                        <td class="value-cell">{{ allocation.end_date }}</td>
                    </tr>
                </table>
        {% endif %}
    {% endfor %}
{% else %}
    <p>No allocations available.</p>
{% endif %}

{% if staff.comment %}
<h2 class="section-header">Additional Comments</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell">Comment:</td>
        <td class="value-cell">{{ staff.comment }}</td>
    </tr>
</table>
{% endif %}
<p>
    Submitted by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).
</p>
{% if details_url %}
<p>
    <a href="{{ details_url }}">View record in operational database</a>
</p>
{% endif %}

<p>Best regards,</p>
<p>EOPCN Automation Admin</p>


</body>
</html>