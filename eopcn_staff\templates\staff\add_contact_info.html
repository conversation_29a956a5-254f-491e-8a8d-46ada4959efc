{% extends 'base.html' %}
{% load static %}

{% block content %}
<!-- Link to the external stylesheet -->
<link rel="stylesheet" type="text/css" href="{% static 'eopcn_staff/css/staff_form.css' %}">

<div class="form-container">
    <h2 class="subheading">Add Contact Info for {{ staff.first_name }} {{ staff.last_name }}</h2>
    
    <!-- Page Purpose and Instructions -->
    <div class="info-section">
        <h3>About This Form</h3>
        <p>This form manages how to contact staff based on their physical location on any given day. This is separate from work allocations, as staff may work remotely for one program while being physically located elsewhere.</p>
        
        <div class="important-notes">
            <h4>Important Information:</h4>
            <ul>
                <li><strong>Teams Integration:</strong> Contact information entered here directly updates the Staff List in MS Teams > EOPCN All Staff Team > Lists and Operational Info.</li>
                <li><strong>Automatic Updates:</strong> The Staff List updates automatically at 7:00 AM, 12:00 PM, and 10:00 PM daily.</li>
                <li><strong>Visibility:</strong> All staff can see the notes and contact information you add to this page through the Teams Staff List.</li>
                <li><strong>Physical vs. Work Location:</strong> Use this for where staff can physically be reached, not necessarily where they're allocated to work (e.g., staff may work remotely for one program but be physically in a different clinic).</li>
            </ul>
        </div>
    </div>

    <form method="post">
        {% csrf_token %}
        
        {% for field in form %}
            {% if field.name != 'comment' and field.name != 'email_leadership' and field.name != 'email_group' %}
            <div class="form-group">
                {{ field.label_tag }}
                {{ field }}
                {% if field.help_text %}
                    <small>{{ field.help_text }}</small>
                {% endif %}
                {% if field.errors %}
                    <div class="alert alert-danger">
                        {% for error in field.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            {% endif %}
        {% endfor %}
</div>

<div class="form-container">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <button type="submit" class="btn btn-primary">Save</button>
        <a href="{% url 'staff_detail' staff.pk %}" class="btn btn-secondary">Cancel</a>
    </div>
</div>
    </form>
</div>

<style>
    .info-section {
        background-color: #e8f4fd;
        border: 1px solid #b8daff;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .info-section h3 {
        color: #004085;
        margin-bottom: 10px;
    }

    .important-notes {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 12px;
        margin-top: 15px;
    }

    .important-notes h4 {
        color: #856404;
        margin-bottom: 8px;
    }

    .important-notes ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .important-notes li {
        margin-bottom: 5px;
    }
</style>
{% endblock %}
