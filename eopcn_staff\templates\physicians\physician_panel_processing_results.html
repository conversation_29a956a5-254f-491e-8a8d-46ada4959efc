{% extends "base.html" %}

{% block title %}Physician Panel Processing Results{% endblock %}

{% block content %}
<style>
body {
  font-family: arial, sans-serif;
  margin: 20px;
  padding: 0;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
}

.container {
  max-width: 1200px;
  margin: auto;
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 15px;
  border: 1px solid #c3e6cb;
  border-radius: 5px;
  margin-bottom: 20px;
}

.warning-message {
  background-color: #fff3cd;
  color: #856404;
  padding: 15px;
  border: 1px solid #ffeaa7;
  border-radius: 5px;
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #28a745;
}

.stat-number.total-records {
  color: #6c757d; /* Dark grey for total records */
}

.stat-number.unmatched {
  color: #ffc107; /* Yellow for unmatched physicians */
}

.stat-label {
  font-size: 1.1em;
  color: #666;
  margin-top: 10px;
}

.manual-match-form {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 10px;
  border: 1px solid #dee2e6;
}

.manual-match-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.physician-select {
  min-width: 300px;
  padding: 5px;
}

.match-button {
  background-color: #17a2b8;
  color: white;
  padding: 5px 15px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.match-button:hover {
  background-color: #138496;
}

.matched-indicator {
  color: #28a745;
  font-weight: bold;
}
</style>

<div class="container">
    <h2>Physician Panel Data Processing Results</h2>

    {% if processing_success %}
        <div class="success-message">
            <strong>Processing Completed Successfully!</strong><br>
            The staged data has been processed and physician matching has been completed.
        </div>
    {% endif %}

    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number total-records">{{ total_records }}</div>
            <div class="stat-label">Total Records Processed</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ matched_records }}</div>
            <div class="stat-label">Direct Matches with EOPCN Database</div>
        </div>
        <div class="stat-card">
            <div class="stat-number unmatched">{{ unmatched_count }}</div>
            <div class="stat-label">Unmatched Physicians</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ mapping_updates }}</div>
            <div class="stat-label">Matched via Name Mapping</div>
        </div>
    </div>

    {% if match_breakdown %}
        <div class="section">
            <h3>Match Type Breakdown</h3>
            <table class="unmatched-table">
                <thead>
                    <tr>
                        <th>Match Type</th>
                        <th>Count</th>
                    </tr>
                </thead>
                <tbody>
                    {% for match_type in match_breakdown %}
                        <tr>
                            <td>{{ match_type.status|default:"Direct Match" }}</td>
                            <td>{{ match_type.count }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}

    {% if unmatched_count > 0 %}
        <div class="section">
            <div class="warning-message">
                <strong>Attention Required:</strong> {{ unmatched_count }} physician(s) could not be matched with the main physician database. 
                You can manually match them below or add them to the physician name mapping table.
            </div>
            
            <h3>Unmatched Physicians - Manual Matching</h3>
            
            {% for physician in unmatched_physicians %}
                <div class="manual-match-form" id="physician-{{ forloop.counter }}">
                    <div style="display: flex; justify-content: between; align-items: center;">
                        <div style="flex: 1;">
                            <strong>Unmatched:</strong> {{ physician.full_name_concat }} 
                            <span style="color: #666;">({{ physician.last_name }}, {{ physician.first_name }})</span>
                        </div>
                        <div id="match-status-{{ forloop.counter }}" style="flex: 0 0 auto;"></div>
                    </div>
                    
                    <div class="manual-match-controls">
                        <label for="physician-select-{{ forloop.counter }}">Match with:</label>
                        <select id="physician-select-{{ forloop.counter }}" class="physician-select">
                            <option value="">-- Select a physician --</option>
                            {% for db_physician in all_physicians %}
                                <option value="{{ db_physician.physician_id }}">
                                    {% if db_physician.first_name and db_physician.last_name %}
                                        {{ db_physician.last_name }}, {{ db_physician.first_name }} (ID: {{ db_physician.physician_id }})
                                    {% else %}
                                        {{ db_physician.physician_name|default:"Unknown" }} (ID: {{ db_physician.physician_id }})
                                    {% endif %}
                                </option>
                            {% endfor %}
                        </select>
                        <button type="button" class="match-button" 
                                onclick="matchPhysician('{{ physician.last_name|escapejs }}', '{{ physician.first_name|escapejs }}', {{ forloop.counter }})">
                            Match
                        </button>
                    </div>
                </div>
            {% endfor %}
            
            <div style="margin-top: 20px;">
                <button type="button" class="button button-blue" onclick="refreshMatchStatus()">
                    Refresh Match Status
                </button>
            </div>
        </div>
    {% endif %}

    <div class="section">
        <h3>Next Steps</h3>
        {% if unmatched_count == 0 %}
            <p>All physicians have been successfully matched! You can now proceed to transfer the data to the master table.</p>
            <form method="post" action="{% url 'transfer_to_master_table' %}" style="display: inline;">
                {% csrf_token %}
                <button type="submit" class="button button-green">Transfer to Master Table</button>
            </form>
        {% else %}
            <p><strong>Cannot transfer to master table:</strong> {{ unmatched_count }} physician(s) still need to be matched manually above before proceeding.</p>
            <p>Please complete the manual matching process for all unmatched physicians to enable the transfer.</p>
        {% endif %}
        
        <div style="margin-top: 20px;">
            <a href="{% url 'physician_name_mapping_list' %}" class="button button-blue">Manage Physician Name Mappings</a>
        </div>
    </div>

<script>
function matchPhysician(lastName, firstName, rowIndex) {
    const selectElement = document.getElementById(`physician-select-${rowIndex}`);
    const selectedPhysicianId = selectElement.value;
    const statusElement = document.getElementById(`match-status-${rowIndex}`);
    
    if (!selectedPhysicianId) {
        alert('Please select a physician to match.');
        return;
    }
    
    // Show loading indicator
    statusElement.innerHTML = '<span style="color: #007bff;">Matching...</span>';
    
    // Send AJAX request to match the physician
    fetch('{% url "manual_physician_match" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify({
            'last_name': lastName,
            'first_name': firstName,
            'physician_id': selectedPhysicianId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusElement.innerHTML = '<span class="matched-indicator">✓ Matched!</span>';
            selectElement.disabled = true;
            // Update the stats
            updateMatchStats();
        } else {
            statusElement.innerHTML = '<span style="color: #dc3545;">Error: ' + data.error + '</span>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        statusElement.innerHTML = '<span style="color: #dc3545;">Network error</span>';
    });
}

function updateMatchStats() {
    // Refresh the statistics on the page
    fetch('{% url "get_match_statistics" %}')
    .then(response => response.json())
    .then(data => {
        // Update the stat cards
        document.querySelector('.stat-number:not(.total-records):not(.unmatched)').textContent = data.matched_records;
        document.querySelector('.stat-number.unmatched').textContent = data.unmatched_count;
    });
}

function refreshMatchStatus() {
    location.reload();
}
</script>

</div>

{% endblock %}
