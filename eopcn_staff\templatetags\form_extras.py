from django import template

register = template.Library()

@register.filter(name='add_class')
def add_class(field, css_class):
    try:
        return field.as_widget(attrs={"class": css_class})
    except AttributeError:
        return field

@register.filter(name='field_type')
def field_type(field, field_type):
    try:
        return field.as_widget(attrs={"type": field_type})
    except AttributeError:
        return field

