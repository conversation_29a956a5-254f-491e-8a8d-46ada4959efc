<!DOCTYPE html>
<html>
<head>
    <title>Profile Updated for {{ staff_member.first_name }} {{ staff_member.last_name }}</title>
</head>
<body>
    <h2>Profile Updated for {{ staff_member.first_name }} {{ staff_member.last_name }}</h2>

    <p>Hello,</p>

    <p>The profile for <strong>{{ staff_member.first_name }} {{ staff_member.last_name }}</strong> has been updated.</p>

    <p>Here are the updated details:</p>
    <ul>
        {% comment %} {% if staff_member.photo %}
        <li><strong>Photo:</strong> <img src="{{ staff_member.photo.url }}" alt="Staff Photo" width="100"></li>
        {% endif %} {% endcomment %}

        {% if staff_member.first_name %}
        <li><strong{% if changes.first_name %} style="background-color:#fff8c6;"{% endif %}>First Name:</strong>
            {% if changes.first_name %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.first_name.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.first_name.new_value }}</span>
            {% else %}
                {{ staff_member.first_name }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_member.last_name %}
        <li><strong{% if changes.last_name %} style="background-color:#fff8c6;"{% endif %}>Last Name:</strong>
            {% if changes.last_name %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.last_name.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.last_name.new_value }}</span>
            {% else %}
                {{ staff_member.last_name }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_member.start_date %}
        <li><strong{% if changes.start_date %} style="background-color:#fff8c6;"{% endif %}>Start Date:</strong>
            {% if changes.start_date %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.start_date.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.start_date.new_value }}</span>
            {% else %}
                {{ staff_member.start_date }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_member.suggested_email %}
        <li><strong{% if changes.suggested_email %} style="background-color:#fff8c6;"{% endif %}>Suggested Email:</strong>
            {% if changes.suggested_email %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.suggested_email.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.suggested_email.new_value }}</span>
            {% else %}
                {{ staff_member.suggested_email }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_member.end_date %}
        <li><strong{% if changes.end_date %} style="background-color:#fff8c6;"{% endif %}>End Date:</strong>
            {% if changes.end_date %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.end_date.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.end_date.new_value }}</span>
            {% else %}
                {{ staff_member.end_date }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_member.currently_active %}
        <li><strong{% if changes.currently_active %} style="background-color:#fff8c6;"{% endif %}>Currently Active:</strong>
            {% if changes.currently_active %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.currently_active.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.currently_active.new_value }}</span>
            {% else %}
                {{ staff_member.currently_active }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_member.n95_mask_size %}
        <li><strong{% if changes.n95_mask_size %} style="background-color:#fff8c6;"{% endif %}>N95 Mask Size:</strong>
            {% if changes.n95_mask_size %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.n95_mask_size.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.n95_mask_size.new_value }}</span>
            {% else %}
                {{ staff_member.n95_mask_size }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_member.office_number %}
        <li><strong{% if changes.office_number %} style="background-color:#fff8c6;"{% endif %}>Office Number:</strong>
            {% if changes.office_number %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.office_number.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.office_number.new_value }}</span>
            {% else %}
                {{ staff_member.office_number }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_member.desk_number %}
        <li><strong{% if changes.desk_number %} style="background-color:#fff8c6;"{% endif %}>Desk Number:</strong>
            {% if changes.desk_number %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.desk_number.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.desk_number.new_value }}</span>
            {% else %}
                {{ staff_member.desk_number }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_member.phone %}
        <li><strong{% if changes.phone %} style="background-color:#fff8c6;"{% endif %}>Phone:</strong>
            {% if changes.phone %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.phone.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.phone.new_value }}</span>
            {% else %}
                {{ staff_member.phone }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_member.ext %}
        <li><strong{% if changes.ext %} style="background-color:#fff8c6;"{% endif %}>Extension:</strong>
            {% if changes.ext %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.ext.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.ext.new_value }}</span>
            {% else %}
                {{ staff_member.ext }}
            {% endif %}
        </li>
        {% endif %}

        {% if staff_member.computer_number %}
        <li><strong{% if changes.computer_number %} style="background-color:#fff8c6;"{% endif %}>Computer Number:</strong>
            {% if changes.computer_number %}
                <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.computer_number.old_value }}</span> 
                → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.computer_number.new_value }}</span>
            {% else %}
                {{ staff_member.computer_number }}
            {% endif %}
        </li>
        {% endif %}
    </ul>



    {% if comment %}
    <p><strong>Comment:</strong> <span style="background-color:#DDEBF7">{{ comment }}</span></p>
    {% endif %}

    {% if details_url %}
    <p>
        <a href="{{ details_url }}">View record in operational database</a>
    </p>
    {% endif %}
    <p>
        Submitted by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).
    </p>

    <p>Best regards,</p>
    <p>EOPCN Automation Admin</p>
</body>
</html>
