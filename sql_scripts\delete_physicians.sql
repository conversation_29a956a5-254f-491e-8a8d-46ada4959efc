-- Script to delete physicians with IDs from 418 to 421
-- First, let's check what we're about to delete
SELECT physician_id, physician_name, first_name, last_name
FROM [dbo].[mh_physicians]
WHERE physician_id BETWEEN 418 AND 421;

-- Check if there are any related records in the mh_clinics_physicians table
SELECT cp.clinics_physicians_ID, cp.physician_id, cp.clinic_name, p.physician_name
FROM [dbo].[mh_clinics_physicians] cp
JOIN [dbo].[mh_physicians] p ON cp.physician_id = p.physician_id
WHERE p.physician_id BETWEEN 418 AND 421;

-- If you're sure you want to proceed, first delete the related clinic associations
-- This is necessary because of foreign key constraints
DELETE FROM [dbo].[mh_clinics_physicians]
WHERE physician_id BETWEEN 418 AND 421;

-- Now delete the physicians
DELETE FROM [dbo].[mh_physicians]
WHERE physician_id BETWEEN 418 AND 421;

-- Verify that the records were deleted
SELECT physician_id, physician_name, first_name, last_name
FRO<PERSON> [dbo].[mh_physicians]
WHERE physician_id BETWEEN 418 AND 421;
