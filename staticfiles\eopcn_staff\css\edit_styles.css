    .form-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .button-container {
        flex: 1;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border-radius: 8px;
        margin-top: 0px;
        margin-bottom: 20px;
    }

    .save-container {
        display: flex;
        justify-content: center;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .form-group {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .form-group label {
        width: auto;
        margin-right: 10px;
        text-align: left;
    }

    .form-group input, 
    .form-group select, 
    .form-group textarea {
        flex: 1;
        padding: 5px;
        max-width: 100%;
    }

    button {
        width: auto;
        padding: 10px 20px;
    }

    a.btn-secondary {
        margin-left: 10px;
        padding: 10px 20px;
        text-decoration: none;
        background-color: #6c757d;
        color: white;
        border-radius: 4px;
    }

    .allocation-form-row {
        margin-bottom: 20px;
    }

    hr {
        margin: 10px 0;
        border: 0;
        border-top: 1px solid #ccc;
    }

    .allocation-heading {
    text-align: left; /* Centers the text horizontally */
    font-weight: bold;  /* Optional: makes the text bold */
    margin: 10px 0;     /* Adds vertical margin around the text */
    }

    .subheading {
    text-align: center; /* Center alignment */
    }

    .radio-inline-container {
        /* Optional: style the container if needed */
        margin-bottom: 1em;
    }

    .radio-inline {
        display: flex;
        flex-wrap: nowrap;
        gap: 10px;
    }

    .radio-inline label {
        display: inline-flex;
        align-items: center;
        margin: 0;
    }

    .radio-inline input[type="radio"] {
        margin-right: 5px; /* small space between radio button & label text */
    }

    .label-container {
        width: 180px;       /* Adjust as needed */
        margin-right: 10px; /* Spacing between label and field */
        text-align: left;   /* Left-align the label text */
    }

    /* The field container expands to fill remaining space. */
    .field-container {
        flex: 1;
    }
