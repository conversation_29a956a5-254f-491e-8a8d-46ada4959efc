{% extends "base.html" %}

{% block title %}Clinic Primary Care Provider List{% endblock %}

{% block content %}
<!DOCTYPE html>
<html>
<head>
<style>
body {
  font-family: arial, sans-serif;
  margin: 0;
  padding: 0;
}

.column {
  float: left;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.row::after {
  content: "";
  clear: both;
  display: table;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #dddddd;
}

h2 {
  text-align: center;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 4px;
}

.button1 {
    background-color: #008CBA;
}

.button:hover {
    background-color: #006e92;
}

.link-button {
    display: inline-block;
    padding: 5px 10px;
    font-size: 12px;
    background-color: #73b3f8;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    margin-right: 5px;
}

.link-button:hover {
    background-color: #0056b3;
}

.dataTables_filter, .dataTables_length {
    padding-bottom: 10px;
}

.dataTables_filter {
    float: left !important;
    text-align: left !important;
}

.dataTables_length {
    float: right !important;
    text-align: right !important;
}

@media screen and (max-width: 650px) {
  .column {
    width: 100%;
    display: block;
  }
}

.greyed-out {
  background-color: #f0f0f0;
  color: #888888;
}
</style>
</head>
<body>

<h2>Primary Care Providers In Clinics</h2>

<label>
  <input type="checkbox" id="toggleActive" checked /> Show primary care providers' active clinics only
</label>

<div class="row">
  <div class="column">
    <table id="pcndatatable" class="display">
      <thead>
        <tr>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Clinic Name</th>
            <th>Portion of Practice</th>
            <th>Accepting Patients</th>
            <th>Include on AFAD</th>
            <th>Date Active in Clinic</th>
            <th>Date Left Clinic</th>
            <th>Date Modified</th>
            <th>Date Created</th>
        </tr>
      </thead>    
      <tbody>
        {% for clinic_physician in clinic_physicians %}
        <tr {% if clinic_physician.date_left_clinic %}class="greyed-out role-ended"{% endif %}>
            <td><a href="{% url 'physician_detail' clinic_physician.physician.physician_id %}">{{ clinic_physician.physician.first_name }}</a></td>
            <td><a href="{% url 'physician_detail' clinic_physician.physician.physician_id %}">{{ clinic_physician.physician.last_name }}</a></td>
            <td>
                {% if clinic_physician.clinic.clinic_id %}
                    <a href="{% url 'clinic_detail' clinic_physician.clinic.clinic_id %}">{{ clinic_physician.clinic.clinic_name }}</a>
                {% else %}
                    {{ clinic_physician.clinic.clinic_name }}
                {% endif %}
            </td>
            <td>{{ clinic_physician.portion_of_practice|default_if_none:"N/A" }}</td>
            <td>{% if clinic_physician.accepting_patients is not None %}{{ clinic_physician.accepting_patients }}{% endif %}</td>
            <td>{% if clinic_physician.include_on_afad_website is not None %}{{ clinic_physician.include_on_afad_website }}{% endif %}</td>
            <td data-order="{{ clinic_physician.date_active_in_clinic|date:"Y-m-d" }}">{{ clinic_physician.date_active_in_clinic|date:"M. j, Y" }}</td>
            <td data-order="{{ clinic_physician.date_left_clinic|date:"Y-m-d" }}">{{ clinic_physician.date_left_clinic|date:"M. j, Y" }}</td>
            <td data-order="{{ clinic_physician.date_modified|date:"Y-m-d" }}">{{ clinic_physician.date_modified|date:"M. j, Y" }}</td>
            <td data-order="{{ clinic_physician.date_created|date:"Y-m-d" }}">{{ clinic_physician.date_created|date:"M. j, Y" }}</td>
          </tr>
        {% endfor %}
      </tbody>    
    </table>
  </div>
</div>

{% load static %}
<script src="{% static 'eopcn_staff/js/datatables.js' %}"></script>

</body>
</html>
{% endblock %}
