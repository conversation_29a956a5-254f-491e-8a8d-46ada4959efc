{% extends "base.html" %}

{% block title %}Edit Staff Member{% endblock %}

{% block content %}
{% load form_extras %}
{% load static %}

<div class="form-container">
    <h2 class="subheading">Edit Assignment for {{ staff.first_name }} {{ staff.last_name }}</h2>
    
    <!-- Page Purpose and Instructions -->
    <div class="info-section">
        <h3>About This Form</h3>
        <p>This section manages the staff member's role assignment within the EOPCN organization - their hired position, supervisor, and FTE allocation.</p>
        
        <div class="important-notes">
            <h4>Important Guidelines:</h4>
            <ul>
                <li><strong>Staff on Leave:</strong> Only update "Currently Active" to inactive/unchecked. Staff retain their role but are temporarily not active in their assigned position. Do NOT add an end date for leave.</li>
                <li><strong>Staff Leaving Organization or Changing Roles:</strong> Add an end date when staff permanently leave the organization OR move to a different role assignment.</li>
                <li><strong>Supervisor Changes:</strong> Simply overwrite the current supervisor field and save to update.</li>
                <li><strong>Position Numbers:</strong> When a staff member becomes inactive or their role is end-dated, their position number becomes available for reassignment to another staff member.</li>
            </ul>
        </div>
    </div>

    <form id="editStaffForm" method="post" enctype="multipart/form-data">
        {% csrf_token %}

        <!-- Display Errors for the Form -->
        {% if form.errors %}
            <div class="alert alert-danger">
                <ul>
                    {% for field, errors in form.errors.items %}
                        <li>{{ field }}: {{ errors|join:", " }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}

        <div class="form-group">
            {{ form.role.label_tag }} 
            {{ form.role }}
        </div>
        <div class="helper-text">
            <small class="form-text text-muted">ℹ️ Missing a role? <a href="{% url 'staff_roles_list' %}" target="_blank">Add roles here</a></small>
        </div>

        <div class="form-group">
            {{ form.supervisor.label_tag }} 
            {{ form.supervisor }}
        </div>
        <div class="helper-text">
            <small class="form-text text-muted">ℹ️ Missing a supervisor? <a href="{% url 'supervisor_list' %}" target="_blank">Add supervisors here</a></small>
        </div>

        <div class="form-group">
            {{ form.position.label_tag }} 
            {{ form.position }}
        </div>
        <div class="helper-text">
            <small class="form-text text-muted">ℹ️ Only available position numbers are listed. Missing a position? <a href="{% url 'position_list' %}" target="_blank">Check position availability here</a></small>
        </div>

        <div class="form-group">
            {{ form.permanent_vs_temporary.label_tag }}<br>
            {% for radio in form.permanent_vs_temporary %}
                <div class="form-check form-check-inline">
                    {{ radio.tag }}
                    <label class="form-check-label">{{ radio.choice_label }}</label>
                </div>
            {% endfor %}
        </div>

        <div class="form-group">
            {{ form.role_fte.label_tag }} 
            {{ form.role_fte }}
        </div>

        <div class="form-group">
            {{ form.service.label_tag }} 
            {{ form.service }}
        </div>
        <div class="helper-text">
            <small class="form-text text-muted">ℹ️ Missing a program/team? <a href="{% url 'program_list' %}" target="_blank">Add programs here</a></small>
        </div>

        <div class="form-group">
            {{ form.start_date.label_tag }} 
            {{ form.start_date }}
        </div>

        <div class="form-group">
            {{ form.end_date.label_tag }} 
            {{ form.end_date }}
        </div>
        <div class="helper-text">
            <small class="form-text text-muted">⚠️ Only set when staff leaves the organization or moves to another role assignment</small>
        </div>

        <div class="form-group form-check">
            {{ form.currently_active.label_tag }} 
            {{ form.currently_active }}
        </div>
        <div class="helper-text">
            <small class="form-text text-muted">ℹ️ Set to inactive only for staff on leave (no end date) or staff who have left/moved to new role</small>
        </div>
        </div>

<!-- Email Group Selection -->
<div class="form-container">
    {% with form=form %}
        {% include 'staff/email_group_widget.html' %}
    {% endwith %}
</div>

<div class="form-container">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <button type="submit" class="btn btn-primary mt-3">Save</button>
        <button type="submit" name="delete_assignment" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this assignment? This action cannot be undone.');">Delete Assignment</button>
    </div>
</div>
    </form>

    <script src="{% static 'eopcn_staff/js/edit_staff.js' %}"></script>


    {% comment %} <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Event listener for adding new assignment forms
            const addAssignmentBtn = document.getElementById('add-assignment');
            addAssignmentBtn.addEventListener('click', add_new_assignment_form);

            function add_new_assignment_form(event) {
                if (event) {
                    event.preventDefault();
                }

                const formCopyTarget = document.getElementById('assignment-formset');
                console.log(formCopyTarget);
                const totalForms = document.getElementById('id_assignments-TOTAL_FORMS');
                console.log(totalForms.value);
                const currentFormCount = parseInt(totalForms.value);
                console.log("Current form count:", currentFormCount);

                // Clone the last form in the assignment formset
                const lastForm = document.querySelector('.assignment-form-row:last-of-type');
                const newFormEl = lastForm.cloneNode(true);

                // Update form indices and clear data
                updateAssignmentFormIndices(newFormEl, currentFormCount);

                // Increment the total number of forms in the management form
                totalForms.value = currentFormCount + 1;

                // Append the newly cloned form into the formset container
                formCopyTarget.appendChild(newFormEl);

                // Add numbering to the new form (if desired)
                addAssignmentNumbering(newFormEl, currentFormCount + 1);

                // Attach any necessary event listeners for the new form
                attachAssignmentToggleEvents(currentFormCount);
            }

            function updateAssignmentFormIndices(formEl, newIndex) {
                const formRegex = /assignments-(\d+|__prefix__)-/g;

                formEl.querySelectorAll('*').forEach(function(element) {
                    if (element.name) {
                        element.name = element.name.replace(formRegex, `assignments-${newIndex}-`);
                    }
                    if (element.id) {
                        element.id = element.id.replace(formRegex, `assignments-${newIndex}-`);
                    }
                    if (element.getAttribute('for')) {
                        element.setAttribute('for', element.getAttribute('for').replace(formRegex, `assignments-${newIndex}-`));
                    }

                    // Clear the value of input fields
                    if (element.tagName === 'INPUT' || element.tagName === 'SELECT' || element.tagName === 'TEXTAREA') {
                        if (element.type === 'checkbox' || element.type === 'radio') {
                            element.checked = false;
                        } else if (element.type !== 'hidden' && element.name !== 'csrfmiddlewaretoken') {
                            element.value = '';
                        }
                    }
                });
            }

            function attachEventsToExistingAllocationForms() {
                const totalFormsElement = document.getElementById('id_allocations-TOTAL_FORMS');
                
                if (!totalFormsElement) {
                    console.error('Element with ID "id_allocations-TOTAL_FORMS" not found.');
                    return;
                }

                const totalForms = parseInt(totalFormsElement.value);

                for (let i = 0; i < totalForms; i++) {
                    attachAllocationToggleEvents(i);
                }
            }

            function addAssignmentNumbering(formEl, number) {
                const heading = formEl.querySelector('h4');
                if (heading) {
                    heading.textContent = 'Role ' + number;
                }
            }

            function attachAssignmentToggleEvents(formIndex) {
                const formPrefix = `assignments-${formIndex}`;
                const centralizedVsRicRadios = document.querySelectorAll(`input[name="${formPrefix}-centralized_vs_ric"]`);

                centralizedVsRicRadios.forEach(radio => {
                    radio.addEventListener('change', toggleFields);
                });

                function toggleFields() {
                    const selectedValue = document.querySelector(`input[name="${formPrefix}-centralized_vs_ric"]:checked`);
                    const clinicInput = document.getElementById(`id_${formPrefix}-clinic`);
                    const assignmentInClinicInput = document.getElementById(`id_${formPrefix}-assignment_in_clinic`);
                    const programInput = document.getElementById(`id_${formPrefix}-program`);

                    const clinicField = clinicInput ? clinicInput.closest('.form-group') : null;
                    const assignmentInClinicField = assignmentInClinicInput ? assignmentInClinicInput.closest('.form-group') : null;
                    const programField = programInput ? programInput.closest('.form-group') : null;

                    if (selectedValue) {
                        if (selectedValue.value === 'RIC/Decentralized') {
                            if (clinicField) clinicField.style.display = 'none';
                            if (assignmentInClinicField) assignmentInClinicField.style.display = 'none';
                            if (programField) programField.style.display = '';
                        } else {
                            if (clinicField) clinicField.style.display = '';
                            if (assignmentInClinicField) assignmentInClinicField.style.display = '';
                            if (programField) programField.style.display = 'none';
                        }
                    } else {
                        if (clinicField) clinicField.style.display = 'none';
                        if (assignmentInClinicField) assignmentInClinicField.style.display = 'none';
                        if (programField) programField.style.display = 'none';
                    }
                }

                toggleFields();
            }

            function attachEventsToExistingAssignmentForms() {
                const totalForms = parseInt(document.getElementById('id_assignments-TOTAL_FORMS').value);

                for (let i = 0; i < totalForms; i++) {
                    attachAssignmentToggleEvents(i);
                }
            }

            // Call the function to attach events to existing assignment forms
            attachEventsToExistingAssignmentForms();

            // Event listener for adding new allocation forms
            document.addEventListener('click', function(event) {
                if (event.target && event.target.classList.contains('add-allocation')) {
                    add_new_allocation_form(event);
                }
            });

            function add_new_allocation_form(event) {
                if (event) {
                    event.preventDefault();
                }

                const formCopyTarget = event.target.closest('.allocation-formset');
                const totalForms = formCopyTarget.querySelector('[name$="-TOTAL_FORMS"]');
                const currentFormCount = parseInt(totalForms.value);

                // Clone the last form in the allocation formset
                const lastForm = formCopyTarget.querySelector('.allocation-form-row:last-of-type');
                const newFormEl = lastForm.cloneNode(true);

                // Update form indices and clear data
                updateAllocationFormIndices(newFormEl, currentFormCount);

                // Increment the total number of forms in the management form
                totalForms.value = currentFormCount + 1;

                // Append the newly cloned form into the formset container
                formCopyTarget.appendChild(newFormEl);

                // Add numbering to the new form (if desired)
                addAllocationNumbering(newFormEl, currentFormCount + 1);

                // Attach any necessary event listeners for the new form
                attachAllocationToggleEvents(currentFormCount);
            }

            function updateAllocationFormIndices(formEl, newIndex) {
                const formRegex = /allocations-(\d+|__prefix__)-/g;

                formEl.querySelectorAll('*').forEach(function(element) {
                    if (element.name) {
                        element.name = element.name.replace(formRegex, `allocations-${newIndex}-`);
                    }
                    if (element.id) {
                        element.id = element.id.replace(formRegex, `allocations-${newIndex}-`);
                    }
                    if (element.getAttribute('for')) {
                        element.setAttribute('for', element.getAttribute('for').replace(formRegex, `allocations-${newIndex}-`));
                    }

                    // Clear the value of input fields
                    if (element.tagName === 'INPUT' || element.tagName === 'SELECT' || element.tagName === 'TEXTAREA') {
                        if (element.type === 'checkbox' || element.type === 'radio') {
                            element.checked = false;
                        } else if (element.type !== 'hidden' && element.name !== 'csrfmiddlewaretoken') {
                            element.value = '';
                        }
                    }
                });
            }

            function addAllocationNumbering(formEl, number) {
                const heading = formEl.querySelector('h5');
                if (heading) {
                    heading.textContent = 'Allocation ' + number;
                }
            }

            function attachAllocationToggleEvents(formIndex) {
                const formPrefix = `allocations-${formIndex}`;
                const centralizedVsRicRadios = document.querySelectorAll(`input[name="${formPrefix}-centralized_vs_ric"]`);

                centralizedVsRicRadios.forEach(radio => {
                    radio.addEventListener('change', toggleFields);
                });

                function toggleFields() {
                    const selectedValue = document.querySelector(`input[name="${formPrefix}-centralized_vs_ric"]:checked`);
                    const clinicInput = document.getElementById(`id_${formPrefix}-clinic`);
                    const assignmentInClinicInput = document.getElementById(`id_${formPrefix}-assignment_in_clinic`);
                    const programInput = document.getElementById(`id_${formPrefix}-program`);

                    const clinicField = clinicInput ? clinicInput.closest('.form-group') : null;
                    const assignmentInClinicField = assignmentInClinicInput ? assignmentInClinicInput.closest('.form-group') : null;
                    const programField = programInput ? programInput.closest('.form-group') : null;

                    if (selectedValue) {
                        if (selectedValue.value === 'RIC/Decentralized') {
                            if (clinicField) clinicField.style.display = 'none';
                            if (assignmentInClinicField) assignmentInClinicField.style.display = 'none';
                            if (programField) programField.style.display = '';
                        } else {
                            if (clinicField) clinicField.style.display = '';
                            if (assignmentInClinicField) assignmentInClinicField.style.display = '';
                            if (programField) programField.style.display = 'none';
                        }
                    } else {
                        if (clinicField) clinicField.style.display = 'none';
                        if (assignmentInClinicField) assignmentInClinicField.style.display = 'none';
                        if (programField) programField.style.display = 'none';
                    }
                }

                toggleFields();
            }
        });
    </script> {% endcomment %}

{% comment %} 
<script>
    document.addEventListener('DOMContentLoaded', function() {
    const addAssignmentBtn = document.getElementById('add-assignment');
    addAssignmentBtn.addEventListener('click', add_new_assignment_form);

    function add_new_assignment_form(event) {
        if (event) {
            event.preventDefault();
        }

        const formCopyTarget = document.getElementById('assignment-formset');
        const totalForms = document.getElementById('id_assignments-TOTAL_FORMS');
        const currentFormCount = parseInt(totalForms.value);

        // Clone the last form in the assignment formset
        const lastForm = document.querySelector('.assignment-form-row:last-of-type');
        const newFormEl = lastForm.cloneNode(true);

        // Update form indices and clear data
        updateAssignmentFormIndices(newFormEl, currentFormCount);

        // Increment the total number of forms in the management form
        totalForms.value = currentFormCount + 1;

        // Append the newly cloned form into the formset container
        formCopyTarget.appendChild(newFormEl);

        // Add numbering to the new form (if desired)
        addAssignmentNumbering(newFormEl, currentFormCount + 1);

        // Attach any necessary event listeners for the new form
        attachAssignmentToggleEvents(currentFormCount);
    }

    function updateAssignmentFormIndices(formEl, newIndex) {
        const formRegex = /assignments-(\d+|__prefix__)-/g;

        formEl.querySelectorAll('*').forEach(function(element) {
            if (element.name) {
                element.name = element.name.replace(formRegex, `assignments-${newIndex}-`);
            }
            if (element.id) {
                element.id = element.id.replace(formRegex, `assignments-${newIndex}-`);
            }
            if (element.getAttribute('for')) {
                element.setAttribute('for', element.getAttribute('for').replace(formRegex, `assignments-${newIndex}-`));
            }

            // Clear the value of input fields
            if (element.tagName === 'INPUT' || element.tagName === 'SELECT' || element.tagName === 'TEXTAREA') {
                if (element.type === 'checkbox' || element.type === 'radio') {
                    element.checked = false;
                } else if (element.type !== 'hidden' && element.name !== 'csrfmiddlewaretoken') {
                    element.value = '';
                }
            }
        });
    }

    function addAssignmentNumbering(formEl, number) {
        const heading = formEl.querySelector('h4');
        if (heading) {
            heading.textContent = 'Assignment ' + number;
        }
    }

    function attachAssignmentToggleEvents(formIndex) {
        const formPrefix = `assignments-${formIndex}`;

        // Add any specific event handling for toggling assignment fields, if necessary.
        // Example: Adding logic for toggling fields based on selection (similar to what you've done with centralized_vs_ric for allocation forms).
    }

    function attachEventsToExistingAssignmentForms() {
        const totalForms = parseInt(document.getElementById('id_assignments-TOTAL_FORMS').value);

        for (let i = 0; i < totalForms; i++) {
            attachAssignmentToggleEvents(i);
        }
    }

    // Call the function to attach events to existing assignment forms
    attachEventsToExistingAssignmentForms();
});

</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const addMoreBtn = document.getElementById('add-allocation');
    addMoreBtn.addEventListener('click', add_new_form);

    function add_new_form(event) {
        if (event) {
            event.preventDefault();
        }

        const formCopyTarget = document.getElementById('allocation-formset');
        const totalForms = document.getElementById('id_allocations-TOTAL_FORMS'); // Updated ID
        const currentFormCount = parseInt(totalForms.value);

        // Clone the last form in the formset
        const lastForm = document.querySelector('.allocation-form-row:last-of-type');
        const newFormEl = lastForm.cloneNode(true);

        // Update form indices and clear data
        updateFormIndices(newFormEl, currentFormCount);

        // Increment the total number of forms in the management form
        totalForms.value = currentFormCount + 1;

        // Append the newly cloned form into the formset container
        formCopyTarget.appendChild(newFormEl);

        // Add numbering to the new form
        addNumbering(newFormEl, currentFormCount + 1);

        // Attach event listeners to the new form
        attachToggleEvents(currentFormCount);
    }

    function updateFormIndices(formEl, newIndex) {
        const formRegex = /allocations-(\d+|__prefix__)-/g;  // Updated regex to use allocations-

        formEl.querySelectorAll('*').forEach(function(element) {
            if (element.name) {
                element.name = element.name.replace(formRegex, `allocations-${newIndex}-`); // Updated to allocations-
            }
            if (element.id) {
                element.id = element.id.replace(formRegex, `allocations-${newIndex}-`);  // Updated to allocations-
            }
            if (element.getAttribute('for')) {
                element.setAttribute('for', element.getAttribute('for').replace(formRegex, `allocations-${newIndex}-`));  // Updated to allocations-
            }

            // Clear the value of input fields
            if (element.tagName === 'INPUT' || element.tagName === 'SELECT' || element.tagName === 'TEXTAREA') {
                if (element.type === 'checkbox' || element.type === 'radio') {
                    element.checked = false;
                } else if (element.type !== 'hidden' && element.name !== 'csrfmiddlewaretoken') {
                    element.value = '';
                }
            }
        });
    }

    function addNumbering(formEl, number) {
        const heading = formEl.querySelector('h4');
        if (heading) {
            heading.textContent = 'Allocation ' + number;
        }
    }

    function attachToggleEvents(formIndex) {
        const formPrefix = `allocations-${formIndex}`;  // Updated to allocations-

        // Select the radio buttons for this formset
        const centralizedVsRicRadios = document.querySelectorAll(`input[name="${formPrefix}-centralized_vs_ric"]`);

        // Select the fields to toggle for this formset by using dynamic IDs
        const clinicInput = document.getElementById(`id_${formPrefix}-clinic`);
        const assignmentInClinicInput = document.getElementById(`id_${formPrefix}-assignment_in_clinic`);
        const programInput = document.getElementById(`id_${formPrefix}-program`);

        // Check if the inputs exist and get their parent .form-group elements
        const clinicField = clinicInput ? clinicInput.closest('.form-group') : null;
        const assignmentInClinicField = assignmentInClinicInput ? assignmentInClinicInput.closest('.form-group') : null;
        const programField = programInput ? programInput.closest('.form-group') : null;

        // Function to hide all fields
        function hideFields() {
            if (clinicField) clinicField.style.display = 'none';
            if (assignmentInClinicField) assignmentInClinicField.style.display = 'none';
            if (programField) programField.style.display = 'none';
        }

        // Function to toggle fields based on the selected radio button for this formset
        function toggleFields() {
            const selectedValue = document.querySelector(`input[name="${formPrefix}-centralized_vs_ric"]:checked`);
            if (selectedValue) {
                if (selectedValue.value === 'RIC/Decentralized') {
                    if (clinicField) clinicField.style.display = '';
                    if (assignmentInClinicField) assignmentInClinicField.style.display = '';
                    if (programField) programField.style.display = 'none';
                } else if (selectedValue.value === 'Centralized') {
                    if (clinicField) clinicField.style.display = 'none';
                    if (assignmentInClinicField) assignmentInClinicField.style.display = 'none';
                    if (programField) programField.style.display = '';
                }
            } else {
                hideFields();
            }
        }

        // Initially hide all fields when the form is added
        hideFields();

        // Toggle fields based on current selection
        toggleFields();

        // Attach change event listeners to radio buttons to toggle fields dynamically
        centralizedVsRicRadios.forEach(radio => {
            radio.addEventListener('change', toggleFields);
        });
    }

    function attachEventsToExistingForms() {
        const totalForms = parseInt(document.getElementById('id_allocations-TOTAL_FORMS').value);  // Updated ID

        for (let i = 0; i < totalForms; i++) {
            attachToggleEvents(i);
        }
    }

    // Call the function to attach events to existing forms
    attachEventsToExistingForms();
});
</script> {% endcomment %}


<style>
    .form-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .button-container {
        flex: 1;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border-radius: 8px;
        margin-top: 0px;
        margin-bottom: 20px;
    }

    .save-container {
        display: flex;
        justify-content: center;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .form-group {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .form-group label {
        width: 200px;
        margin-right: 10px;
        text-align: left;
    }

    .form-group input, 
    .form-group select, 
    .form-group textarea {
        flex: 1;
        padding: 5px;
        max-width: 100%;
    }

    button {
        width: auto;
        padding: 10px 20px;
    }

    a.btn-secondary {
        margin-left: 10px;
        padding: 10px 20px;
        text-decoration: none;
        background-color: #6c757d;
        color: white;
        border-radius: 4px;
    }

    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .allocation-form-row {
        margin-bottom: 20px;
    }

    hr {
        margin: 10px 0;
        border: 0;
        border-top: 1px solid #ccc;
    }

    .allocation-heading {
    text-align: left; /* Centers the text horizontally */
    font-weight: bold;  /* Optional: makes the text bold */
    margin: 10px 0;     /* Adds vertical margin around the text */
    }

    .subheading {
    text-align: center; /* Center alignment */
    }

    .info-section {
        background-color: #e8f4fd;
        border: 1px solid #b8daff;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .info-section h3 {
        color: #004085;
        margin-bottom: 10px;
    }

    .important-notes {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 12px;
        margin-top: 15px;
    }

    .important-notes h4 {
        color: #856404;
        margin-bottom: 8px;
    }

    .important-notes ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .important-notes li {
        margin-bottom: 5px;
    }

    .form-text {
        font-size: 0.875em;
        color: #6c757d;
        margin-left: 10px;
    }

    .helper-text {
        margin-left: 210px;
        margin-top: -10px;
        margin-bottom: 15px;
    }

</style>

{% endblock %}
