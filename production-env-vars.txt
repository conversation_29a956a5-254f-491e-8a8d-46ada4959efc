# Production Environment Variables for Azure App Service
# Copy these values to your Azure App Service Configuration > Application settings
# ⚠️  WARNING: Use STRONG, UNIQUE values for production - these are your current dev values

# Required for Production:

SECRET_KEY=django-insecure-&6kj1hz@1e0dl&jjeg$$j%_2=d5wq_*kdw4bixbjhvnxs_zvjv
DJANGO_ENVIRONMENT=production

AZURE_CLIENT_ID=d7628e66-31af-4eee-8290-fbb45a23f7fa
AZURE_CLIENT_SECRET=****************************************
AZURE_TENANT_ID=8303f5d5-24fe-4856-9015-25d9a4ee9400

EMAIL_HOST_PASSWORD=Juh49280

AZURE_STORAGE_KEY=dOcf7w0qB79ajxzf+gyoKmpYO+WWLhPCDf8XH6YnOtjZzajL2pMrjCRzLn8hWWVIRAqsdSkx7zqR+ASt6A349w==

# Optional (only if NOT using Managed Identity for database):
DB_USER=<EMAIL>
DB_PASSWORD=Juh49280

# Instructions for Azure App Service:
# 1. Go to Azure Portal > App Services > Your App
# 2. Navigate to Configuration > Application settings
# 3. Click "New application setting" for each variable above
# 4. Copy the name and value exactly as shown
# 5. Click "Save" after adding all variables
# 6. Restart your app service

# Security Recommendations for Production:
# 1. Generate a new SECRET_KEY using: python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
# 2. Consider using Azure Key Vault for sensitive values
# 3. Enable Managed Identity for database access (removes need for DB_USER/DB_PASSWORD)
# 4. Rotate secrets regularly
