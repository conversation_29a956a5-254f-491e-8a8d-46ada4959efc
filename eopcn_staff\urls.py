from django.urls import path, include
from . import views
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.auth import views as auth_views
from django.contrib import admin


urlpatterns = [
    path('', views.list_staff, name='list_staff'),  # URL for listing staff members
    path('staff/<int:pk>/', views.staff_detail, name='staff_detail'),  # URL for staff details
    path('staff/add_new/', views.add_staff, name='add_staff_form'),
    path('staff/add_assignment/<int:pk>', views.add_staff_assignment, name='add_staff_assignment'),
    path('staff/edit_assignment/<int:pk>', views.edit_assignment, name='edit_assignment'),
    path('staff/edit_profile/<int:pk>', views.edit_profile, name='edit_profile'),
    path('staff/edit_allocation/<int:pk>', views.edit_allocation, name='edit_allocation'),
    path('staff/delete/<int:pk>', views.delete_staff, name='delete_staff'),
    path('physicians/', views.physician_list, name='physician_list'),
    path('physicians_in_clinics/', views.clinic_physician_list, name='clinic_physician_list'),  # Updated URL pattern
    path('clinics/', views.clinic_list, name='clinic_list'),
    path('clinics/<int:clinic_id>/', views.clinic_detail, name='clinic_detail'),
    path('clinics/add/', views.add_clinic, name='add_clinic'),
    path('clinics/<int:clinic_id>/edit/', views.edit_clinic, name='edit_clinic'),
    path('staff/staff_leave/add/<int:pk>/', views.add_staff_leave, name='add_staff_leave'),
    path('staff/staff_leave/edit/<int:pk>/', views.edit_staff_leave, name='edit_staff_leave'),
    path('staff/add_position/', views.add_position, name='add_position'),
    path('staff/positions/', views.position_list, name='position_list'),
    path('staff/positions/edit/<int:position_id>/', views.edit_position, name='edit_position'),
    path('staff/positions/delete/<int:position_id>/', views.delete_position, name='delete_position'),
    path('staff/supervisors/', views.supervisor_list, name='supervisor_list'),
    path('staff/supervisors/add/', views.add_supervisor, name='add_supervisor'),
    path('staff/supervisors/edit/<int:pk>/', views.edit_supervisor, name='edit_supervisor'),
    path('staff/assignments_in_clinic/', views.assignments_in_clinic_list, name='assignments_in_clinic_list'),
    path('staff/assignments_in_clinic/add/', views.add_assignments_in_clinic, name='add_assignments_in_clinic'),
    path('staff/assignments_in_clinic/edit/<int:pk>/', views.edit_assignments_in_clinic, name='edit_assignments_in_clinic'),
    path('staff/services/', views.service_list, name='service_list'),
    path('staff/services/add/', views.add_service, name='add_service'),
    path('staff/services/edit/<int:pk>/', views.edit_service, name='edit_service'),
    path('staff/programs/', views.program_list, name='program_list'),
    path('staff/programs/add/', views.add_program, name='add_program'),
    path('staff/programs/edit/<int:pk>/', views.edit_program, name='edit_program'),
    path('staff/lists/', views.lists, name='lists'),
    path('staff/leaves/', views.staff_leaves_view, name='staff_leaves'),
    path('staff/roles/', views.staff_roles_list, name='staff_roles_list'),
    path('staff/roles/add/', views.add_staff_role, name='add_staff_role'),
    path('staff/roles/edit/<int:pk>/', views.edit_staff_role, name='edit_staff_role'),
    # path('login/', auth_views.LoginView.as_view(template_name='staff/login.html'), name='login'),  # Login URL
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),  # Logout URL
    # path('admin/', admin.site.urls),  # Admin URL
    path('physician_panel_details/', views.physician_panel_details_view, name='physician_panel_details'),
    path('physician_panel_master/', views.physician_panel_master_view, name='physician_panel_master'),
    path('clinic_staff/', views.clinic_staff_list, name='clinic_staff_list'),
    path('staff/seating_map', views.seating_map, name='seating_map'),
    path('staff/<int:staff_id>/add_contact/', views.add_contact_info, name='add_contact_info'),
    path('staff/contact/<int:contact_id>/edit/', views.edit_contact_info, name='edit_contact_info'),
    path('physician/<int:physician_id>/', views.physician_detail, name='physician_detail'),
    path('physician/<int:physician_id>/edit/', views.edit_physician, name='edit_physician'),
    path('physician/<int:physician_id>/edit/clinics/', views.edit_physician_clinics, name='edit_physician_clinics'),
    path('physicians/add/', views.add_physician, name='add_physician'),
    path('physicians/import_panel/', views.physician_panel_import_view, name='physician_panel_import'),
    path('physicians/process_panel_upload/', views.process_physician_panel_upload, name='process_physician_panel_upload'),
    path('physicians/process_staged_data/', views.process_staged_panel_data_view, name='process_staged_panel_data'),
    path('physicians/manual_match/', views.manual_physician_match, name='manual_physician_match'),
    path('physicians/match_statistics/', views.get_match_statistics, name='get_match_statistics'),
    path('physicians/transfer_to_master/', views.transfer_to_master_table, name='transfer_to_master_table'),
    path('physicians/name_mappings/', views.physician_name_mapping_list, name='physician_name_mapping_list'),
    path('physicians/name_mappings/add/', views.add_physician_name_mapping, name='add_physician_name_mapping'),
    path('physicians/name_mappings/edit/<int:mapping_id>/', views.edit_physician_name_mapping, name='edit_physician_name_mapping'),


    # Azure AD authentication URLs
    path('oauth2/', include('django_auth_adfs.urls')),  # Matches LOGIN_URL in settings.py
    path('supervisor/<int:supervisor_id>/', views.supervisor_staff_view, name='supervisor_staff'),    # Clinic note URLs - make sure these are included
    path('clinic/<int:clinic_id>/add-note/', views.add_clinic_note, name='add_clinic_note'),
    path('clinic-note/<int:note_id>/edit/', views.edit_clinic_note, name='edit_clinic_note'),    # Email management URLs
    path('email-groups/', views.email_groups_list, name='email_groups_list'),
    path('email-groups/add/', views.add_email_group, name='add_email_group'),
    path('email-groups/<int:group_id>/', views.email_group_detail, name='email_group_detail'),
    path('email-groups/<int:group_id>/edit/', views.edit_email_group, name='edit_email_group'),
    path('email-groups/<int:group_id>/info/', views.get_email_group_info, name='get_email_group_info'),
    path('email-recipients/', views.email_recipients_list, name='email_recipients_list'),
    path('email-recipients/add/', views.add_email_recipient, name='add_email_recipient'),
    path('email-recipients/<int:recipient_id>/edit/', views.edit_email_recipient, name='edit_email_recipient'),
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)