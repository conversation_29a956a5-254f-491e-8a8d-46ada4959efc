{% extends "base.html" %}
{% block content %}
<h2>EOPCN Team List</h2>
{% if messages %}
        <script>
            {% for message in messages %}
                alert("{{ message }}");
            {% endfor %}
        </script>
{% endif %}
<a href="{% url 'add_program' %}" class="btn btn-secondary">+ Add EOPCN Team</a>

<table id="programTable" class="display">
    <thead>
        <tr>
            <th>EOPCN Team Name</th>
            <th>Service</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for program in programs %}
        <tr>
            <td>{{ program.program_name }}</td>
            <td>{{ program.service.service_name }}</td>
            <td>
                <a href="{% url 'edit_program' program.program_id %}" class="btn btn-primary">Edit</a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        // Initialize DataTable with FixedHeader
        const table = $('#programTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "pageLength": 50,
        });

        // Add FixedHeader if available
        if ($.fn.dataTable.FixedHeader) {
            new $.fn.dataTable.FixedHeader(table);
        }
    });
</script>
{% endblock %}
