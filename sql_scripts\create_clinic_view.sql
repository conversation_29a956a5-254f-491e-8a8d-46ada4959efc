-- <PERSON><PERSON>t to create the vw_clinics_w_active_physician_count view
-- This view is required for the clinic list functionality in the Django app

-- Drop the view if it exists
IF OBJECT_ID('vw_clinics_w_active_physician_count', 'V') IS NOT NULL
    DROP VIEW vw_clinics_w_active_physician_count;
GO

-- Create the view
CREATE VIEW vw_clinics_w_active_physician_count AS
SELECT 
    c.clinic_id,
    c.clinic_name,
    c.street_address,
    c.floor_unit_room,
    c.city,
    c.business_phone,
    c.fax,
    c.include_on_eopcn_website,
    c.primary_contact,
    c.primary_contact_role,
    c.primary_contact_phone,
    c.primary_contact_email,
    c.clinic_website,
    -- Count active physicians (those currently active in the clinic)
    COUNT(DISTINCT CASE 
        WHEN (cp.date_active_in_clinic <= GETDATE() AND 
             (cp.date_left_clinic IS NULL OR cp.date_left_clinic > GETDATE()))
        THEN cp.physician_id 
        ELSE NULL 
    END) AS active_physician_count,
    -- Get the most recent clinic note date
    (SELECT MAX(cn.note_date) 
     FROM mh_clinic_notes cn 
     WHERE cn.clinic_id = c.clinic_id) AS last_note_date
FROM 
    mh_clinics c
LEFT JOIN 
    mh_clinics_physicians cp ON c.clinic_id = cp.clinic_id
GROUP BY 
    c.clinic_id,
    c.clinic_name,
    c.street_address,
    c.floor_unit_room,
    c.city,
    c.business_phone,
    c.fax,
    c.include_on_eopcn_website,
    c.primary_contact,
    c.primary_contact_role,
    c.primary_contact_phone,
    c.primary_contact_email,
    c.clinic_website;
GO

PRINT 'View vw_clinics_w_active_physician_count created successfully!';
