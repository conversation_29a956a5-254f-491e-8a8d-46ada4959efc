-- S<PERSON> Script to create enhanced reminder tables in SQL Server
-- Run this in your SQL Server database if you want the advanced reminder features

-- Create leave_reminder_types table
CREATE TABLE leave_reminder_types (
    type_id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100) NOT NULL,
    reminder_type NVARCHAR(20) NOT NULL CHECK (reminder_type IN ('before_leave', 'during_leave', 'before_return', 'overdue_return', 'custom')),
    days_offset INT NOT NULL, -- Days before/after leave start (negative for before, positive for after)
    is_active BIT NOT NULL DEFAULT 1,
    description NVARCHAR(MAX) NULL
);

-- Create leave_reminders table
CREATE TABLE leave_reminders (
    reminder_id INT IDENTITY(1,1) PRIMARY KEY,
    staff_leave_id INT NOT NULL, -- Foreign key to staff_leave.leave_id
    reminder_type_id INT NOT NULL, -- Foreign key to leave_reminder_types.type_id
    scheduled_datetime DATETIME2 NOT NULL,
    status NVARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'cancelled')),
    sent_datetime DATETIME2 NULL,
    error_message NVARCHAR(MAX) NULL,
    recipients NVARCHAR(MAX) NULL, -- JSON list of email addresses
    custom_email_group_id INT NULL, -- Foreign key to email_groups.group_id
    custom_email_address NVARCHAR(254) NULL,
    date_created DATETIME2 NOT NULL DEFAULT GETDATE(),
    created_by NVARCHAR(50) NULL,
    
    -- Foreign key constraints
    CONSTRAINT FK_leave_reminders_staff_leave FOREIGN KEY (staff_leave_id) REFERENCES staff_leave(leave_id),
    CONSTRAINT FK_leave_reminders_reminder_type FOREIGN KEY (reminder_type_id) REFERENCES leave_reminder_types(type_id),
    CONSTRAINT FK_leave_reminders_email_group FOREIGN KEY (custom_email_group_id) REFERENCES email_groups(group_id),
    
    -- Unique constraint to prevent duplicate reminders
    CONSTRAINT UQ_leave_reminders_staff_leave_type UNIQUE (staff_leave_id, reminder_type_id)
);

-- Insert default reminder types
INSERT INTO leave_reminder_types (name, reminder_type, days_offset, is_active, description) VALUES
('Leave Starting Soon', 'before_leave', -3, 1, 'Reminder sent 3 days before leave starts'),
('Return Date Verification', 'before_return', -1, 1, 'Reminder to verify return date 1 day before return'),
('Leave Status Check', 'during_leave', 7, 1, 'Check on leave status during extended leave'),
('Overdue Return Check', 'overdue_return', 1, 1, 'Check when staff member has not returned as scheduled');

-- Create indexes for better performance
CREATE INDEX IX_leave_reminders_status_scheduled ON leave_reminders(status, scheduled_datetime);
CREATE INDEX IX_leave_reminders_staff_leave ON leave_reminders(staff_leave_id);
CREATE INDEX IX_leave_reminder_types_active ON leave_reminder_types(is_active);

PRINT 'Enhanced reminder tables created successfully!';
PRINT 'You can now use the advanced reminder features.';
