from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.db import transaction
from django.forms import modelformset_factory, inlineformset_factory, formset_factory
from django.template.loader import render_to_string
from django.urls import reverse
from django.http import JsonResponse
from .forms import AddStaffForm, StaffAllocationForm, StaffAssignmentForm, StaffAllocationFormSet, StaffAssignmentFormSet, StaffAssignmentWithAllocationsFormSet, StaffLeaveForm, CommentForm, PositionForm, StaffSupervisorForm, StaffAssignmentsInClinicForm, ServiceForm, ProgramForm, StaffRoleForm, StaffLocationContactForm, StaffCoverageFormSet, StaffCoverageForm, PhysicianForm, PhysicianLanguageFormSet, ClinicPhysicianForm, ClinicForm, ClinicNoteForm, EmailRecipientForm, EmailGroupForm, EmailGroupMembershipForm, EmailGroupForm, EmailRecipientForm, EmailGroupMembershipForm
from .models import Staff, StaffAllocation, StaffAssignment, StaffWithRole, Physician, ClinicPhysician, Clinic, StaffLeave, StaffSupervisor, StaffAssignmentsInClinic, Service, Program, StaffRole, PhysicianPanelDetails, ClinicStaffAllocation, StaffLocationContact, ClinicWithActivePhysicianCount, StaffCoverage, PositionList, Position, ClinicNote, EmailRecipient, EmailGroup, EmailGroupMembership, EmailGroup, EmailRecipient, EmailGroupMembership, FourCutPanelMaster, PhysicianLanguage
from django.core.mail import send_mail
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.utils import timezone
import pytz
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
from datetime import datetime, timedelta
from django.db.models import Prefetch, Q
from django.db.models import Sum, Count
from datetime import date
from django.db import connection
from django.contrib.auth.decorators import login_required
from collections import defaultdict
from django.http import Http404
import pandas as pd
from .models import PhysicianNameMapping
from .forms import PhysicianNameMappingForm
from .utils import capture_original_data, track_changes, get_field_labels_for_model, track_formset_changes



# Helper functions for safe user handling
def get_safe_user_name(user):
    """Helper function to safely get user name for both authenticated and anonymous users"""
    if user.is_authenticated:
        return user.get_full_name() or user.username
    return "Anonymous (Testing)"

def get_safe_user_email(user):
    """Helper function to safely get user email for both authenticated and anonymous users"""
    if user.is_authenticated and hasattr(user, 'email'):
        return user.email
    return "<EMAIL>"


def get_blob_sas_url(blob_name):
    account_name = 'eopcnstaffphotos'
    account_key = '****************************************************************************************'
    container_name = 'staffphotos'
    
    blob_service_client = BlobServiceClient(account_url=f"https://{account_name}.blob.core.windows.net", credential=account_key)
    
    # Set the expiration time for the SAS token (e.g., 1 hour from now)
    sas_token = generate_blob_sas(
        account_name=account_name,
        container_name=container_name,
        blob_name=blob_name,
        account_key=account_key,
        permission=BlobSasPermissions(read=True),  # Allow read access
        expiry=datetime.now(timezone.utc) + timedelta(hours=1)  # Expiration time (1 hour)
    )
    
    # Generate the full URL with the SAS token
    sas_url = f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}?{sas_token}"
    return sas_url

def get_blob_sas_url_seating_map(blob_name):
    account_name = 'eopcnstaffphotos'
    account_key = '****************************************************************************************'
    container_name = 'seatingmap'
    
    blob_service_client = BlobServiceClient(account_url=f"https://{account_name}.blob.core.windows.net", credential=account_key)
    
    # Set the expiration time for the SAS token (e.g., 1 hour from now)
    sas_token = generate_blob_sas(
        account_name=account_name,
        container_name=container_name,
        blob_name=blob_name,
        account_key=account_key,
        permission=BlobSasPermissions(read=True),  # Allow read access
        expiry=datetime.now(timezone.utc) + timedelta(hours=1)  # Expiration time (1 hour)
    )
    
    # Generate the full URL with the SAS token
    sas_url_seating_map = f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}?{sas_token}"
    return sas_url_seating_map

def get_blob_sas_url_physician_update(blob_name):
    account_name = 'eopcnstaffphotos'
    account_key = '****************************************************************************************'
    container_name = 'physicianupdate'
    
    blob_service_client = BlobServiceClient(account_url=f"https://{account_name}.blob.core.windows.net", credential=account_key)
    
    # Set the expiration time for the SAS token (e.g., 1 hour from now)
    sas_token = generate_blob_sas(
        account_name=account_name,
        container_name=container_name,
        blob_name=blob_name,
        account_key=account_key,
        permission=BlobSasPermissions(read=True),  # Allow read access
        expiry=datetime.now(timezone.utc) + timedelta(hours=1)  # Expiration time (1 hour)
    )
    
    # Generate the full URL with the SAS token
    sas_url = f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}?{sas_token}"
    return sas_url


def seating_map(request):
    # Replace with the actual blob name for your PDF
    blob_name = "EOPCN Floor Plan NO NAMES NO DEPT LEGEND Mar 2025.pdf"
    map_url = get_blob_sas_url_seating_map(blob_name)
    return render(request, 'seating_map.html', {'map_url': map_url})



def list_staff(request):
    # Fetch data from the view
    staff_with_roles = StaffWithRole.objects.all()

    return render(request, 'staff/list_staff.html', {
        'staff_with_roles': staff_with_roles,
    })

def staff_detail(request, pk):
    # Get the staff member by primary key
    staff_member = get_object_or_404(Staff, pk=pk)

    # Generate the SAS URL for the staff photo
    if staff_member.photo:
        staff_photo_url = get_blob_sas_url(staff_member.photo.name)
    else:
        staff_photo_url = None  # Handle cases where no photo is uploaded
    
    # Get all assignments related to this staff member, prefetching allocations
    assignments = staff_member.assignments.prefetch_related(
    Prefetch('allocations', queryset=StaffAllocation.objects.order_by('-currently_active', '-start_date'))
    ).order_by('-currently_active', '-start_date')
    
    staff_members = Staff.objects.all().order_by('first_name')

    # Find the index of the current staff member in the sorted list
    staff_list = list(staff_members)
    current_index = staff_list.index(staff_member)
    
    # Determine previous and next staff members
    previous_staff = staff_list[current_index - 1] if current_index > 0 else None
    next_staff = staff_list[current_index + 1] if current_index < len(staff_list) - 1 else None

    location_contacts = StaffLocationContact.objects.filter(staff=staff_member)
    
    # Check if staff member is a supervisor
    staff_is_supervisor = StaffSupervisor.objects.filter(staff=staff_member).exists()
    supervisor_id = None
    if staff_is_supervisor:
        supervisor = StaffSupervisor.objects.get(staff=staff_member)
        supervisor_id = supervisor.supervisor_id
    
    context = {
        'staff_member': staff_member,
        'assignments': assignments,
        'staff_photo_url': staff_photo_url,
        'staff_members': staff_members,
        'previous_staff': previous_staff,
        'next_staff': next_staff,
        'location_contacts': location_contacts,
        'staff_is_supervisor': staff_is_supervisor,
        'supervisor_id': supervisor_id,
    }
    
    return render(request, 'staff/staff_detail.html', context)

from .models import DashboardStats


def home(request):
    stats = get_dashboard_stats()  # Fetch stats from SQL Server view
    context = stats
    context['today'] = date.today()  # Add today's date to the context
    return render(request, "home.html", stats)


def get_dashboard_stats():
    """Fetch the latest row from vwDashboardStats"""
    stats = DashboardStats.objects.first()  # Expecting only one row since it's an aggregate view.

    return {
        'active_staff_count': stats.active_staff_count if stats else 0,
        'total_fte_active_staff': stats.total_fte_active_staff if stats else 0,
        'on_leave_count': stats.on_leave_count if stats else 0,
        'centralized_staff_count': stats.centralized_staff_count if stats else 0,
        'ric_staff_count': stats.ric_staff_count if stats else 0,
        'vacancy_count': stats.vacancy_count if stats else 0,
        'active_physicians_count': stats.active_physicians_count if stats else 0,
        'active_clinics_count': stats.active_clinics_count if stats else 0,
        'latest_panel_month': stats.latest_panel_month if stats else 0,
        'latest_panel_year': stats.latest_panel_year if stats else 0,
        'total_panel_size': stats.total_panel_size if stats else 0,
    }


email_group_1 = [
    '<EMAIL>', 
    '<EMAIL>',
]

#     '<EMAIL>', 
    # '<EMAIL>',
    # '<EMAIL>',

email_group_2 = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
]


def send_email(subject, context, to_email_list, template_name, custom_email_group=None):
    from_email = '<EMAIL>'
    
    # Only set default email groups if to_email_list is not provided
    if to_email_list is None:
        to_email_list = get_email_list_for_template(template_name, custom_email_group)

    # Special case for new physician email
    if template_name == 'physicians/emails/physician_add_notification.html':
        # Make a copy of the to_email_list to avoid modifying the original list
        if isinstance(to_email_list, list):
            to_email_list = to_email_list.copy()
        else:
            to_email_list = get_email_list_for_template(template_name, custom_email_group).copy()
        
        # Add <EMAIL> to the recipient list if not already included
        if '<EMAIL>' not in to_email_list:
            to_email_list.append('<EMAIL>')

    # Ensure to_email_list is actually a list, even if defaulted above
    if not isinstance(to_email_list, list):
         to_email_list = get_email_list_for_template(template_name, custom_email_group)

    # Add <EMAIL> for specific scenarios
    should_add_dlam = False
    
    # For new physician or new clinic emails, always add dlam
    if template_name in ['physicians/emails/physician_add_notification.html', 'clinics/emails/clinic_add_notification.html']:
        should_add_dlam = True

    # For physician updates, only add dlam if there's a date_left_eopcn being set
    elif template_name == 'physicians/emails/physician_update_notification.html':
        if context and context.get('physician_data', {}).get('date_left_eopcn'):
            should_add_dlam = True

    # For clinic-physician updates, add dlam if there's a date_left_clinic being set
    elif template_name == 'physicians/emails/physician_clinic_update_notification.html':
        if context and any(clinic.get('date_left_clinic') for clinic in context.get('clinic_data', [])):
            should_add_dlam = True
    
    # Add <EMAIL> if needed and not already present
    if should_add_dlam and '<EMAIL>' not in to_email_list:
        to_email_list.append('<EMAIL>')
        
    # Render the email template to HTML
    html_content = render_to_string(template_name, context)
    text_content = strip_tags(html_content)  # Strip the HTML tags for plain text

    # Create the email message
    email = EmailMultiAlternatives(subject, text_content, from_email, to_email_list)
    email.attach_alternative(html_content, "text/html")  # Attach HTML version

    # Send the email
    # This line relies entirely on the settings in settings.py (EMAIL_BACKEND, HOST, PORT, USER, PASSWORD, USE_TLS)
    # The 5.7.139 error occurs here, indicating the email server rejected the authentication based on those settings.
    email.send()


def add_staff(request):
    StaffAllocationFormSet = inlineformset_factory(
        StaffAssignment,
        StaffAllocation,
        form=StaffAllocationForm,
        extra=5,
    )
    
    if request.method == 'POST':
        staff_form = AddStaffForm(request.POST, request.FILES)
        assignment_form = StaffAssignmentForm(request.POST)
        location_contact_form = StaffLocationContactForm(request.POST)
        # Initialize the formset with POST data regardless of form validity
        allocation_formset = StaffAllocationFormSet(request.POST, prefix='allocations')
    
        if staff_form.is_valid() and assignment_form.is_valid() and location_contact_form.is_valid():
            try:
                with transaction.atomic():
                    # Save Staff instance
                    staff = staff_form.save(commit=False)
                    staff.created_by = get_safe_user_name(request.user)
                    staff.currently_active = True
                    staff.save()

                    # Save StaffLocationContact instance if has data
                    if (location_contact_form.cleaned_data.get('contact_type') or 
                        location_contact_form.cleaned_data.get('clinic') or 
                        location_contact_form.cleaned_data.get('phone') or 
                        location_contact_form.cleaned_data.get('extension')):
                        location_contact = location_contact_form.save(commit=False)
                        location_contact.staff = staff
                        location_contact.save()

                    # Save StaffAssignment instance
                    staff_assignment = assignment_form.save(commit=False)
                    staff_assignment.staff = staff
                    staff_assignment.created_by = get_safe_user_name(request.user)
                    staff_assignment.currently_active = True
                    staff_assignment.save()

                    # Re-initialize allocation_formset with the instance
                    allocation_formset = StaffAllocationFormSet(request.POST, instance=staff_assignment, prefix='allocations')
                    if allocation_formset.is_valid():
                        for form in allocation_formset:
                            if form.cleaned_data.get('clinic') or form.cleaned_data.get('program') or form.cleaned_data.get('fte') or form.cleaned_data.get('start_date'):
                                allocation = form.save(commit=False)
                                allocation.staff_assignment = staff_assignment
                                allocation.created_by = get_safe_user_name(request.user) if get_safe_user_name(request.user) != '' else None
                                allocation.currently_active = True

                                if allocation.assignment_in_clinic == '':
                                    allocation.assignment_in_clinic = None

                                allocation.save()
                      # Prepare the cleaned data to send in the email
                    staff_data = staff_form.cleaned_data
                    assignment_data = assignment_form.cleaned_data
                    location_contact_data = location_contact_form.cleaned_data
                    allocation_data = [form.cleaned_data for form in allocation_formset]
                    
                    email_leadership = staff_form.cleaned_data.get('email_leadership')
                    email_group = staff_form.cleaned_data.get('email_group')

                    # Prepare the user info dictionary
                    user_info = {
                        'full_name': get_safe_user_name(request.user),
                        'email': get_safe_user_email(request.user)
                    }

                    if email_leadership:
                        details_url = request.build_absolute_uri(reverse('staff_detail', args=[staff.pk]))
                        try:
                            # Calling send_email
                            send_email(
                                f'New Staff Member Added: {staff.first_name} {staff.last_name}',
                                {
                                    'staff': staff_data,
                                    'location_contact': location_contact_data,
                                    'assignment': assignment_data,
                                    'allocations': allocation_data,
                                    'user': user_info,
                                    'details_url': details_url
                                },
                                None,
                                'staff/emails/staff_add_notification.html',
                                email_group
                            )
                        except Exception as email_error: # This generic exception block will catch the 5.7.139 error
                            # Log the error but don't prevent staff creation
                            print(f"Error sending email: {email_error}")
                            messages.warning(request, f"Staff member was added but email notification could not be sent. Error: {str(email_error)}")

                    messages.success(request, "Staff member added successfully.")
                    return redirect('staff_detail', pk=staff.pk)

            except Exception as e:
                print(f"Error saving staff: {e}")
                messages.error(request, "An error occurred while saving the staff member. Please try again.")
        else:
            # Forms are not valid, but allocation_formset is already initialized
            pass
    else:
        staff_form = AddStaffForm(initial={'currently_active': True})
        assignment_form = StaffAssignmentForm()
        location_contact_form = StaffLocationContactForm()
        allocation_formset = StaffAllocationFormSet(prefix='allocations')

    context = {
        'staff_form': staff_form,
        'assignment_form': assignment_form,
        'location_contact_form': location_contact_form,
        'allocation_formset': allocation_formset,
    }
    return render(request, 'staff/add_staff_form.html', context)



def add_staff_assignment(request, pk):
    staff = get_object_or_404(Staff, pk=pk)

    if request.method == 'POST':
        form = StaffAssignmentForm(request.POST)
        if form.is_valid():
            new_assignment = form.save(commit=False)
            new_assignment.staff = staff  # Assign the staff member
            new_assignment.created_by = get_safe_user_name(request.user)
            new_assignment.save()
            assignment_data = form.cleaned_data

            email_leadership = form.cleaned_data.get('email_leadership')
            email_group = form.cleaned_data.get('email_group')
            comment = form.cleaned_data.get('comment')  # Get the comment

            # Prepare the user info dictionary
            user_info = {
                'full_name': get_safe_user_name(request.user),
                'email': get_safe_user_email(request.user)
            }

            if email_leadership:
                # Prepare email content and grab cleaned data from the form
                subject = f'New Staff Assignment Added to Staff: { staff.first_name } { staff.last_name }'
                details_url = request.build_absolute_uri(reverse('staff_detail', args=[staff.pk]))
                context = {
                    'staff_member': staff,
                    'assignment': assignment_data,
                    'comment': comment,  # Add comment to context
                    'user': user_info,
                    'details_url': details_url
                }

                # Send the email using the HTML template
                send_email(subject, context, None, 'staff/emails/staff_assignment_add_notification.html', email_group)

            messages.success(request, 'Staff assignment added successfully.')
            return redirect('staff_detail', pk=staff.pk)
    else:
        form = StaffAssignmentForm()

    context = {
        'form': form,
        'staff': staff
    }
    return render(request, 'staff/add_staff_assignment.html', context)


def edit_profile(request, pk):
    staff = get_object_or_404(Staff, pk=pk)

    if request.method == 'POST':
        # Capture original data before form processing
        original_data = capture_original_data(staff, ['staff_id'])

        staff_form = AddStaffForm(request.POST, request.FILES, instance=staff)

        if staff_form.is_valid():
            try:
                with transaction.atomic():
                    # Track changes using utility function
                    field_labels = get_field_labels_for_model('staff')
                    changes = track_changes(original_data, staff_form.cleaned_data, field_labels)
                    
                    staff = staff_form.save(commit=False)
                    staff.modified_by = get_safe_user_name(request.user)
                    staff.save()
                    
                    email_leadership = staff_form.cleaned_data.get('email_leadership')
                    email_group = staff_form.cleaned_data.get('email_group')
                    comment = staff_form.cleaned_data.get('comment')  # Get the comment

                    # Prepare the user info dictionary
                    user_info = {
                        'full_name': get_safe_user_name(request.user),
                        'email': get_safe_user_email(request.user)
                    }

                    if email_leadership:
                        # Prepare email content with changes
                        subject = f'Profile Update: {staff.first_name} {staff.last_name}'
                        details_url = request.build_absolute_uri(reverse('staff_detail', args=[staff.pk]))
                        context = {
                            'staff_member': staff,
                            'changes': changes,
                            'comment': comment,  # Add comment to context
                            'user': user_info,
                            'details_url': details_url
                        }

                        # Send the email using the HTML template
                        send_email(subject, context, None, 'staff/emails/staff_profile_update_notification.html', email_group)

                    messages.success(request, "Staff profile updated successfully.")
                    return redirect('staff_detail', pk=staff.pk)

            except Exception as e:
                print(f"Error: {e}")
                messages.error(request, "Something went wrong. Please try again.")
        else:
            messages.error(request, "Please correct the errors below.")

    else:
        staff_form = AddStaffForm(instance=staff, initial={'email_leadership': False})

    context = {
        'staff_form': staff_form,
        'staff': staff,
    }
    
    return render(request, 'staff/edit_profile.html', context)


def edit_assignment(request, pk):
    # pk here should be the primary key of the assignment, not the staff
    assignment = get_object_or_404(StaffAssignment, pk=pk)
    staff = assignment.staff  # Access the related staff member from the assignment

    if request.method == 'POST':
        # Check if the delete button was clicked
        if 'delete_assignment' in request.POST:
            assignment.delete()
            messages.success(request, "Staff assignment deleted successfully.")
            return redirect('staff_detail', pk=staff.pk)
        
        # Capture original data before form processing
        original_data = capture_original_data(assignment, ['assignment_id'])
        
        form = StaffAssignmentForm(request.POST, instance=assignment)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Track changes using utility function
                    field_labels = get_field_labels_for_model('assignment')
                    changes = track_changes(original_data, form.cleaned_data, field_labels)

                    updated_assignment = form.save(commit=False)
                    updated_assignment.modified_by = get_safe_user_name(request.user)
                    updated_assignment.save()

                    messages.success(request, "Staff assignment updated successfully.")

                    email_leadership = form.cleaned_data.get('email_leadership')
                    email_group = form.cleaned_data.get('email_group')
                    comment = form.cleaned_data.get('comment')  # Get the comment

                    # Prepare the user info dictionary
                    user_info = {
                        'full_name': get_safe_user_name(request.user),
                        'email': get_safe_user_email(request.user)
                    }

                    if email_leadership:
                        # Send the email with changes
                        subject = f"Assignment Update: {staff.first_name} {staff.last_name}"
                        details_url = request.build_absolute_uri(reverse('staff_detail', args=[staff.pk]))
                        context = {
                            'staff_member': staff,
                            'assignment': updated_assignment,
                            'changes': changes,
                            'comment': comment,  # Add comment to context
                            'user': user_info,
                            'details_url': details_url
                        }
                        send_email(subject, context, None, 'staff/emails/staff_assignment_update_notification.html', email_group)

                    return redirect('staff_detail', pk=staff.pk)

            except Exception as e:
                print(f"Error: {e}")
                messages.error(request, "Something went wrong. Please try again.")
        else:
            messages.error(request, "Please correct the errors below.")
    else:
        form = StaffAssignmentForm(instance=assignment, initial={'email_leadership': False})

    context = {
        'staff': staff,
        'form': form,
    }
    
    return render(request, 'staff/edit_assignment.html', context)


# create function to edit a staff member's allocation details attached to an assignment
def edit_allocation(request, pk):
    assignment = get_object_or_404(StaffAssignment, pk=pk)

    # Create formset with can_delete=True to allow deletions
    StaffAllocationFormSet = inlineformset_factory(
        StaffAssignment,
        StaffAllocation,
        form=StaffAllocationForm,
        extra=5,
        can_delete=True  # Allows the deletion of allocations
    )

    if request.method == 'POST':
        # Capture original data for all existing allocations before form processing
        existing_allocations = assignment.allocations.all()
        original_data_list = []
        for allocation in existing_allocations:
            original_data_list.append(capture_original_data(allocation, ['allocation_id']))

        allocation_formset = StaffAllocationFormSet(request.POST, instance=assignment)
        comment_form = CommentForm(request.POST)

        if allocation_formset.is_valid() and comment_form.is_valid():
            try:
                with transaction.atomic():
                    # Track changes using utility function for formsets
                    field_labels = get_field_labels_for_model('allocation')
                    allocation_changes = track_formset_changes(original_data_list, allocation_formset, field_labels)

                    # Save the allocations but don't commit to allow modification before saving
                    allocations = allocation_formset.save(commit=False)

                    # Handle new or modified allocations
                    for allocation in allocations:
                        if allocation.clinic or allocation.program or allocation.fte or allocation.start_date:
                            allocation.modified_by = get_safe_user_name(request.user)
                            allocation.staff_assignment = assignment
                            allocation.save()

                    # Handle deletions (Django tracks deletions via the formset)
                    for allocation in allocation_formset.deleted_objects:
                        allocation.delete()

                    allocation_data = allocation_formset.cleaned_data
                    # Extract the comment and email_leadership from the comment form
                    comment = comment_form.cleaned_data.get('comment')
                    email_leadership = comment_form.cleaned_data.get('email_leadership')
                    email_group = comment_form.cleaned_data.get('email_group')

                    # Prepare the user info dictionary
                    user_info = {
                        'full_name': get_safe_user_name(request.user),
                        'email': get_safe_user_email(request.user)
                    }

                    if email_leadership:
                        # Send the email with changes
                        subject = f"Allocations Update: {assignment.staff.first_name} {assignment.staff.last_name}"
                        details_url = request.build_absolute_uri(reverse('staff_detail', args=[assignment.staff.pk]))
                        context = {
                            'assignment': assignment,
                            'allocations': allocation_data,
                            'allocation_changes': allocation_changes,
                            'comment': comment,
                            'user': user_info,
                            'details_url': details_url
                        }
                        send_email(subject, context, None, 'staff/emails/staff_allocation_update_notification.html', email_group)

                    # Save the formset changes
                    allocation_formset.save_m2m()

                    messages.success(request, "Allocations updated successfully.")
                    return redirect('staff_detail', pk=assignment.staff.pk)
            except Exception as e:
                print(f"Error: {e}")
                messages.error(request, "An error occurred while updating the allocation. Please try again.")
    else:
        allocation_formset = StaffAllocationFormSet(instance=assignment)
        comment_form = CommentForm(initial={'email_leadership': False})

    context = {
        'assignment': assignment,
        'allocation_formset': allocation_formset,
        'comment_form': comment_form
    }

    return render(request, 'staff/edit_allocation.html', context)


def add_staff_leave(request, pk):
    staff = get_object_or_404(Staff, pk=pk)
    
    StaffCoverageFormSet = inlineformset_factory(
        StaffLeave,
        StaffCoverage,
        form=StaffCoverageForm,
        extra=1,
        can_delete=True
    )

    if request.method == 'POST':
        form = StaffLeaveForm(request.POST)
        coverage_formset = StaffCoverageFormSet(request.POST)

        if form.is_valid() and coverage_formset.is_valid():
            staff_leave = form.save(commit=False)
            staff_leave.staff = staff
            staff_leave.created_by = get_safe_user_name(request.user)
            staff_leave.date_created = timezone.now()
            staff_leave.modified_by = get_safe_user_name(request.user)
            staff_leave.date_modified = timezone.now()
            # Handle reminder datetime in MST and store as UTC
            reminder_dt = form.cleaned_data.get('reminder_datetime')
            if reminder_dt:
                mst = pytz.timezone('US/Mountain')
                # The datetime-local input gives us a naive datetime that represents MST time
                if timezone.is_naive(reminder_dt):
                    # User entered time is already in MST, so localize it as MST
                    reminder_dt_mst = mst.localize(reminder_dt)
                else:
                    # If it's already timezone-aware, convert to MST
                    reminder_dt_mst = reminder_dt.astimezone(mst)
                # Convert MST to UTC for storage
                staff_leave.reminder_datetime = reminder_dt_mst.astimezone(pytz.UTC)
            staff_leave.reminder_email_group = form.cleaned_data.get('reminder_email_group')
            if form.cleaned_data.get('send_reminder_to_self'):
                staff_leave.reminder_email_address = get_safe_user_email(request.user)
            else:
                staff_leave.reminder_email_address = None
            staff_leave.save()

            # Save the coverage data to the database
            coverage_formset.instance = staff_leave
            coverage_formset.save()

            # Extract valid coverage data for the email from the formset's cleaned_data
            coverage_data = [
                data_dict for data_dict in coverage_formset.cleaned_data
                if data_dict and (data_dict.get('covering_staff') or data_dict.get('coverage_start_date'))
            ]
            
            email_leadership = form.cleaned_data.get('email_leadership')
            email_group = form.cleaned_data.get('email_group')
            comment = form.cleaned_data.get('comment')  # Get the comment

            # Prepare the user info dictionary
            user_info = {
                'full_name': get_safe_user_name(request.user),
                'email': get_safe_user_email(request.user)
            }

            if email_leadership:
                # Prepare email content
                subject = f"New Leave Record: {staff.first_name} {staff.last_name}"
                details_url = request.build_absolute_uri(reverse('staff_detail', args=[staff.pk]))
                context = {
                    'staff_member': staff,
                    'staff_leave': form.cleaned_data,
                    'staff_coverage': coverage_data,
                    'comment': comment,  # Add comment to context
                    'user': user_info,
                    'details_url': details_url
                }

                # Send the email
                send_email(subject, context, None, 'staff/emails/staff_leave_add_notification.html', email_group)

            messages.success(request, 'Staff leave record added successfully.')
            return redirect('staff_detail', pk=staff.pk)
    else:
        form = StaffLeaveForm()
        coverage_formset = StaffCoverageFormSet()

    return render(request, 'staff/add_staff_leave.html', {
        'form': form,
        'coverage_formset': coverage_formset,
        'staff': staff
    })


def edit_staff_leave(request, pk):
    staff_leave = get_object_or_404(StaffLeave, pk=pk)
    staff = staff_leave.staff  # Get the associated staff member

    StaffCoverageFormSet = inlineformset_factory(
        StaffLeave,
        StaffCoverage,
        form=StaffCoverageForm,
        extra=1,
        can_delete=True
    )

    if request.method == 'POST':
        # Handle the delete request
        if 'delete' in request.POST:
            staff_leave.delete()  # Delete the leave entry
            messages.success(request, 'Staff leave record deleted successfully.')
            return redirect('staff_detail', pk=staff.pk)  # Redirect after deletion

        # Capture original data before form processing
        original_data = capture_original_data(staff_leave, ['leave_id'])

        # Handle the update request
        form = StaffLeaveForm(request.POST, instance=staff_leave)
        coverage_formset = StaffCoverageFormSet(request.POST, instance=staff_leave)

        if form.is_valid() and coverage_formset.is_valid():
            # Track changes using utility function
            field_labels = get_field_labels_for_model('leave')
            changes = track_changes(original_data, form.cleaned_data, field_labels)

            updated_staff_leave = form.save(commit=False)
            updated_staff_leave.modified_by = get_safe_user_name(request.user)
            updated_staff_leave.date_modified = timezone.now()
            reminder_dt = form.cleaned_data.get('reminder_datetime')
            if reminder_dt:
                mst = pytz.timezone('US/Mountain')
                # The datetime-local input gives us a naive datetime that represents MST time
                if timezone.is_naive(reminder_dt):
                    # User entered time is already in MST, so localize it as MST
                    reminder_dt_mst = mst.localize(reminder_dt)
                else:
                    # If it's already timezone-aware, convert to MST
                    reminder_dt_mst = reminder_dt.astimezone(mst)
                # Convert MST to UTC for storage
                updated_staff_leave.reminder_datetime = reminder_dt_mst.astimezone(pytz.UTC)
                updated_staff_leave.reminder_sent = False
            else:
                # Clear any existing reminder
                updated_staff_leave.reminder_datetime = None
                updated_staff_leave.reminder_sent = False
            updated_staff_leave.reminder_email_group = form.cleaned_data.get('reminder_email_group')
            if form.cleaned_data.get('send_reminder_to_self'):
                updated_staff_leave.reminder_email_address = get_safe_user_email(request.user)
            else:
                updated_staff_leave.reminder_email_address = None
            updated_staff_leave.save()

            # Save only non-empty coverage forms
            coverage_instances = coverage_formset.save(commit=False)
            
            # Delete any instances marked for deletion
            for instance in coverage_formset.deleted_objects:
                instance.delete()
            
            # Save only instances that have meaningful data
            for instance in coverage_instances:
                if (instance.covering_staff or 
                    instance.coverage_start_date or 
                    instance.coverage_end_date or 
                    instance.coverage_type):
                    instance.save()

            # Extract valid coverage data for the email from saved instances
            coverage_data = []
            for form in coverage_formset:
                if (form.cleaned_data and 
                    not form.cleaned_data.get('DELETE', False) and
                    (form.cleaned_data.get('covering_staff') or 
                     form.cleaned_data.get('coverage_start_date') or 
                     form.cleaned_data.get('coverage_end_date') or 
                     form.cleaned_data.get('coverage_type'))):
                    coverage_data.append(form.cleaned_data)
            
            email_leadership = form.cleaned_data.get('email_leadership')
            email_group = form.cleaned_data.get('email_group')
            comment = form.cleaned_data.get('comment')  # Get the comment

            # Prepare the user info dictionary
            user_info = {
                'full_name': get_safe_user_name(request.user),
                'email': get_safe_user_email(request.user)
            }

            if email_leadership:
                # Prepare email content with changes
                subject = f"Leave Record Updated: {staff.first_name} {staff.last_name}"
                details_url = request.build_absolute_uri(reverse('staff_detail', args=[staff.pk]))
                context = {
                    'staff_member': staff,
                    'staff_leave': form.cleaned_data,
                    'staff_coverage': coverage_data,
                    'changes': changes,
                    'comment': comment,  # Add comment to context
                    'user': user_info,
                    'details_url': details_url
                }

                # Send the email
                send_email(subject, context, None, 'staff/emails/staff_leave_update_notification.html', email_group)

            messages.success(request, 'Staff leave record updated successfully.')
            return redirect('staff_detail', pk=staff.pk)
    else:
        form = StaffLeaveForm(instance=staff_leave)
        coverage_formset = StaffCoverageFormSet(instance=staff_leave)

    return render(
        request,
        'staff/edit_staff_leave.html',
        {
            'form': form,
            'coverage_formset': coverage_formset,
            'staff': staff,
            'staff_leave': staff_leave,
        },
    )


def add_position(request):
    if request.method == 'POST':
        form = PositionForm(request.POST)
        if form.is_valid():
            # Create a new instance but don’t save to the database yet
            position = form.save(commit=False)
            position.is_available = True  # Set is_available to True
            position.decommissioned = False  # Set decommissioned to False
            position.created_by = get_safe_user_name(request.user)  # Set created_by to the current user's username
            position.save()  # Save the instance to the database
            messages.success(request, 'Position added successfully!')
            return redirect('position_list')  # Redirect to a page, adjust as needed
    else:
        form = PositionForm()

    return render(request, 'staff/add_position.html', {'form': form})

def edit_position(request, position_id):
    position = get_object_or_404(Position, position_id=position_id)
    if request.method == 'POST':
        form = PositionForm(request.POST, instance=position)
        if form.is_valid():
            form.save()
            messages.success(request, f'Position {position.position_number} has been updated.')
            return redirect('position_list')
    else:
        form = PositionForm(instance=position)
    
    return render(request, 'staff/edit_position.html', {'form': form, 'position': position})


def position_list(request):
    active_vacancy_type = request.GET.get('active_vacancy_type')
    allocation_type = request.GET.get('allocation_type')

    # 1. Fetch all positions from your simplified SQL view
    positions_qs = PositionList.objects.all()

    # 2. Apply any filters (temporary vacancy, etc.)
    if active_vacancy_type == 'available_positions':
        positions_qs = positions_qs.filter(is_available=True)
    # ... your other filters ...

    positions_list = list(positions_qs)

    # 3. Group by position_number
    positions_by_number = defaultdict(list)
    for pos in positions_list:
        positions_by_number[pos.position_number].append(pos)

    # 4. For each position_number, sort by your custom "recency" logic:
    #    - staff_currently_active DESC (still employed outranks ex-staff)
    #    - assignment_currently_active DESC (actively occupying outranks ended/leave)
    #    - assignment_end_date DESC (among ended, the one that ended last is newer)
    #    - assignment_start_date DESC (final tiebreaker)
    #
    #    Adjust if you prefer a different priority order.
    for pnum, p_list in positions_by_number.items():
        def sort_key(p):
            staff_flag = 1 if p.staff_currently_active else 0
            assign_flag = 1 if p.assignment_currently_active else 0
            end_date = p.assignment_end_date or date(1900,1,1)
            start_date = p.assignment_start_date or date(1900,1,1)
            return (staff_flag, assign_flag, end_date, start_date)

        # Sort descending by the tuple
        p_list.sort(key=sort_key, reverse=True)

        # 5. The first item in the sorted list is "most recent"
        p_list[0].is_most_recent = True
        for old_record in p_list[1:]:
            old_record.is_most_recent = False

    # 6. Flatten the grouped data back into a single list
    annotated_positions = []
    for pnum, p_list in positions_by_number.items():
        annotated_positions.extend(p_list)

    # 7. (Optional) Sort by position_number so your table is neat
    annotated_positions.sort(key=lambda x: (x.position_number, x.assignment_start_date or date(1900,1,1)))

    # 8. Pass to the template
    staff_members = Staff.objects.all().order_by('first_name', 'last_name')
    toggle_active = active_vacancy_type is None

    return render(request, 'staff/position_list.html', {
        'positions': annotated_positions,
        'staff_members': staff_members,
        'toggle_active': toggle_active,
    })

def delete_position(request, position_id):
    position = get_object_or_404(Position, position_id=position_id)
    if request.method == 'POST':
        position.delete()
        messages.success(request, f'Position {position.position_number} has been deleted.')
        return redirect('position_list')
    return render(request, 'staff/confirm_delete_position.html', {'position': position})

def add_supervisor(request):
    if request.method == 'POST':
        form = StaffSupervisorForm(request.POST)
        if form.is_valid():
            form.save()
            return redirect('supervisor_list')  # Redirect to a list of supervisors or another page
    else:
        form = StaffSupervisorForm()
    
    return render(request, 'staff/add_supervisor.html', {'form': form})


def supervisor_list(request):
    active_assignments = StaffAssignment.objects.filter(currently_active__exact=True) \
                        .order_by('-start_date') \
                        .prefetch_related('role', 'service')
    
    supervisors = StaffSupervisor.objects.all() \
        .select_related('staff') \
        .order_by('staff__first_name', 'staff__last_name') \
        .prefetch_related(
            Prefetch('staff__assignments', queryset=active_assignments, to_attr='active_assignments')
        )
    return render(request, 'staff/supervisor_list.html', {'supervisors': supervisors})


def edit_supervisor(request, pk):
    supervisor = get_object_or_404(StaffSupervisor, pk=pk)
    if request.method == 'POST':
        if 'delete' in request.POST:  # Check if the delete button was clicked
            supervisor.delete()
            messages.success(request, "Supervisor deleted successfully.")
            return redirect('supervisor_list')
        else:
            form = StaffSupervisorForm(request.POST, instance=supervisor)
            if form.is_valid():
                form.save()
                messages.success(request, "Supervisor updated successfully.")
                return redirect('supervisor_list')
    else:
        form = StaffSupervisorForm(instance=supervisor)
    
    return render(request, 'staff/edit_supervisor.html', {'form': form, 'supervisor': supervisor})


def assignments_in_clinic_list(request):
    assignments = StaffAssignmentsInClinic.objects.all().order_by('assignment_name')
    return render(request, 'staff/assignments_in_clinic_list.html', {'assignments': assignments})

# Add New Assignment in Clinic View
def add_assignments_in_clinic(request):
    if request.method == 'POST':
        form = StaffAssignmentsInClinicForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, "Assignment in Clinic added successfully.")
            return redirect('assignments_in_clinic_list')
    else:
        form = StaffAssignmentsInClinicForm()
    return render(request, 'staff/add_assignments_in_clinic.html', {'form': form})

# Edit Assignment in Clinic View
def edit_assignments_in_clinic(request, pk):
    assignment = get_object_or_404(StaffAssignmentsInClinic, pk=pk)
    
    if request.method == 'POST':
        if 'delete' in request.POST:  # Check if the delete button was clicked
            assignment.delete()
            messages.success(request, "Assignment in Clinic deleted successfully.")
            return redirect('assignments_in_clinic_list')  # Redirect to the list view after deletion
        else:
            form = StaffAssignmentsInClinicForm(request.POST, instance=assignment)
            if form.is_valid():
                form.save()
                messages.success(request, "Assignment in Clinic updated successfully.")
                return redirect('assignments_in_clinic_list')  # Redirect to the list view after editing
    else:
        form = StaffAssignmentsInClinicForm(instance=assignment)
    
    return render(request, 'staff/edit_assignments_in_clinic.html', {'form': form, 'assignment': assignment})


# List all services
def service_list(request):
    services = Service.objects.all().order_by('service_name')
    return render(request, 'staff/service_list.html', {'services': services})

# Add new service
def add_service(request):
    if request.method == 'POST':
        form = ServiceForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, "Service added successfully.")
            return redirect('service_list')
    else:
        form = ServiceForm()
    return render(request, 'staff/add_service.html', {'form': form})

# Edit service
def edit_service(request, pk):
    service = get_object_or_404(Service, pk=pk)
    if request.method == 'POST':
        if 'delete' in request.POST:
            service.delete()
            messages.success(request, "Service deleted successfully.")
            return redirect('service_list')
        else:
            form = ServiceForm(request.POST, instance=service)
            if form.is_valid():
                form.save()
                messages.success(request, "Service updated successfully.")
                return redirect('service_list')
    else:
        form = ServiceForm(instance=service)
    return render(request, 'staff/edit_service.html', {'form': form, 'service': service})


# List all programs
def program_list(request):
    programs = Program.objects.all().order_by('program_name')
    return render(request, 'staff/program_list.html', {'programs': programs})

# Add new program
def add_program(request):
    if request.method == 'POST':
        form = ProgramForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, "Program added successfully.")
            return redirect('program_list')
    else:
        form = ProgramForm()
    return render(request, 'staff/add_program.html', {'form': form})

# Edit program
def edit_program(request, pk):
    program = get_object_or_404(Program, pk=pk)
    
    if request.method == 'POST':
        if 'delete' in request.POST:
            program.delete()
            messages.success(request, "Program deleted successfully.")
            return redirect('program_list')
        else:
            form = ProgramForm(request.POST, instance=program)
            if form.is_valid():
                form.save()
                messages.success(request, "Program updated successfully.")
                return redirect('program_list')
    else:
        form = ProgramForm(instance=program)
    
    return render(request, 'staff/edit_program.html', {'form': form, 'program': program})


def staff_leaves_view(request):
    today = date.today()
    active_leave = request.GET.get('active_leave')
    
    staff_coverage_prefetch = Prefetch(
        'staffcoverage_set',  # Reverse relation from StaffLeave to StaffCoverage
        queryset=StaffCoverage.objects.select_related('covering_staff'),
        to_attr='coverages'
    )

    if active_leave == 'true':
        staff_leaves = StaffLeave.objects.select_related('staff', 'leave_type') \
            .prefetch_related(staff_coverage_prefetch) \
            .filter(leave_start_date__lte=today) \
            .filter(Q(return_date__gt=today) | Q(return_date__isnull=True)) \
            .order_by('-date_created')
        checkbox_checked = False  # Uncheck the filter checkbox when active filter is applied
    else:
        staff_leaves = StaffLeave.objects.select_related('staff', 'leave_type') \
            .prefetch_related(staff_coverage_prefetch) \
            .all().order_by('-date_created')
        checkbox_checked = True

    return render(request, 'staff/staff_leaves_list.html', {
        'staff_leaves': staff_leaves,
        'checkbox_checked': checkbox_checked,
    })


def staff_roles_list(request):
    staff_roles = StaffRole.objects.all().order_by('role_name')
    return render(request, 'staff/staff_roles_list.html', {'staff_roles': staff_roles})


def add_staff_role(request):
    if request.method == 'POST':
        form = StaffRoleForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, "Staff role added successfully.")
            return redirect('staff_roles_list')  # Redirect to the list view after saving
    else:
        form = StaffRoleForm()

    return render(request, 'staff/add_staff_role.html', {'form': form})


def edit_staff_role(request, pk):
    staff_role = get_object_or_404(StaffRole, pk=pk)
    
    if request.method == 'POST':
        # Check if the delete button was clicked
        if 'delete' in request.POST:
            staff_role.delete()
            messages.success(request, "Staff role deleted successfully.")
        # Otherwise, handle the regular form submission
        form = StaffRoleForm(request.POST, instance=staff_role)
        if form.is_valid():
            form.save()
            messages.success(request, "Staff role updated successfully.")
            return redirect('staff_roles_list')
    else:
        form = StaffRoleForm(instance=staff_role)

    return render(request, 'staff/edit_staff_role.html', {'form': form, 'staff_role': staff_role})


def lists(request):
    return render(request, 'staff/lists.html')


def physician_panel_details_view(request):
    physicians = PhysicianPanelDetails.objects.all()  # Adjust if you want fewer records
    return render(request, 'physicians/physician_panel_details.html', {'physicians': physicians})


def physician_panel_master_view(request):
    panels = FourCutPanelMaster.objects.all()
    latest_entry = FourCutPanelMaster.objects.order_by('-report_year', '-report_month').first()
    latest_year = latest_entry.report_year if latest_entry else None
    latest_month = latest_entry.report_month if latest_entry else None
    context = {
        'panels': panels,
        'latest_year': latest_year,
        'latest_month': latest_month,
    }
    return render(request, 'physicians/physician_panel_master.html', context)


def clinic_staff_list(request):
    clinic_staff = ClinicStaffAllocation.objects.all()
    return render(request, 'clinics/clinic_staff_list.html', {'clinic_staff': clinic_staff})

def add_contact_info(request, staff_id):
    staff = get_object_or_404(Staff, pk=staff_id)
    if request.method == 'POST':
        form = StaffLocationContactForm(request.POST)
        if form.is_valid():
            contact_info = form.save(commit=False)
            contact_info.staff = staff
            contact_info.save()
            return redirect('staff_detail', pk=staff_id)
    else:
        form = StaffLocationContactForm(initial={'staff': staff})
    
    return render(request, 'staff/add_contact_info.html', {'form': form, 'staff': staff})

def edit_contact_info(request, contact_id):
    contact_info = get_object_or_404(StaffLocationContact, pk=contact_id)

    # Handle delete request
    if 'delete_contact' in request.POST:
        staff_id = contact_info.staff_id  # Save staff ID for redirecting
        contact_info.delete()
        messages.success(request, "Contact information deleted successfully.")
        return redirect('staff_detail', pk=staff_id)

    # Handle edit request
    if request.method == 'POST':
        form = StaffLocationContactForm(request.POST, instance=contact_info)
        if form.is_valid():
            form.save()
            messages.success(request, "Contact information updated successfully.")
            return redirect('staff_detail', pk=contact_info.staff_id)
    else:
        form = StaffLocationContactForm(instance=contact_info)
    
    return render(request, 'staff/edit_contact_info.html', {'form': form, 'contact_info': contact_info})


def delete_staff(request, pk):
    staff = get_object_or_404(Staff, pk=pk)
    if request.method == 'POST':
        staff.delete()
        return redirect('list_staff')
    return render(request, 'staff/confirm_delete.html', {'staff': staff})


def physician_list(request):
    physicians = Physician.objects.all()
    return render(request, 'physicians/physician_list.html', {'physicians': physicians})


def clinic_physician_list(request):
    clinic_physicians = ClinicPhysician.objects.select_related('physician').all()
    return render(request, 'clinics/clinic_physician_list.html', {'clinic_physicians': clinic_physicians})


def clinic_list(request):
    try:
        clinics = ClinicWithActivePhysicianCount.objects.all()
    except Exception as e:
        # Fallback to basic Clinic model if the view doesn't exist
        from django.db import connection
        from django.utils import timezone

        # Use raw SQL to get clinic data with active physician count
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    c.clinic_id,
                    c.clinic_name,
                    c.street_address,
                    c.floor_unit_room,
                    c.city,
                    c.business_phone,
                    c.fax,
                    c.include_on_eopcn_website,
                    c.primary_contact,
                    c.primary_contact_role,
                    c.primary_contact_phone,
                    c.primary_contact_email,
                    c.clinic_website,
                    COUNT(DISTINCT CASE
                        WHEN (cp.date_active_in_clinic <= GETDATE() AND
                             (cp.date_left_clinic IS NULL OR cp.date_left_clinic > GETDATE()))
                        THEN cp.physician_id
                        ELSE NULL
                    END) AS active_physician_count,
                    (SELECT MAX(cn.note_date)
                     FROM mh_clinic_notes cn
                     WHERE cn.clinic_id = c.clinic_id) AS last_note_date
                FROM
                    mh_clinics c
                LEFT JOIN
                    mh_clinics_physicians cp ON c.clinic_id = cp.clinic_id
                GROUP BY
                    c.clinic_id,
                    c.clinic_name,
                    c.street_address,
                    c.floor_unit_room,
                    c.city,
                    c.business_phone,
                    c.fax,
                    c.include_on_eopcn_website,
                    c.primary_contact,
                    c.primary_contact_role,
                    c.primary_contact_phone,
                    c.primary_contact_email,
                    c.clinic_website
                ORDER BY c.clinic_name
            """)

            columns = [col[0] for col in cursor.description]
            clinics = [dict(zip(columns, row)) for row in cursor.fetchall()]

    return render(request, 'clinics/clinic_list.html', {'clinics': clinics})


def physician_detail(request, physician_id):
    # Retrieve the primary Physician record
    physician = get_object_or_404(Physician, physician_id=physician_id)

    # Get all panel details for this physician
    panel_details = PhysicianPanelDetails.objects.filter(physician_id=physician_id)

    # Get the QR code URL with SAS token using the new filename
    qr_code_name = "QRCode for EOPCN Physician Information Update Form_cropped.png" # Updated filename
    qr_code_url = get_blob_sas_url_physician_update(qr_code_name)

    # Form URL for the QR code to link to - hardcoded exact URL
    form_url = "https://forms.office.com/r/eJphxf5AEM"

    # Note: logo_url is provided by the global context processor, so we don't override it here
    # This ensures the logo works consistently across all pages

    context = {
        'physician': physician,
        'qr_code_url': qr_code_url,
        'form_url': form_url,
        'panel_details': panel_details,
    }

    return render(request, 'physicians/physician_detail.html', context)


# View for editing physician information
def edit_physician(request, physician_id):
    print(f"edit_physician view called with method: {request.method}")
    physician = get_object_or_404(Physician, physician_id=physician_id)

    if request.method == 'POST':
        print(f"POST request received for physician {physician_id}")
        print(f"POST data keys: {list(request.POST.keys())}")

        original_data = capture_original_data(physician, ['physician_id'])

        # Capture original language data
        original_languages = list(physician.languages.all().values_list('language', flat=True))

        form = PhysicianForm(request.POST, instance=physician)
        language_formset = PhysicianLanguageFormSet(request.POST, instance=physician)

        # Debug prints (can be removed in production)
        print(f"Form valid: {form.is_valid()}")
        print(f"Language formset valid: {language_formset.is_valid()}")
        if not language_formset.is_valid():
            print(f"Language formset errors: {language_formset.errors}")

        if form.is_valid() and language_formset.is_valid():
            # print("Form is valid, proceeding with save...")  # Debug - can be removed
            field_labels = get_field_labels_for_model('physician')
            changes = track_changes(original_data, form.cleaned_data, field_labels)
            updated_physician = form.save()
            updated_physician.comment = form.cleaned_data.get('comment')
            updated_physician.save()
            
            # Handle language updates with formset
            try:
                with transaction.atomic():
                    # Save the language formset
                    language_instances = language_formset.save(commit=False)

                    # Set created_by and modified_by for new instances
                    for instance in language_instances:
                        if not instance.pk:  # New instance
                            instance.created_by = get_safe_user_name(request.user)
                        instance.modified_by = get_safe_user_name(request.user)
                        instance.physician_Active = 'Yes'
                        instance.save()

                    # Handle deletions
                    for obj in language_formset.deleted_objects:
                        obj.delete()

                    print(f"Language formset saved successfully")  # Debug - can be removed

                    # Track language changes for email
                    new_languages = list(updated_physician.languages.all().values_list('language', flat=True))
                    if set(original_languages) != set(new_languages):
                        old_languages_str = ', '.join(original_languages) if original_languages else 'None specified'
                        new_languages_str = ', '.join(new_languages) if new_languages else 'None specified'
                        changes['languages'] = {
                            'old_value': old_languages_str,
                            'new_value': new_languages_str,
                            'field_label': 'Languages Spoken'
                        }

            except Exception as e:
                messages.error(request, f"Error updating languages: {str(e)}")
                print(f"Language formset error: {str(e)}")  # Debug print - can be removed
                import traceback
                print(f"Full traceback: {traceback.format_exc()}")  # Debug - can be removed
            
            user_info = {
                'full_name': get_safe_user_name(request.user),
                'email': get_safe_user_email(request.user)
            }
            
            email_leadership = form.cleaned_data.get('email_leadership')
            email_group = form.cleaned_data.get('email_group')
            
            # Get checkbox states
            left_eopcn_checked = form.cleaned_data.get('left_eopcn')
            no_longer_practicing_checked = form.cleaned_data.get('no_longer_practicing')
            
            # Get dates
            end_date = form.cleaned_data.get('date_left_eopcn')
            no_longer_practicing_date = form.cleaned_data.get('date_no_longer_practicing')
            
            # Only send emails if email_leadership is True
            if email_leadership:
                details_url = request.build_absolute_uri(reverse('physician_detail', args=[physician.physician_id]))
                # Check for the specific scenarios in order of priority
                if no_longer_practicing_checked and no_longer_practicing_date:
                    # Send email about physician no longer practicing
                    subject = f"Member's Medical Practice End Date: {updated_physician.physician_name}"
                    context = {'physician': updated_physician, 'user': user_info, 'details_url': details_url}
                    send_email(subject, context, None, 'physicians/emails/physician_practice_end_notification.html', custom_email_group=email_group)
                elif left_eopcn_checked and end_date:
                    # Send email about physician leaving EOPCN
                    subject = f"EOPCN Membership End Date: {updated_physician.physician_name}"
                    context = {'physician': updated_physician, 'user': user_info, 'details_url': details_url}
                    # Send to selected email group
                    send_email(subject, context, None, 'physicians/emails/physician_membership_end_notification.html', custom_email_group=email_group)
                else:
                    # Send regular update email
                    subject = f"Physician Update: {updated_physician.physician_name}"
                    context = {
                        'physician': updated_physician,
                        'changes': changes,
                        'user': user_info,
                        'details_url': details_url,
                        'comment': form.cleaned_data.get('comment')
                    }
                    send_email(subject, context, None, 'physicians/emails/physician_update_notification.html', custom_email_group=email_group)
            
            messages.success(request, f"Physician {physician.physician_name} updated successfully.")
            return redirect('physician_detail', physician_id=physician.physician_id)
        else:
            print("Form errors:", form.errors)
            print("Language formset errors:", language_formset.errors)
    else:
        form = PhysicianForm(instance=physician)
        language_formset = PhysicianLanguageFormSet(instance=physician)

    context = {
        'form': form,
        'language_formset': language_formset,
        'physician': physician,
    }
    return render(request, 'physicians/edit_physician.html', context)


# View for editing clinic associations with formset
def edit_physician_clinics(request, physician_id):
    physician = get_object_or_404(Physician, physician_id=physician_id)
    
    ClinicPhysicianFormSet = inlineformset_factory(
        Physician,
        ClinicPhysician,
        form=ClinicPhysicianForm,
        fields=['clinic', 'portion_of_practice', 'accepting_patients', 'include_on_afad_website', 'include_on_eopcn_website', 'date_active_in_clinic', 'date_left_clinic', 'CPAR_Panel_ID', 'active_CII', 'active_CPAR'],
        extra=1,
        can_delete=True
    )
    
    if request.method == 'POST':
        # Capture original data for change tracking
        original_data_list = []
        for clinic_physician in physician.clinicphysician_set.all():
            original_data_list.append(capture_original_data(clinic_physician, ['clinics_physicians_ID']))

        formset = ClinicPhysicianFormSet(request.POST, instance=physician)
        comment_form = CommentForm(request.POST)

        if formset.is_valid():
            # Track changes using utility function for formsets
            field_labels = get_field_labels_for_model('clinic_physician')
            clinic_changes = track_formset_changes(original_data_list, formset, field_labels)

            # Save the formset
            for form in formset:
                if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                    clinic_physician = form.save(commit=False)
                    clinic_physician.modified_by = get_safe_user_name(request.user)
                    clinic_physician.save()

            # Handle deletions
            formset.save()
            
            # Send email notification if requested
            if comment_form.is_valid() and comment_form.cleaned_data.get('email_leadership'):
                # Send email notification
                subject = f"Clinic Associations Updated for {physician.physician_name}"
                details_url = request.build_absolute_uri(reverse('physician_detail', args=[physician.physician_id]))
                context = {
                    'physician': physician,
                    'clinic_physicians': ClinicPhysician.objects.filter(physician=physician).select_related('clinic'),
                    'changes': clinic_changes,
                    'comment': comment_form.cleaned_data.get('comment', ''),
                    'user': {
                        'full_name': get_safe_user_name(request.user),
                        'email': get_safe_user_email(request.user)
                    },
                    'details_url': details_url
                }
                
                # Get email list based on selected group
                email_group = comment_form.cleaned_data.get('email_group')
                
                # Use the correct template name that matches your existing file
                send_email(
                    subject=subject,
                    context=context,
                    to_email_list=None,
                    template_name='physicians/emails/physician_clinic_update_notification.html',
                    custom_email_group=email_group
                )
            
            messages.success(request, f'Clinic associations for {physician.physician_name} have been updated successfully.')
            return redirect('physician_detail', physician_id=physician.physician_id)
    else:
        formset = ClinicPhysicianFormSet(instance=physician)
        comment_form = CommentForm()
    
    return render(request, 'physicians/edit_physician_clinics.html', {
        'physician': physician,
        'formset': formset,
        'comment_form': comment_form,
    })

def add_physician(request):
    if request.method == 'POST':
        physician_form = PhysicianForm(request.POST)
        clinic_formset = modelformset_factory(
            ClinicPhysician,
            form=ClinicPhysicianForm,
            fields=['clinic', 'portion_of_practice', 'accepting_patients', 'include_on_afad_website', 'include_on_eopcn_website', 'date_active_in_clinic', 'date_left_clinic', 'CPAR_Panel_ID', 'active_CII', 'active_CPAR'],
            extra=3,
            max_num=3
        )(request.POST)

        # Create a temporary physician instance for the language formset
        temp_physician = Physician() if not physician_form.instance.pk else physician_form.instance
        language_formset = PhysicianLanguageFormSet(request.POST, instance=temp_physician)

        # Debug prints for add physician (can be removed in production)
        print(f"Add physician - Form valid: {physician_form.is_valid()}")
        print(f"Add physician - Clinic formset valid: {clinic_formset.is_valid()}")
        print(f"Add physician - Language formset valid: {language_formset.is_valid()}")

        if physician_form.is_valid() and clinic_formset.is_valid() and language_formset.is_valid():
            try:
                with transaction.atomic():
                    # Save the physician
                    physician = physician_form.save(commit=False)
                    physician.created_by = get_safe_user_name(request.user)
                    physician.modified_by = get_safe_user_name(request.user)
                    physician.save()
                    
                    # Save languages using formset
                    language_formset.instance = physician  # Set the physician instance
                    language_instances = language_formset.save(commit=False)

                    for instance in language_instances:
                        instance.physician = physician
                        instance.created_by = get_safe_user_name(request.user)
                        instance.modified_by = get_safe_user_name(request.user)
                        instance.physician_Active = 'Yes'
                        instance.save()

                    print(f"Created {len(language_instances)} languages")  # Debug - can be removed
                    
                    # Save clinic associations
                    for form in clinic_formset:
                        if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                            clinic_physician = form.save(commit=False)
                            clinic_physician.physician = physician
                            clinic_physician.created_by = get_safe_user_name(request.user)
                            clinic_physician.modified_by = get_safe_user_name(request.user)
                            clinic_physician.save()
                    
                    # Send email notification if requested
                    if physician_form.cleaned_data.get('email_leadership'):
                        subject = f"New Primary Care Provider Added: {physician.physician_name}"

                        # Prepare the user info dictionary
                        user_info = {
                            'full_name': get_safe_user_name(request.user),
                            'email': get_safe_user_email(request.user)
                        }

                        details_url = request.build_absolute_uri(reverse('physician_detail', args=[physician.physician_id]))
                        
                        # Get clinic associations for email
                        clinic_associations = ClinicPhysician.objects.filter(physician=physician).select_related('clinic')
                        
                        context = {
                            'physician': physician,
                            'clinic_associations': clinic_associations,
                            'comment': physician_form.cleaned_data.get('comment', ''),
                            'user': user_info,
                            'details_url': details_url
                        }

                        # Get email list based on selected group or use default
                        selected_group = physician_form.cleaned_data.get('email_group')
                        to_email_list = get_email_list_for_template('physicians/emails/physician_add_notification.html', selected_group)

                        send_email(
                            subject=subject,
                            context=context,
                            to_email_list=to_email_list,
                            template_name='physicians/emails/physician_add_notification.html'
                        )
                    
                    messages.success(request, f'Primary care provider {physician.physician_name} has been successfully added.')
                    return redirect('physician_detail', physician_id=physician.physician_id)
                    
            except Exception as e:
                messages.error(request, f"Error saving physician: {str(e)}")
    else:
        physician_form = PhysicianForm()
        clinic_formset = modelformset_factory(
            ClinicPhysician,
            form=ClinicPhysicianForm,
            fields=['clinic', 'portion_of_practice', 'accepting_patients', 'include_on_afad_website', 'include_on_eopcn_website', 'date_active_in_clinic', 'date_left_clinic', 'CPAR_Panel_ID', 'active_CII', 'active_CPAR'],
            extra=3,
            max_num=3
        )(queryset=ClinicPhysician.objects.none())
        language_formset = PhysicianLanguageFormSet()

    return render(request, 'physicians/add_physician.html', {
        'physician_form': physician_form,
        'clinic_formset': clinic_formset,
        'language_formset': language_formset,
    })


def supervisor_staff_view(request, supervisor_id):
    supervisor = get_object_or_404(StaffSupervisor, pk=supervisor_id)
    show_inactive = request.GET.get('show_inactive', 'true').lower() == 'true'  # Default to showing inactive staff
    
    # Get the active assignment for the supervisor to find their role
    supervisor_assignment = StaffAssignment.objects.filter(
        staff=supervisor.staff, 
        currently_active=True,
    ).select_related('role', 'position').first()
    
    supervisor_role = supervisor_assignment.role.role_name if supervisor_assignment and supervisor_assignment.role else "Supervisor"
    
    # Get the supervisor's position number
    supervisor_position_number = None
    if supervisor_assignment and supervisor_assignment.position:
        supervisor_position_number = supervisor_assignment.position.position_number
    
    # Get current date for leave comparisons
    current_date = timezone.now().date()
    
    # Get distinct staff members who report to this supervisor to avoid duplicates
    staff_ids_reporting_to_supervisor = StaffAssignment.objects.filter(
        supervisor=supervisor
    ).values_list('staff_id', flat=True).distinct()
    
    # Get the most recent assignment for each staff member
    staff_assignments = []
    for staff_id in staff_ids_reporting_to_supervisor:
        # Get all assignments for this staff member under this supervisor
        assignments = StaffAssignment.objects.filter(
            supervisor=supervisor,
            staff_id=staff_id
        ).select_related('staff', 'role', 'service', 'position').order_by('-start_date')
        
        if assignments.exists():
            # Add the most recent assignment to our list
            staff_assignments.append(assignments.first())
    
    # Get all leave records for staff assigned to this supervisor
    leave_records = StaffLeave.objects.filter(
        staff_id__in=staff_ids_reporting_to_supervisor,
        leave_start_date__lte=current_date,  # Leave has started
    ).select_related('leave_type')
    
    # Create a dictionary of staff_id -> leave record for quick lookup
    staff_on_leave = {}
    for leave in leave_records:
        # For current leaves (either still active or without end date)
        if leave.return_date is None or leave.return_date >= current_date:
            staff_on_leave[leave.staff_id] = leave

    # Dictionary to store most recent position numbers for each staff member
    staff_position_numbers = {}
    
    # Query the most recent position for each staff member
    recent_positions = StaffAssignment.objects.filter(
        staff_id__in=staff_ids_reporting_to_supervisor,
        position__isnull=False
    ).select_related('position').order_by('staff_id', '-start_date')
    
    # Process the positions to keep only the most recent per staff
    for assignment in recent_positions:
        if assignment.staff_id not in staff_position_numbers and assignment.position:
            staff_position_numbers[assignment.staff_id] = assignment.position.position_number
    
    # Prepare staff assignments with additional metadata
    for assignment in staff_assignments:
        # Set photo URL
        if assignment.staff.photo:
            assignment.staff.photo_url = get_blob_sas_url(assignment.staff.photo.name)
        else:
            assignment.staff.photo_url = None
        
        # Add leave information
        assignment.staff.on_leave = False
        assignment.staff.leave_info = None
        if assignment.staff.pk in staff_on_leave:
            leave = staff_on_leave[assignment.staff.pk]
            assignment.staff.on_leave = True
            assignment.staff.leave_info = {
                'leave_type': leave.leave_type.leave_type_name if leave.leave_type else "On Leave",
                'return_date': leave.return_date,
                'leave_start_date': leave.leave_start_date
            }
        
        # Add position number to staff
        if assignment.position and assignment.position.position_number:
            assignment.staff.position_number = assignment.position.position_number
        elif assignment.staff.pk in staff_position_numbers:
            assignment.staff.position_number = staff_position_numbers[assignment.staff.pk]
        else:
            assignment.staff.position_number = None
        
        # Check if role is active (no end date or end date is in the future)
        assignment.is_role_active = assignment.currently_active and (assignment.end_date is None or assignment.end_date > current_date)
        
        # Calculate overall staff activity status
        # Staff is considered active if they are currently_active at the staff level (even if on leave)
        assignment.staff.is_properly_active = assignment.staff.currently_active
        
        # Calculate the display status for this assignment
        # For display purposes, we consider:
        # - Staff who are currently_active = TRUE at the staff level should be shown as active (even if on leave)
        # - Assignment that is role_active should be shown as an active role
        assignment.should_be_greyed_out = not assignment.staff.is_properly_active
        
        # Check if this staff member is also a supervisor
        assignment.staff.is_supervisor = StaffSupervisor.objects.filter(staff=assignment.staff).exists()
        if assignment.staff.is_supervisor:
            supervisor_obj = StaffSupervisor.objects.get(staff=assignment.staff)
            assignment.staff.supervisor_id = supervisor_obj.supervisor_id
            
            # Get ALL staff assignments that report to this supervisor
            # This ensures we capture every role a staff member has under this supervisor
            all_supervised_assignments = StaffAssignment.objects.filter(
                supervisor=supervisor_obj
            ).select_related('staff', 'position', 'role', 'service').order_by('staff__first_name', 'staff__last_name', '-start_date')
            
            # Get all staff IDs for leave lookup
            sub_staff_ids = all_supervised_assignments.values_list('staff_id', flat=True).distinct()
            
            # Get leave records for all these staff members
            sub_leave_records = StaffLeave.objects.filter(
                staff_id__in=sub_staff_ids,
                leave_start_date__lte=current_date,
            ).select_related('leave_type')
            
            # Create a dictionary of staff_id -> leave record for quick lookup
            sub_staff_on_leave = {}
            for leave in sub_leave_records:
                if leave.return_date is None or leave.return_date >= current_date:
                    sub_staff_on_leave[leave.staff_id] = leave
            
            # Group by staff_id to handle multiple roles
            grouped_by_staff = {}
            for sub_assignment in all_supervised_assignments:
                staff_id = sub_assignment.staff.pk
                if staff_id not in grouped_by_staff:
                    grouped_by_staff[staff_id] = []
                grouped_by_staff[staff_id].append(sub_assignment)
            
            # Create supervised staff list with proper role and status information
            supervised_staff = []
            for staff_id, staff_assignments_list in grouped_by_staff.items():
                # Get the staff object (same for all assignments)
                staff_obj = staff_assignments_list[0].staff
                
                # Get the most recent assignment for this staff member
                most_recent = staff_assignments_list[0]  # Assignments should be ordered by -start_date
                
                # Check specifically if the assignment has an end date (regardless of currently_active flag)
                has_ended_assignment = most_recent.end_date is not None and most_recent.end_date <= current_date
                
                # Check if this staff member is on leave
                on_leave = staff_id in sub_staff_on_leave
                leave_info = None
                if on_leave:
                    leave = sub_staff_on_leave[staff_id]
                    leave_info = {
                        'leave_type': leave.leave_type.leave_type_name if leave.leave_type else "On Leave",
                        'return_date': leave.return_date,
                        'leave_start_date': leave.leave_start_date
                    }
                
                # Only consider active assignments (by our criteria)
                active_assignments = []
                inactive_assignments = []
                
                # Categorize assignments as active or inactive
                for sa in staff_assignments_list:
                    is_role_active = sa.currently_active and (sa.end_date is None or sa.end_date > current_date)
                    sa_object = {

                        'assignment_id': sa.pk,
                        'role_name': sa.role.role_name if sa.role else "No Role",
                        'program_name': sa.service.service_name if sa.service else "No Program",
                        'position_number': sa.position.position_number if sa.position else None,
                        'start_date': sa.start_date,
 'end_date': sa.end_date,
                        'currently_active': sa.currently_active,
                        'is_role_active': is_role_active
                    }
                    if is_role_active:
                        active_assignments.append(sa_object)
                    else:
                        inactive_assignments.append(sa_object)
                
                # Calculate active status based on assignment-level status, not just global staff status
                # A staff member in a nested list should be considered inactive if ALL their assignments 
                # to this specific supervisor are inactive (currently_active=False AND have an end_date)
                has_active_assignment_to_this_supervisor = len(active_assignments) > 0
                
                # Override the global staff active status for the nested supervisor view
                # Even if staff is globally active, grey them out if they have no active assignments to this supervisor
                should_be_greyed_out = not has_active_assignment_to_this_supervisor
                
                # For the position number, prioritize the position from an active assignment,
                # or fall back to the position from the most recent assignment
                position_number = None
                if active_assignments and active_assignments[0]['position_number']:
                    position_number = active_assignments[0]['position_number']
                elif most_recent.position and most_recent.position.position_number:
                    position_number = most_recent.position.position_number
                
                # Create the staff object with all the necessary info
                staff_detail = {
                    'staff_id': staff_obj.staff_id,
                    'first_name': staff_obj.first_name,
                    'last_name': staff_obj.last_name,
                    'currently_active': staff_obj.currently_active,
                    'is_properly_active': has_active_assignment_to_this_supervisor,
                    'should_be_greyed_out': should_be_greyed_out,
                    'has_ended_assignment': has_ended_assignment,  # Add this field based on end date
                    'position_number': position_number,
                    'on_leave': on_leave,
                    'leave_info': leave_info,
                    'photo_url': get_blob_sas_url(staff_obj.photo.name) if staff_obj.photo else None,
                    'active_assignments': active_assignments,
                    'inactive_assignments': inactive_assignments,
                    'has_multiple_roles': len(active_assignments) + len(inactive_assignments) > 1
                }
                
                supervised_staff.append(staff_detail)
            
            # Sort supervised staff by position number alphabetically (None values at the end)
            supervised_staff.sort(key=lambda x: (x['position_number'] is None, x['position_number'] or ""))
            
            # Filter out inactive staff if not showing them
            # For the nested staff view, use the assignment-level active status
            # not the global staff active status
            if not show_inactive:
                supervised_staff = [s for s in supervised_staff if s['is_properly_active']]
                
            assignment.staff.supervised_staff = supervised_staff
        else:
            assignment.staff.supervisor_id = None
            assignment.staff.supervised_staff = []
    
    # Filter staff if not showing inactive
    # We only filter out completely inactive staff based on our refined definition
    if not show_inactive:
        staff_assignments = [a for a in staff_assignments if a.staff.is_properly_active]
    
    # Group by programs and roles
    grouped_programs = {}
    program_has_active_staff = {}
      # First pass: Group by programs
    for assignment in staff_assignments:
        program_name = assignment.service.service_name if assignment.service else "No Program"
        if program_name not in grouped_programs:
            grouped_programs[program_name] = []
            program_has_active_staff[program_name] = False
        
        grouped_programs[program_name].append(assignment)
        
        # Check if staff is properly active AND if assignment hasn't ended to update the program's active status
        # An assignment is considered "not ended" if:
        # 1. Staff is properly active
        # 2. Assignment has no end date OR end date is in the future
        if assignment.staff.is_properly_active and (assignment.end_date is None or assignment.end_date > current_date):            program_has_active_staff[program_name] = True
    
    # Second pass: Group by roles within each program
    program_role_groups = {}
    role_has_active_staff = {}
    
    for program_name, assignments in grouped_programs.items():
        role_groups = {}
        role_has_active_staff[program_name] = {}
        
        for assignment in assignments:
            role_name = assignment.role.role_name if assignment.role else "No Role"
            if role_name not in role_groups:
                role_groups[role_name] = []
                role_has_active_staff[program_name][role_name] = False
            role_groups[role_name].append(assignment)
            
            # Check if staff is properly active AND if assignment hasn't ended to update the role's active status
            if assignment.staff.is_properly_active and (assignment.end_date is None or assignment.end_date > current_date):
                role_has_active_staff[program_name][role_name] = True
        
        # Sort assignments within each role by position number alphabetically
        for role_name in role_groups:
            role_groups[role_name].sort(key=lambda a: (a.staff.position_number is None, a.staff.position_number or ""))
        
        program_role_groups[program_name] = role_groups
    
    # Generate SAS URL for supervisor's photo
    if supervisor.staff.photo:
        supervisor_photo_url = get_blob_sas_url(supervisor.staff.photo.name)
    else:
        supervisor_photo_url = None
    
    return render(request, 'staff/supervisor_staff.html', {
        'supervisor': supervisor,
        'supervisor_photo_url': supervisor_photo_url,
        'supervisor_role': supervisor_role,
        'supervisor_position_number': supervisor_position_number,
        'grouped_programs': program_role_groups,
        'program_has_active_staff': program_has_active_staff,
        'role_has_active_staff': role_has_active_staff,
        'show_inactive': show_inactive,
        'current_date': current_date,
    })


def clinic_detail(request, clinic_id):
    clinic = get_object_or_404(Clinic, clinic_id=clinic_id)
    
    # Get all physicians associated with this clinic
    clinic_physicians = ClinicPhysician.objects.filter(clinic=clinic).select_related('physician')
    
    # Collect all physician IDs
    physician_ids = [cp.physician.physician_id for cp in clinic_physicians]
    
    # Get panel sizes from the PhysicianPanelDetails model
    panel_details = PhysicianPanelDetails.objects.filter(
        physician_id__in=physician_ids,
        clinic_id=clinic_id  # Make sure we're getting details for this specific clinic
    )
    
    # Create a mapping of physician_id to their panel details
    panel_details_dict = {pd.physician_id: pd for pd in panel_details}
    
    # Attach panel size data to each clinic_physician object
    for cp in clinic_physicians:
        panel_detail = panel_details_dict.get(cp.physician.physician_id)
        if panel_detail:
            cp.panel_size_oct_2024 = panel_detail.panel_size_oct_2024
            cp.panel_size_report_year = panel_detail.panel_size_report_year
            cp.panel_size_report_month = panel_detail.panel_size_report_month
        else:
            cp.panel_size_oct_2024 = None
            cp.panel_size_report_year = None
            cp.panel_size_report_month = None
    
    # Get staff allocations
    clinic_staff = ClinicStaffAllocation.objects.filter(
        clinic_name=clinic.clinic_name
    ).order_by('last_name', 'first_name')
    
    # Get clinic notes - fetch all clinic notes for this specific clinic
    # Use the related_name 'notes' that's defined in the ForeignKey relationship
    # Order by date_of_entry descending (newest first)
    clinic_notes = ClinicNote.objects.filter(clinic_id=clinic_id).order_by('-date_of_entry')
    
    # Get clinic notes with timezone handling
    try:
        # Try ORM first - get notes and prefetch physician attendance
        clinic_notes = ClinicNote.objects.filter(clinic_id=clinic_id).order_by('-date_of_entry')
        
        # For each note, manually fetch physician attendance to ensure we get the data
        for note in clinic_notes:
            try:
                # Try to use the ORM relationship if it exists
                attendance_list = list(note.physician_attendance.all())
                note.physician_attendances = attendance_list
            except:
                # Fallback to raw SQL for this specific note
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT p.physician_id, p.first_name, p.last_name
                        FROM mh_clinic_note_physician_attendance pa
                        JOIN mh_physicians p ON pa.physician_id = p.physician_id
                        WHERE pa.note_ID = %s
                    """, [note.note_id])
                    
                    physician_attendances_list = []
                    for p_row in cursor.fetchall():
                        temp_physician_obj = _TempPhysician(physician_id=p_row[0], first_name=p_row[1], last_name=p_row[2])
                       
                        temp_attendance_obj = _TempAttendance(physician=temp_physician_obj)
                        physician_attendances_list.append(temp_attendance_obj)
                    
                    note.physician_attendances = physician_attendances_list
                    
    except Exception as e:
        print(f"Error fetching clinic notes via ORM: {e}")
       
        # Complete fallback to raw SQL
        with connection.cursor() as cursor:
            cursor.execute('''
                SELECT note_ID, clinic_id, clinic_name, date_of_entry, 
                       author_of_entry, note, type_of_entry, 
                       date_modified, date_created, other_attendees
                FROM mh_clinic_notes
                WHERE clinic_id = %s

                ORDER BY date_of_entry DESC
            ''', [clinic_id])
            columns = [col[0] for col in cursor.description]
            clinic_notes_raw = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            processed_clinic_notes = []
            for raw_note_dict in clinic_notes_raw:
                cursor.execute("""
                    SELECT p.physician_id, p.first_name, p.last_name
                    FROM mh_clinic_note_physician_attendance pa
                    JOIN mh_physicians p ON pa.physician_id = p.physician_id
                    WHERE pa.note_ID = %s
                """, [raw_note_dict['note_ID']])

                physician_attendances_list = []
                for p_row in cursor.fetchall():
                    temp_physician_obj = _TempPhysician(physician_id=p_row[0], first_name=p_row[1], last_name=p_row[2])
                    temp_attendance_obj = _TempAttendance(physician=temp_physician_obj)
                   
                    physician_attendances_list.append(temp_attendance_obj)
                
                note_obj = _TempNoteObject(raw_note_dict, physician_attendances_list)
                # Add the attendances as a simple list for template access
               
                note_obj.physician_attendances = physician_attendances_list
                processed_clinic_notes.append(note_obj)
            clinic_notes = processed_clinic_notes
    
    # Get all clinics for the dropdown, ordered by clinic name
    all_clinics = Clinic.objects.all().order_by('clinic_name')
    
    # Find previous and next clinic for navigation
    previous_clinic = None
    next_clinic = None
    
    # Get all clinic IDs ordered by name
    clinic_ids = list(all_clinics.values_list('clinic_id', flat=True))
    
    # Find the index of the current clinic
    if clinic_id in clinic_ids:
        current_index = clinic_ids.index(clinic_id)
        
        # Get previous clinic (if not the first)
        if current_index > 0:
            previous_clinic = all_clinics.get(clinic_id=clinic_ids[current_index - 1])

        
        # Get next clinic (if not the last)
        if current_index < len(clinic_ids) - 1:
            next_clinic = all_clinics.get(clinic_id=clinic_ids[current_index + 1])
    
    context = {
        'clinic': clinic,
        'clinic_physicians': clinic_physicians,
        'clinic_staff': clinic_staff,
        'clinic_notes': clinic_notes,
        'all_clinics': all_clinics,
        'previous_clinic': previous_clinic,
        'next_clinic': next_clinic,
    }
    
    return render(request, 'clinics/clinic_detail.html', context)

def add_clinic(request):
    if request.method == 'POST':
        form = ClinicForm(request.POST)
        if form.is_valid():
            # Save the clinic
            new_clinic = form.save(commit=False)
            
            # Check if user is authenticated before calling get_full_name()
            if request.user.is_authenticated:
                new_clinic.created_by = request.user.get_full_name()
                new_clinic.modified_by = request.user.get_full_name()
            else:
                new_clinic.created_by = "Anonymous (Testing)"
                new_clinic.modified_by = "Anonymous (Testing)"
                
            new_clinic.date_created = timezone.now()
            new_clinic.date_modified = timezone.now()
            new_clinic.save()
            
            # Check if email_leadership is checked
            email_leadership = form.cleaned_data.get('email_leadership')
            comment = form.cleaned_data.get('comment')
            
            # Prepare the user info dictionary - handle anonymous users
            user_info = {}
            if request.user.is_authenticated:
                user_info = {
                    'full_name': request.user.get_full_name(),
                    'email': request.user.email
                }
            else:
                user_info = {
                    'full_name': 'Anonymous (Testing)',
                    'email': '<EMAIL>'
                }
            
            if email_leadership:
                try:
                    # Get the selected email group from the form
                    email_group = form.cleaned_data.get('email_group')
                    details_url = request.build_absolute_uri(reverse('clinic_detail', args=[new_clinic.clinic_id]))
                    send_email(
                        f'New Clinic Added: {new_clinic.clinic_name}',
                        {
                            'clinic': form.cleaned_data,
                            'comment': comment,
                            'user': user_info,
                            'details_url': details_url
                        },
                        None,
                        'clinics/emails/clinic_add_notification.html',
                        custom_email_group=email_group
                    )
                except Exception as email_error:
                    # Log the error but don't prevent clinic creation
                    print(f"Error sending email: {email_error}")
                    messages.warning(request, f"Clinic was added but email notification could not be sent. Error: {str(email_error)}")
            
            messages.success(request, f"Clinic {new_clinic.clinic_name} was added successfully.")
            return redirect('clinic_list')
    else:
        form = ClinicForm()
    
    return render(request, 'clinics/add_clinic.html', {
        'form': form,
    })

def edit_clinic(request, clinic_id):
    clinic = get_object_or_404(Clinic, clinic_id=clinic_id)

    if request.method == 'POST':
        original_data = capture_original_data(clinic, ['clinic_id'])
        form = ClinicForm(request.POST, instance=clinic)
        if form.is_valid():
            field_labels = get_field_labels_for_model('clinic')
            changes = track_changes(original_data, form.cleaned_data, field_labels)
            # Save the updated clinic
            updated_clinic = form.save(commit=False)
            
            # Check if user is authenticated before calling get_full_name()
            if request.user.is_authenticated:
                updated_clinic.modified_by = request.user.get_full_name()
            else:
                updated_clinic.modified_by = "Anonymous (Testing)"
                
            updated_clinic.date_modified = timezone.now()
            updated_clinic.save()
            
            # Check if email_leadership is checked
            email_leadership = form.cleaned_data.get('email_leadership')
            comment = form.cleaned_data.get('comment')
            
            # Prepare the user info dictionary - handle anonymous users
            user_info = {}
            if request.user.is_authenticated:
                user_info = {
                    'full_name': request.user.get_full_name(),
                    'email': request.user.email
                }
            else:                user_info = {
                    'full_name': 'Anonymous (Testing)',
                    'email': '<EMAIL>'
                }
            
            if email_leadership:
                try:
                    # Get the selected email group from the form
                    email_group = form.cleaned_data.get('email_group')
                    details_url = request.build_absolute_uri(reverse('clinic_detail', args=[clinic.clinic_id]))
                    send_email(
                        f'Clinic Updated: {updated_clinic.clinic_name}',
                        {
                            'clinic': form.cleaned_data,
                            'changes': changes,
                            'comment': comment,
                            'user': user_info,
                            'details_url': details_url
                        },
                        None,
                        'clinics/emails/clinic_update_notification.html',
                        custom_email_group=email_group
                    )
                except Exception as email_error:
                    # Log the error but don't prevent clinic update
                    print(f"Error sending email: {email_error}")
                    messages.warning(request, f"Clinic was updated but email notification could not be sent. Error: {str(email_error)}")
            
            messages.success(request, f"Clinic {clinic.clinic_name} was updated successfully.")
            return redirect('clinic_detail', clinic_id=clinic.clinic_id)
    else:
        form = ClinicForm(instance=clinic)
    
    context = {
        'form': form,
        'clinic': clinic,
    }
    return render(request, 'clinics/edit_clinic.html', context)


def add_clinic_note(request, clinic_id):
    clinic = get_object_or_404(Clinic, clinic_id=clinic_id)
    
    # Get clinic physicians with their association data
    clinic_physicians = ClinicPhysician.objects.filter(
        clinic=clinic
    ).select_related('physician').order_by('physician__last_name', 'physician__first_name')
    
    if request.method == 'POST':
        form = ClinicNoteForm(request.POST, clinic=clinic)
        if form.is_valid():
            note = form.save(commit=False)
            note.clinic = clinic
            note.clinic_name = clinic.clinic_name
            
            # If no date is provided, use current date and time
            if not note.date_of_entry:
                note.date_of_entry = timezone.now()
                
            # If user is authenticated, use their name for author if not provided
            if not note.author_of_entry:
                note.author_of_entry = get_safe_user_name(request.user)

            # Save with error handling for date conversion
            try:
                note.save()
                
                # Save physician attendance data
                physicians = form.cleaned_data.get('physicians_in_attendance', [])
                if physicians:
                    # Use raw SQL for better control over data insertion
                    with connection.cursor() as cursor:
                        for physician in physicians:
                            cursor.execute("""
                                INSERT INTO mh_clinic_note_physician_attendance
                                (note_ID, physician_id, date_created)
                                VALUES (%s, %s, GETDATE())
                            """, [note.note_id, physician.physician_id])
                
                # Save other_attendees using raw SQL if provided
                other_attendees = form.cleaned_data.get('other_attendees', '')
                if other_attendees:
                    with connection.cursor() as cursor:
                        cursor.execute("""
                            UPDATE mh_clinic_notes 
                            SET other_attendees = %s
                            WHERE note_id = %s
                        """, [other_attendees, note.note_id])
                
                messages.success(request, "Clinic note was successfully added.")
                return redirect('clinic_detail', clinic_id=clinic_id)
            except Exception as e:
                print(f"Error saving note: {e}")
                messages.error(request, f"Error saving note: {e}")
    else:
        # Pre-populate form with initial values
        initial_data = {
            'date_of_entry': timezone.now(),
            'author_of_entry': get_safe_user_name(request.user),
        }
        form = ClinicNoteForm(initial=initial_data, clinic=clinic)

    context = {
        'form': form,
        'clinic': clinic,
        'clinic_physicians': clinic_physicians,
    }
    return render(request, 'clinics/add_clinic_note.html', context)


def edit_clinic_note(request, note_id):
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT note_id, clinic_id, clinic_name, date_of_entry, 
                   author_of_entry, note, type_of_entry, 
                   date_modified, date_created, other_attendees
            FROM mh_clinic_notes
            WHERE note_id = %s
        """, [note_id])
        
        columns = [col[0] for col in cursor.description]
        result = cursor.fetchone()
        if not result:
            raise Http404("Clinic note does not exist")
            
        note_data = dict(zip(columns, result))
        clinic = get_object_or_404(Clinic, clinic_id=note_data['clinic_id'])
        
        # Create the note instance with only model fields
        note_instance = ClinicNote(
            note_id=note_data['note_id'],
            clinic=clinic,
            clinic_name=note_data['clinic_name'],
            date_of_entry=note_data['date_of_entry'],
            author_of_entry=note_data['author_of_entry'],
            note=note_data['note'],
            type_of_entry=note_data['type_of_entry']
        )
        
        # Add other_attendees as a separate attribute if it exists in the database
        if 'other_attendees' in note_data:
            note_instance.other_attendees = note_data['other_attendees']
        
        # Fetch physician attendance IDs for initial form data
        cursor.execute("""
            SELECT physician_id FROM mh_clinic_note_physician_attendance
            WHERE note_ID = %s
        """, [note_id])
        physician_ids = [row[0] for row in cursor.fetchall()]
    
    if request.method == 'POST':
        if 'delete' in request.POST:
            with connection.cursor() as cursor:
                cursor.execute("DELETE FROM mh_clinic_note_physician_attendance WHERE note_ID = %s", [note_id])
                cursor.execute("DELETE FROM mh_clinic_notes WHERE note_id = %s", [note_id])
            messages.success(request, "Mapping deleted successfully.")
            return redirect('clinic_detail', clinic_id=note_data['clinic_id'])
        else:
            form = ClinicNoteForm(request.POST, instance=note_instance, clinic=clinic)
            if form.is_valid():
                try:
                    cleaned_data = form.cleaned_data
                    date_of_entry = cleaned_data.get('date_of_entry')
                    
                    # Handle date formatting for SQL Server
                    if date_of_entry:
                        if isinstance(date_of_entry, datetime):
                            date_str = date_of_entry.strftime('%Y-%m-%d %H:%M:%S')
                        elif isinstance(date_of_entry, date):
                            date_str = datetime.combine(date_of_entry, datetime.min.time()).strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            try:
                                dt_obj = datetime.strptime(str(date_of_entry).split('.')[0], '%Y-%m-%d %H:%M:%S')
                                date_str = dt_obj.strftime('%Y-%m-%d %H:%M:%S')
                            except ValueError:
                                date_str = str(date_of_entry)
                    else:
                        date_str = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    with connection.cursor() as cursor:
                        cursor.execute("""
                            UPDATE mh_clinic_notes 
                            SET date_of_entry = %s, author_of_entry = %s, 
                                type_of_entry = %s, note = %s, date_modified = GETDATE(),
                                other_attendees = %s
                            WHERE note_id = %s
                        """, [
                            date_str,
                            cleaned_data.get('author_of_entry'),
                            cleaned_data.get('type_of_entry'),
                            cleaned_data.get('note'),
                            cleaned_data.get('other_attendees', ''),
                            note_id
                        ])
                        
                        # Clear existing physician attendance
                        cursor.execute("DELETE FROM mh_clinic_note_physician_attendance WHERE note_ID = %s", [note_id])
                        
                        # Add new physician attendance
                        selected_physicians = cleaned_data.get('physicians_in_attendance', [])
                        for physician_obj in selected_physicians:
                            cursor.execute("""
                                INSERT INTO mh_clinic_note_physician_attendance
                                (note_ID, physician_id, date_created)
                                VALUES (%s, %s, GETDATE())
                            """, [note_id, physician_obj.physician_id])
                    
                    messages.success(request, "Clinic note was successfully updated.")
                    return redirect('clinic_detail', clinic_id=note_data['clinic_id'])
                except Exception as e:
                    print(f"Error updating note: {e}")
                    messages.error(request, f"Error updating note: {e}")
            else:
                print("Form errors:", form.errors)

    else: # GET request - prepare form with initial data
        initial_form_data = {
            'date_of_entry': note_data['date_of_entry'],
            'author_of_entry': note_data['author_of_entry'],
            'type_of_entry': note_data['type_of_entry'],
            'note': note_data['note'],
            'physicians_in_attendance': physician_ids,  # This should pre-select the checkboxes
            'other_attendees': note_data.get('other_attendees', '')
        }
        form = ClinicNoteForm(initial=initial_form_data, instance=note_instance, clinic=clinic)
        
    context = {
        'form': form,
        'note': note_data,
        'clinic': clinic,
    }
    return render(request, 'clinics/edit_clinic_note.html', context)

# Helper classes for raw SQL fallback in clinic_detail
class _TempPhysician:
    def __init__(self, physician_id, first_name, last_name):
        self.physician_id = physician_id
        self.first_name = first_name
        self.last_name = last_name

class _TempAttendance:
    def __init__(self, physician):
        self.physician = physician

class _TempRelatedManagerMock:
    def __init__(self, items_list):
        self._items = items_list
    def all(self):
        return self._items

class _TempNoteObject:
    def __init__(self, data_dict, physician_attendances_list):
        self.__dict__.update(data_dict) 
        self.physician_attendance = _TempRelatedManagerMock(physician_attendances_list)
        # Ensure note_id is consistently available, mapping from note_ID if that's the column name
        self.note_id = data_dict.get('note_ID') or data_dict.get('note_id')
        self.other_attendees = data_dict.get('other_attendees', '')
        # Ensure all fields accessed by the template are present
        self.date_of_entry = data_dict.get('date_of_entry')
        self.author_of_entry = data_dict.get('author_of_entry')
        self.type_of_entry = data_dict.get('type_of_entry')
        self.note = data_dict.get('note')

def physician_panel_import_view(request):
    # This view only shows the upload form
    return render(request, 'physicians/physician_panel_import.html')

def process_physician_panel_upload(request):
    print(f"process_physician_panel_upload called with method: {request.method}")
    
    if request.method == 'POST':
        excel_file = request.FILES.get('panel_file')
        report_month = request.POST.get('report_month')
        report_year = request.POST.get('report_year')
        
        print(f"Excel file received: {excel_file}")
        print(f"Report month: {report_month}, Report year: {report_year}")
        print(f"All POST data: {request.POST}")
        
        if not excel_file:
            messages.error(request, "No file was uploaded.")
            print("No file uploaded - redirecting")
            return redirect('physician_panel_import')

        if not report_month or not report_year:
            messages.error(request, "Please select both month and year for the report.")
            print("Missing month or year - redirecting")
            return redirect('physician_panel_import')
        
        # Validate year range
        try:
            year_int = int(report_year)
            if year_int < 2020 or year_int > 2030:
                messages.error(request, "Please enter a year between 2020 and 2030.")
                print(f"Year out of range: {year_int}")
                return redirect('physician_panel_import')
        except ValueError:
            messages.error(request, "Please enter a valid year.")
            print(f"Invalid year format: {report_year}")
            return redirect('physician_panel_import')

        if not excel_file.name.endswith(('.xls', '.xlsx')):
            messages.error(request, "Invalid file format. Please upload an Excel file (.xls or .xlsx).")
            print(f"Invalid file format: {excel_file.name}")
            return redirect('physician_panel_import')

        try:
            print(f"Processing file: {excel_file.name}")
            df = pd.read_excel(excel_file)
            print(f"DataFrame columns: {df.columns.tolist()}")
            print(f"DataFrame shape: {df.shape}")
            
            required_columns = ['Last_Name', 'First_Name', 'Total_Panel']
            if not all(col in df.columns for col in required_columns):
                messages.error(request, f"Excel file must contain the columns: {', '.join(required_columns)} in the first sheet.")
                print(f"Missing required columns. Found: {df.columns.tolist()}")
                return redirect('physician_panel_import')

            # Use the selected month and year from the form
            selected_month = int(report_month)
            selected_year = int(report_year)
            
            data_to_insert = []
            for index, row in df.iterrows():
                # Ensure Total_Panel is treated as int, handle potential NaN or non-numeric
                try:
                    total_panel_size = int(row['Total_Panel']) if pd.notna(row['Total_Panel']) else None
                except (ValueError, TypeError):
                    total_panel_size = None

                # Ensure string values are properly cleaned
                last_name = str(row['Last_Name']).strip() if pd.notna(row['Last_Name']) else None
                first_name = str(row['First_Name']).strip() if pd.notna(row['First_Name']) else None

                data_to_insert.append((
                    last_name,            # Maps to Phys_Last_Name
                    first_name,           # Maps to Phys_First_Name
                    total_panel_size,     # Maps to Total_Panel_Size
                    selected_month,       # Use selected month
                    selected_year         # Use selected year
                ))

            print(f"Data to insert: {len(data_to_insert)} records for {selected_month}/{selected_year}")
            
            with connection.cursor() as cursor:
                print("Clearing staging table...")
                # Clear the staging table
                cursor.execute("DELETE FROM AH_staging_physician_panel")
                
                print("Inserting new data...")
                # Insert data one by one for better error handling
                insert_query = """
                    INSERT INTO AH_staging_physician_panel 
                    (Phys_Last_Name, Phys_First_Name, Total_Panel_Size, report_month, report_year) 
                    VALUES (%s, %s, %s, %s, %s)
                """
                
                successful_inserts = 0
                for i, record in enumerate(data_to_insert):
                    try:
                        cursor.execute(insert_query, record)
                        successful_inserts += 1
                    except Exception as insert_error:
                        print(f"Error inserting record {i+1}: {record} - Error: {insert_error}")
                        # Continue with other records
                
                print(f"Successfully inserted {successful_inserts} out of {len(data_to_insert)} records")

                print("Fetching preview data...")
                # Fetch data for preview
                cursor.execute("""
                    SELECT Phys_Last_Name, Phys_First_Name, Total_Panel_Size, report_month, report_year, 
                           Full_Name_Concat, name_update_status, physician_id 
                    FROM AH_staging_physician_panel
                    ORDER BY Phys_Last_Name, Phys_First_Name
                """)
                preview_data = cursor.fetchall()
                preview_columns = [col[0] for col in cursor.description]

            print(f"Preview data: {len(preview_data)} records fetched")
            print("About to render preview template...")
            
            return render(request, 'physicians/physician_panel_preview.html', {
                'preview_data': preview_data,
                'preview_columns': preview_columns,
                'upload_success': True,
                'file_name': excel_file.name,
                'total_records': successful_inserts,
                'total_attempted': len(data_to_insert),
                'report_month': selected_month,
                'report_year': selected_year
            })

        except Exception as e:
            print(f"Exception during processing: {str(e)}")
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")
            messages.error(request, f"An error occurred during file processing: {str(e)}")
            return redirect('physician_panel_import')
    
    print("Not a POST request - redirecting")
    # If not POST, redirect back to the upload form
    return redirect('physician_panel_import')

def process_staged_panel_data_view(request):
    if request.method == 'POST':
        try:
            with connection.cursor() as cursor:
                print("Starting staged data processing...")
                
                # Step 1: Concatenate last and first names
                print("Step 1: Concatenating physician names...")
                cursor.execute("""
                    UPDATE AH_staging_physician_panel
                    SET Full_Name_Concat = CONCAT(Phys_Last_Name, ', ', Phys_First_Name)
                    WHERE Full_Name_Concat IS NULL OR Full_Name_Concat = ''
                """)
                rows_updated = cursor.rowcount
                print(f"Updated {rows_updated} rows with concatenated names")
                
                # Step 2: Match physicians using first_name and last_name from mh_physicians
                # Improved matching with better string normalization
                print("Step 2: Matching physicians by first and last name...")
                cursor.execute("""
                    UPDATE staging
                    SET staging.physician_id = p.physician_id,
                        staging.name_update_status = 'Matched by Name'
                    FROM AH_staging_physician_panel staging
                    JOIN mh_physicians p
                    ON UPPER(LTRIM(RTRIM(REPLACE(staging.Phys_Last_Name, '  ', ' ')))) = UPPER(LTRIM(RTRIM(REPLACE(p.last_name, '  ', ' '))))
                    AND UPPER(LTRIM(RTRIM(REPLACE(staging.Phys_First_Name, '  ', ' ')))) = UPPER(LTRIM(RTRIM(REPLACE(p.first_name, '  ', ' '))))
                    WHERE staging.physician_id IS NULL
                """)
                direct_matches = cursor.rowcount
                print(f"Matched {direct_matches} physicians by first and last name")
                
                # Step 3: Try to match using the mapping table for any remaining unmatched
                # Improved mapping table matching with better string normalization
                print("Step 3: Checking mapping table for remaining unmatched...")
                cursor.execute("""
                    UPDATE staging
                    SET staging.physician_id = p.physician_id,
                        staging.name_update_status = 'Matched via Mapping Table',
                        staging.Full_Name_Concat = m.EOPCN_full_name
                    FROM AH_staging_physician_panel staging
                    JOIN AH_physician_name_mapping m
                    ON UPPER(LTRIM(RTRIM(REPLACE(staging.Full_Name_Concat, '  ', ' ')))) = UPPER(LTRIM(RTRIM(REPLACE(m.AH_Full_Name, '  ', ' '))))
                    JOIN mh_physicians p
                    ON UPPER(LTRIM(RTRIM(REPLACE(m.EOPCN_full_name, '  ', ' ')))) = UPPER(LTRIM(RTRIM(REPLACE(p.physician_name, '  ', ' '))))
                    WHERE staging.physician_id IS NULL
                """)
                mapping_matches = cursor.rowcount
                print(f"Matched {mapping_matches} physicians via mapping table")
                
                # Step 4: Additional fallback matching - try partial matches for names with spaces
                print("Step 4: Attempting fuzzy matching for remaining unmatched...")
                cursor.execute("""
                    UPDATE staging
                    SET staging.physician_id = p.physician_id,
                        staging.name_update_status = 'Matched by Fuzzy Logic'
                    FROM AH_staging_physician_panel staging
                    JOIN mh_physicians p
                    ON (
                        -- Try matching by removing all spaces and comparing
                        UPPER(REPLACE(REPLACE(REPLACE(staging.Full_Name_Concat, ' ', ''), ',', ''), '.', '')) = 
                        UPPER(REPLACE(REPLACE(REPLACE(p.physician_name, ' ', ''), ',', ''), '.', ''))
                        OR
                        -- Try matching physician_name against Full_Name_Concat directly
                        UPPER(LTRIM(RTRIM(REPLACE(staging.Full_Name_Concat, '  ', ' ')))) = UPPER(LTRIM(RTRIM(REPLACE(p.physician_name, '  ', ' '))))
                    )
                    WHERE staging.physician_id IS NULL
                """)
                fuzzy_matches = cursor.rowcount
                print(f"Matched {fuzzy_matches} physicians via fuzzy matching")
                
                # Step 5: Check for unmatched physicians
                print("Step 5: Checking for unmatched physicians...")
                cursor.execute("""
                    SELECT Full_Name_Concat, Phys_Last_Name, Phys_First_Name, name_update_status
                    FROM AH_staging_physician_panel
                    WHERE physician_id IS NULL
                    ORDER BY Phys_Last_Name, Phys_First_Name
                """)
                unmatched_physicians = cursor.fetchall()
                unmatched_count = len(unmatched_physicians)
                print(f"Found {unmatched_count} unmatched physicians")
                
                # Debug: Print unmatched physicians for troubleshooting
                if unmatched_count > 0:
                    print("Unmatched physicians:")
                    for up in unmatched_physicians:
                        print(f"  - {up[0]} (Last: '{up[1]}', First: '{up[2]}')")
                
                # Get all physicians for the dropdown - handle physician_active field properly
                print("Getting all physicians for dropdown...")
                cursor.execute("""
                    SELECT physician_id, physician_name, first_name, last_name
                    FROM mh_physicians
                    WHERE physician_active = 'Yes' OR physician_active IS NULL
                    ORDER BY last_name, first_name
                """)
                all_physicians = cursor.fetchall()
                print(f"Found {len(all_physicians)} active physicians")
                
                # Convert to list of dictionaries for easier template access
                all_physicians_list = []
                for physician in all_physicians:
                    all_physicians_list.append({
                        'physician_id': physician[0],
                        'physician_name': physician[1],
                        'first_name': physician[2],
                        'last_name': physician[3]
                    })
                
                # Get summary statistics
                cursor.execute("SELECT COUNT(*) FROM AH_staging_physician_panel")
                total_records = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM AH_staging_physician_panel WHERE physician_id IS NOT NULL")
                total_matched_records = cursor.fetchone()[0]
                
                # Get breakdown of match types
                cursor.execute("""
                    SELECT name_update_status, COUNT(*) as count
                    FROM AH_staging_physician_panel
                    WHERE physician_id IS NOT NULL
                    GROUP BY name_update_status
                """)
                match_breakdown_raw = cursor.fetchall()
                
                # Calculate specific match counts from the breakdown
                direct_matches_count = 0
                mapping_matches_count = 0
                fuzzy_matches_count = 0
                manual_matches_count = 0
                
                for mb in match_breakdown_raw:
                    status = mb[0] or ''
                    count = mb[1]
                    if 'Matched by Name' in status:
                        direct_matches_count += count
                    elif 'Mapping' in status:
                        mapping_matches_count += count
                    elif 'Fuzzy' in status:
                        fuzzy_matches_count += count
                    elif 'Manual' in status:
                        manual_matches_count += count
                
                # Prepare context for the results page
                context = {
                    'processing_success': True,
                    'total_records': total_records,
                    'matched_records': direct_matches_count,  # Show only direct matches from EOPCN database
                    'unmatched_count': unmatched_count,
                    'mapping_updates': mapping_matches_count,  # Show mapping matches separately
                    'fuzzy_matches': fuzzy_matches_count,  # Add fuzzy matches count
                    'all_physicians': all_physicians_list
                }
                
                # Add detailed breakdown if there are unmatched physicians
                if unmatched_count > 0:
                    unmatched_list = []
                    for up in unmatched_physicians:
                        unmatched_list.append({
                            'full_name_concat': up[0],
                            'last_name': up[1], 
                            'first_name': up[2],
                            'status': up[3] or 'No Status'
                        })
                    context['unmatched_physicians'] = unmatched_list
                
                # Add match breakdown
                if match_breakdown_raw:
                    match_breakdown_list = []
                    for mb in match_breakdown_raw:
                        match_breakdown_list.append({
                            'status': mb[0] or 'Direct Match',
                            'count': mb[1]
                        })
                    context['match_breakdown'] = match_breakdown_list
                
                messages.success(request, f"Processing completed! {transferred_count} out of {total_records} physicians matched successfully.")
                
                return render(request, 'physicians/physician_panel_processing_results.html', context)
                
        except Exception as e:
            print(f"Error in process_staged_panel_data_view: {str(e)}")
            messages.error(request, f"An error occurred during data processing: {str(e)}")
            return redirect('physician_panel_import')
    
    # If GET request, redirect to import page
    return redirect('physician_panel_import')

def manual_physician_match(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            last_name = data.get('last_name')
            first_name = data.get('first_name')
            physician_id = data.get('physician_id')
            
            with connection.cursor() as cursor:
                # Update the staging table with the manual match
                cursor.execute("""
                    UPDATE AH_staging_physician_panel
                    SET physician_id = %s,
                        name_update_status = 'Manually Matched'
                    WHERE Phys_Last_Name = %s AND Phys_First_Name = %s
                """, [physician_id, last_name, first_name])
                
                # Get the physician name for the Full_Name_Concat
                cursor.execute("""
                    SELECT physician_name FROM mh_physicians WHERE physician_id = %s
                """, [physician_id])
                physician_name = cursor.fetchone()
                
                if physician_name:
                    cursor.execute("""
                        UPDATE AH_staging_physician_panel
                        SET Full_Name_Concat = %s
                        WHERE Phys_Last_Name = %s AND Phys_First_Name = %s
                    """, [physician_name[0], last_name, first_name])
            
            return JsonResponse({'success': True})
            
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    
    return JsonResponse({'success': False, 'error': 'Invalid request method'})

def get_match_statistics(request):
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM AH_staging_physician_panel WHERE physician_id IS NOT NULL")
            matched_records = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM AH_staging_physician_panel WHERE physician_id IS NULL")
            unmatched_count = cursor.fetchone()[0]
        
        return JsonResponse({
            'matched_records': matched_records,
            'unmatched_count': unmatched_count
        })
    except Exception as e:
        return JsonResponse({'error': str(e)})

def transfer_to_master_table(request):
    if request.method == 'POST':
        try:
            transfer_matched_only = request.POST.get('transfer_matched_only', False)
            
            with connection.cursor() as cursor:
                # Transfer data to master table (only matched records if specified)
                if transfer_matched_only:
                    cursor.execute("""
                        INSERT INTO AH_physician_fourcut_panel_MASTER 
                        (physician_id, physician_name, panel_size, report_month, report_year, date_created)
                        SELECT physician_id, Full_Name_Concat, Total_Panel_Size, report_month, report_year, GETDATE()
                        FROM AH_staging_physician_panel
                        WHERE physician_id IS NOT NULL
                    """)
                else:
                    cursor.execute("""
                        INSERT INTO AH_physician_fourcut_panel_MASTER 
                        (physician_id, physician_name, panel_size, report_month, report_year, date_created)
                        SELECT physician_id, Full_Name_Concat, Total_Panel_Size, report_month, report_year, GETDATE()
                        FROM AH_staging_physician_panel
                    """)
                
                transferred_count = cursor.rowcount
                
                # Get report period for the message
                cursor.execute("""
                    SELECT DISTINCT report_month, report_year 
                    FROM AH_staging_physician_panel 
                """)
                period = cursor.fetchone()
                report_period = f"{period[0]}/{period[1]}" if period else "Unknown"
            
            messages.success(request, 
                f"✅ Transfer Successful! {transferred_count} physician panel records "
                f"for {report_period} have been successfully transferred to the master table.")
            
            return redirect('physician_panel_details')
            
        except Exception as e:
            messages.error(request, f"Error transferring data: {str(e)}")
            return redirect('physician_panel_import')
    
    return redirect('physician_panel_import')

def physician_name_mapping_list(request):
    """View to list all physician name mappings"""
    try:
        # Use raw SQL to get the mappings since it's an unmanaged model
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT physician_id, AH_Full_Name, EOPCN_full_name, date_created, date_modified,
                       ROW_NUMBER() OVER (ORDER BY AH_Full_Name, EOPCN_full_name) as row_id
                FROM AH_physician_name_mapping
                ORDER BY date_created ASC
            """)
            mappings_raw = cursor.fetchall()
            
        # Convert to list of dictionaries for easier template access
        mappings = []
        for mapping in mappings_raw:
            mappings.append({
                'row_id': mapping[5],  # Use the ROW_NUMBER from SQL
                'physician_id': mapping[0],
                'ah_full_name': mapping[1],
                'eopcn_full_name': mapping[2],
                'date_created': mapping[3],
                'date_modified': mapping[4],
            })
            
        context = {
            'mappings': mappings,
        }
        return render(request, 'physicians/physician_name_mapping_list.html', context)
        
    except Exception as e:
        messages.error(request, f"Error loading mapping data: {str(e)}")
        return redirect('physician_panel_import')

def add_physician_name_mapping(request):
    """View to add a new physician name mapping"""
    if request.method == 'POST':
        form = PhysicianNameMappingForm(request.POST)
        if form.is_valid():
            try:
                physician_id = form.cleaned_data.get('physician_id')
                ah_full_name = form.cleaned_data['ah_full_name']
                eopcn_full_name = form.cleaned_data['eopcn_full_name']
                
                with connection.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO AH_physician_name_mapping 
                        (physician_id, AH_Full_Name, EOPCN_full_name)
                        VALUES (%s, %s, %s)
                    """, [physician_id if physician_id else None, ah_full_name, eopcn_full_name])
                    
                messages.success(request, f"Mapping added successfully: '{ah_full_name}' → '{eopcn_full_name}'")
                return redirect('physician_name_mapping_list')
                
            except Exception as e:
                messages.error(request, f"Error adding mapping: {str(e)}")
    else:
        form = PhysicianNameMappingForm()
        
    return render(request, 'physicians/add_physician_name_mapping.html', {'form': form})

def edit_physician_name_mapping(request, mapping_id):
    """View to edit an existing physician name mapping"""
    try:
        # Get the mapping by row number using the same ordering as the list view
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT physician_id, AH_Full_Name, EOPCN_full_name, date_created, date_modified,
                       ROW_NUMBER() OVER (ORDER BY AH_Full_Name, EOPCN_full_name) as row_id
                FROM AH_physician_name_mapping
            """)
            all_mappings = cursor.fetchall()
            
            # Find the mapping with the matching row ID
            mapping = None
            for m in all_mappings:
                if m[5] == mapping_id:  # row_id matches (now at index 5)
                    mapping = m
                    break
            
            if not mapping:
                messages.error(request, "Mapping not found.")
                return redirect('physician_name_mapping_list')
                
        if request.method == 'POST':
            if 'delete' in request.POST:
                # Delete the mapping
                try:
                    with connection.cursor() as cursor:
                        cursor.execute("""
                            DELETE FROM AH_physician_name_mapping 
                            WHERE AH_Full_Name = %s AND EOPCN_full_name = %s
                        """, [mapping[1], mapping[2]])
                    messages.success(request, "Mapping deleted successfully.")
                    return redirect('physician_name_mapping_list')
                except Exception as e:
                    messages.error(request, f"Error deleting mapping: {str(e)}")
            else:
                # Update the mapping
                form = PhysicianNameMappingForm(request.POST)
                if form.is_valid():
                    try:
                        new_physician_id = form.cleaned_data.get('physician_id')
                        new_ah_full_name = form.cleaned_data['ah_full_name']
                        new_eopcn_full_name = form.cleaned_data['eopcn_full_name']
                        
                        with connection.cursor() as cursor:
                            # Update the mapping using AH_Full_Name and EOPCN_full_name as identifiers
                            cursor.execute("""
                                UPDATE AH_physician_name_mapping 
                                SET physician_id = %s, AH_Full_Name = %s, EOPCN_full_name = %s, date_modified = GETDATE()
                                WHERE AH_Full_Name = %s AND EOPCN_full_name = %s
                            """, [
                                new_physician_id if new_physician_id else None, 
                                new_ah_full_name, 
                                new_eopcn_full_name,
                                mapping[1],  # original AH_Full_Name
                                mapping[2]   # original EOPCN_full_name
                            ])
                        
                        messages.success(request, "Mapping updated successfully.")
                        return redirect('physician_name_mapping_list')
                    except Exception as e:
                        messages.error(request, f"Error updating mapping: {str(e)}")
        else:
            # Pre-populate form with existing data
            initial_data = {
                'physician_id': mapping[0] if mapping[0] else '',
                'ah_full_name': mapping[1],
                'eopcn_full_name': mapping[2],
            }
            form = PhysicianNameMappingForm(initial=initial_data)
        
        return render(request, 'physicians/edit_physician_name_mapping.html', {
            'form': form,
            'mapping_id': mapping_id,
            'original_mapping': {
                'physician_id': mapping[0],
                'ah_full_name': mapping[1],
                'eopcn_full_name': mapping[2],
                'date_created': mapping[3],
                'date_modified': mapping[4],
            }
        })
        
    except Exception as e:
        messages.error(request, f"Error loading mapping: {str(e)}")
        return redirect('physician_name_mapping_list')

def debug_physician_matching(request):
    """Debug view to help troubleshoot physician matching issues"""
    if request.method == 'GET':
        physician_name = request.GET.get('name', 'Seal Grant, Alexandra')
        
        try:
            with connection.cursor() as cursor:
                # Check staging table for this physician
                cursor.execute("""
                    SELECT Phys_Last_Name, Phys_First_Name, Full_Name_Concat, physician_id, name_update_status
                    FROM AH_staging_physician_panel
                    WHERE Full_Name_Concat LIKE %s OR Phys_Last_Name LIKE %s
                """, [f'%{physician_name}%', f'%{physician_name.split(",")[0].strip()}%'])
                staging_results = cursor.fetchall()
                
                # Check main physician table for potential matches
                cursor.execute("""
                    SELECT physician_id, physician_name, first_name, last_name
                    FROM mh_physicians
                    WHERE physician_name LIKE %s OR last_name LIKE %s OR first_name LIKE %s
                """, [f'%{physician_name}%', f'%{physician_name.split(",")[0].strip()}%', f'%{physician_name.split(",")[1].strip() if "," in physician_name else physician_name}%'])
                physician_results = cursor.fetchall()
                
                # Check mapping table
                cursor.execute("""
                    SELECT AH_Full_Name, EOPCN_full_name, physician_id
                    FROM AH_physician_name_mapping
                    WHERE AH_Full_Name LIKE %s OR EOPCN_full_name LIKE %s
                """, [f'%{physician_name}%', f'%{physician_name}%'])
                mapping_results = cursor.fetchall()
                
                debug_info = {
                    'search_name': physician_name,
                    'staging_results': staging_results,
                    'physician_results': physician_results,
                    'mapping_results': mapping_results,
                }
                
                return render(request, 'physicians/debug_physician_matching.html', debug_info)
                
        except Exception as e:
            messages.error(request, f"Debug error: {str(e)}")
            return redirect('physician_panel_import')
    
    return redirect('physician_panel_import')

def email_groups_list(request):
    email_groups = EmailGroup.objects.all().order_by('name')
    return render(request, 'staff/email_groups_list.html', {'email_groups': email_groups})


def add_email_group(request):
    if request.method == 'POST':
        form = EmailGroupForm(request.POST)
        if form.is_valid():
            email_group = form.save(commit=False)
            email_group.created_by = get_safe_user_name(request.user)
            email_group.save()
            messages.success(request, f'Email group "{email_group.name}" added successfully.')
            return redirect('email_groups_list')
    else:
        form = EmailGroupForm()
    
    return render(request, 'staff/add_email_group.html', {'form': form})


def edit_email_group(request, group_id):
    email_group = get_object_or_404(EmailGroup, group_id=group_id)
    
    if request.method == 'POST':
        form = EmailGroupForm(request.POST, instance=email_group)
        if form.is_valid():
            email_group = form.save(commit=False)
            email_group.modified_by = get_safe_user_name(request.user)
            email_group.date_modified = timezone.now()
            email_group.save()
            messages.success(request, f'Email group "{email_group.name}" updated successfully.')
            return redirect('email_groups_list')
    else:
        form = EmailGroupForm(instance=email_group)
    
    return render(request, 'staff/edit_email_group.html', {'form': form, 'email_group': email_group})


def email_group_detail(request, group_id):
    email_group = get_object_or_404(EmailGroup, group_id=group_id)
    recipients = email_group.get_active_recipients()
    
    # Handle adding new members
    if request.method == 'POST':
        if 'add_member' in request.POST:
            membership_form = EmailGroupMembershipForm(request.POST, group=email_group)
            if membership_form.is_valid():
                membership = membership_form.save(commit=False)
                membership.group = email_group
                membership.added_by = get_safe_user_name(request.user)
                membership.save()
                messages.success(request, f'{membership.recipient.name} added to group.')
                return redirect('email_group_detail', group_id=group_id)
        elif 'remove_member' in request.POST:
            recipient_id = request.POST.get('recipient_id')
            try:
                membership = EmailGroupMembership.objects.get(group=email_group, recipient_id=recipient_id)
                recipient_name = membership.recipient.name
                membership.delete()
                messages.success(request, f'{recipient_name} removed from group.')
                return redirect('email_group_detail', group_id=group_id)
            except EmailGroupMembership.DoesNotExist:
                messages.error(request, 'Membership not found.')
    
    membership_form = EmailGroupMembershipForm(group=email_group)
    
    return render(request, 'staff/email_group_detail.html', {
        'email_group': email_group,
        'recipients': recipients,
        'membership_form': membership_form
    })


def email_recipients_list(request):
    recipients = EmailRecipient.objects.all().order_by('name')
    return render(request, 'staff/email_recipients_list.html', {'recipients': recipients})


def add_email_recipient(request):
    if request.method == 'POST':
        form = EmailRecipientForm(request.POST)
        if form.is_valid():
            recipient = form.save(commit=False)
            recipient.created_by = get_safe_user_name(request.user)
            recipient.save()
            messages.success(request, f'Email recipient "{recipient.name}" added successfully.')
            return redirect('email_recipients_list')
    else:
        form = EmailRecipientForm()
    
    return render(request, 'staff/add_email_recipient.html', {'form': form})


def edit_email_recipient(request, recipient_id):
    recipient = get_object_or_404(EmailRecipient, recipient_id=recipient_id)
    
    if request.method == 'POST':
        form = EmailRecipientForm(request.POST, instance=recipient)
        if form.is_valid():
            recipient = form.save(commit=False)
            recipient.modified_by = get_safe_user_name(request.user)
            recipient.date_modified = timezone.now()
            recipient.save()
            messages.success(request, f'Email recipient "{recipient.name}" updated successfully.')
            return redirect('email_recipients_list')
    else:
        form = EmailRecipientForm(instance=recipient)
    
    return render(request, 'staff/edit_email_recipient.html', {'form': form, 'recipient': recipient})


def get_email_group_info(request, group_id):
    """
    AJAX view to get email group information including members
    """
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        try:
            email_group = get_object_or_404(EmailGroup, group_id=group_id, is_active=True)
            recipients = email_group.get_active_recipients()
            
            data = {
                'name': email_group.name,
                'description': email_group.description or 'No description provided',
                'member_count': recipients.count(),
                'members': [
                    {
                        'name': recipient.name,
                        'email': recipient.email
                    }
                    for recipient in recipients[:10]  # Limit to first 10 members for performance
                ],
                'has_more_members': recipients.count() > 10,
                'manage_url': reverse('email_group_detail', args=[group_id])
            }
            
            return JsonResponse(data)
        except EmailGroup.DoesNotExist:
            return JsonResponse({'error': 'Email group not found'}, status=404)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    return JsonResponse({'error': 'Invalid request'}, status=400)


def get_email_list_for_template(template_name, custom_group=None):
    """
    Get the appropriate email list based on template name or custom group
    This replaces the hardcoded email_group_1 and email_group_2 logic
    """
    if custom_group:
        return custom_group.get_active_emails()
    
    # Default group mappings based on template
    try:
        if template_name in [
            'physicians/emails/physician_update_notification.html',
            'physicians/emails/physician_clinic_update_notification.html',
        ]:
            # Try to get the default group for physician updates
            default_group = EmailGroup.objects.filter(name__icontains='physician', is_active=True).first()
            if default_group:
                return default_group.get_active_emails()
        
        # Default to general leadership group
        leadership_group = EmailGroup.objects.filter(name__icontains='leadership', is_active=True).first()
        if leadership_group:
            return leadership_group.get_active_emails()
        
        # Fallback to hardcoded lists if no groups found
        if template_name in [
            'physicians/emails/physician_update_notification.html',
            'physicians/emails/physician_clinic_update_notification.html',
        ]:
            return email_group_2
        else:
            return email_group_1
            
    except Exception:
        # Fallback to hardcoded lists if database error
        if template_name in [
            'physicians/emails/physician_update_notification.html',
            'physicians/emails/physician_clinic_update_notification.html',
        ]:
            return email_group_2
        else:
            return email_group_1