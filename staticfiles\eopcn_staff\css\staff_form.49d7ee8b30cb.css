.form-wrapper {
    display: flex;
    flex-wrap: wrap; /* Allows containers to wrap to the next line if necessary */
    gap: 20px; /* Adds space between the containers */
}

.form-container {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 8px;
    background-color: #f9f9f9;
    margin-bottom: 15px;
}

.button-container {
    flex: 1;
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    border-radius: 8px;
    margin-top: 0;
    margin-bottom: 20px;
}

.save-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 8px;
    background-color: #f9f9f9;
    margin-bottom: 15px;
    box-sizing: border-box; /* Ensures padding is included in the width */
}

.save-container .form-group {
    display: flex;
    align-items: center;
    margin: 0;
}


.form-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.form-group label {
    width: 200px;
    margin-right: 10px;
    text-align: left;
}

.form-group input,
.form-group select,
.form-group textarea {
    flex: 1;
    padding: 5px;
    max-width: 100%;
}

button {
    width: auto;
    padding: 10px 20px;
}

a.btn-secondary {
    margin-left: 10px;
    padding: 10px 20px;
    text-decoration: none;
    background-color: #6c757d;
    color: white;
    border-radius: 4px;
}

.allocation-form-row {
    margin-bottom: 20px;
}

hr {
    margin: 10px 0;
    border: 0;
    border-top: 1px solid #ccc;
}

.allocation-heading {
    text-align: left;
    font-weight: bold;
    margin: 10px 0;
}

.subheading {
    text-align: center;
    margin-bottom: 10px; /* Consolidated margin-bottom value */
}

.leadership-checkbox label {
    margin-right: 0; /* Decrease the space between label and checkbox */
}

.leadership-checkbox input[type="checkbox"] {
    margin-left: 0; /* Ensure no extra margin on the checkbox */
    margin-right: 10px; /* Adjust the spacing around the checkbox */
}

.allocation-header {
    display: flex; /* Enable flexbox */
    justify-content: space-between; /* Space between elements */
    align-items: center; /* Vertically center the items */
    position: relative; /* Helps center the title */
    margin-bottom: 15px;
}

.allocation-title {
    position: absolute; /* Center this container absolutely */
    left: 50%; /* Move to the center horizontally */
    transform: translateX(-50%); /* Exactly center the content */
}

.reset-form {
    margin-left: auto; /* Push the button to the far right */
}

.gap-separator {
    margin-top: 10px; /* Space between the counter and the line */
    margin-bottom: 20px; /* Space after the line before the next content */
}
