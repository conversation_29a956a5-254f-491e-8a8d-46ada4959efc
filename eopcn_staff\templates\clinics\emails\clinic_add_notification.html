<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 15px;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .header {
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 20px;
        }
        .footer {
            background-color: #f1f1f1;
            padding: 10px;
            text-align: center;
            border-radius: 0 0 5px 5px;
            font-size: 12px;
            margin-top: 20px;
        }
        .table-container {
            width: 70%;
            table-layout: fixed;
        }
        .label-cell {
            text-align: right;
            width: 30%;
            padding: 4px;
            line-height: 1.2;
        }
        .value-cell {
            background-color: #DDEBF7;
            width: 70%;
            padding: 4px;
            line-height: 1.2;
        }
        .section-header {
            font-size: 18px;
            margin-top: 10px;
        }
    </style>
</head>
<body>

<h1>New Clinic Information</h1>

<!-- Clinic Information Section -->
<h2 class="section-header">Clinic Details</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell">Clinic Name:</td>
        <td class="value-cell">{{ clinic.clinic_name }}</td>
    </tr>
    <tr>
        <td class="label-cell">Medical Group/Site:</td>
        <td class="value-cell">{{ clinic.med_group_or_site|default_if_none:"" }}</td>
    </tr>
    <tr>
        <td class="label-cell">Address:</td>
        <td class="value-cell">
            {{ clinic.street_address|default_if_none:"" }}<br>
            {% if clinic.floor_unit_room %}{{ clinic.floor_unit_room }}<br>{% endif %}
            {{ clinic.city|default_if_none:"" }}, {{ clinic.province|default_if_none:"" }} {{ clinic.postal_code|default_if_none:"" }}
        </td>
    </tr>
    <tr>
        <td class="label-cell">Phone:</td>
        <td class="value-cell">{{ clinic.business_phone|default_if_none:"" }}{% if clinic.extension %} ext. {{ clinic.extension }}{% endif %}</td>
    </tr>
    <tr>
        <td class="label-cell">Fax:</td>
        <td class="value-cell">{{ clinic.fax|default_if_none:"" }}</td>
    </tr>
    <tr>
        <td class="label-cell">Website:</td>
        <td class="value-cell">{{ clinic.clinic_website|default_if_none:"" }}</td>
    </tr>
    <tr>
        <td class="label-cell">EMR System:</td>
        <td class="value-cell">{{ clinic.clinic_emr|default_if_none:"" }}</td>
    </tr>
    <tr>
        <td class="label-cell">PIA Number:</td>
        <td class="value-cell">{{ clinic.pia_number|default_if_none:"" }}</td>
    </tr>
    <tr>
        <td class="label-cell">Include on EOPCN Website:</td>
        <td class="value-cell">{{ clinic.include_on_eopcn_website|yesno:"Yes,No" }}</td>
    </tr>
</table>

<!-- Primary Contact Section -->
<h2 class="section-header">Primary Contact</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell">Name:</td>
        <td class="value-cell">
            {{ clinic.primary_contact_first_name|default_if_none:"" }} {{ clinic.primary_contact_last_name|default_if_none:"" }}
        </td>
    </tr>
    <tr>
        <td class="label-cell">Role:</td>
        <td class="value-cell">{{ clinic.primary_contact_role|default_if_none:"" }}</td>
    </tr>
    <tr>
        <td class="label-cell">Phone:</td>
        <td class="value-cell">{{ clinic.primary_contact_phone|default_if_none:"" }}{% if clinic.primary_contact_ext %} ext. {{ clinic.primary_contact_ext }}{% endif %}</td>
    </tr>
    <tr>
        <td class="label-cell">Email:</td>
        <td class="value-cell">{{ clinic.primary_contact_email|default_if_none:"" }}</td>
    </tr>
</table>

{% if clinic.alternate_contact_first_name or clinic.alternate_contact_last_name %}
<!-- Alternate Contact Section -->
<h2 class="section-header">Alternate Contact</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell">Name:</td>
        <td class="value-cell">
            {{ clinic.alternate_contact_first_name|default_if_none:"" }} {{ clinic.alternate_contact_last_name|default_if_none:"" }}
        </td>
    </tr>
    <tr>
        <td class="label-cell">Role:</td>
        <td class="value-cell">{{ clinic.alternate_contact_role|default_if_none:"" }}</td>
    </tr>
    <tr>
        <td class="label-cell">Phone:</td>
        <td class="value-cell">{{ clinic.alternate_contact_phone|default_if_none:"" }}{% if clinic.alternate_contact_ext %} ext. {{ clinic.alternate_contact_ext }}{% endif %}</td>
    </tr>
    <tr>
        <td class="label-cell">Email:</td>
        <td class="value-cell">{{ clinic.alternate_contact_email|default_if_none:"" }}</td>
    </tr>
</table>
{% endif %}

{% if comment %}
<!-- Comment Section -->
<h2 class="section-header">Additional Comments</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell">Comment:</td>
        <td class="value-cell">{{ comment }}</td>
    </tr>
</table>
{% endif %}

<p>
    Submitted by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).
</p>
{% if details_url %}
<p>
    <a href="{{ details_url }}">View record in operational database</a>
</p>
{% endif %}

<p>Best regards,</p>
<p>EOPCN Automation Admin</p>

<div style="margin-top: 20px; padding: 10px; background-color: #f1f1f1; text-align: center; border-radius: 0 0 5px 5px; font-size: 12px;">
    <p>This is an automated message from the EOPCN Operational Application.</p>
</div>

</body>
</html>
