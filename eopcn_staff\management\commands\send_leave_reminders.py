from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta, date
import pytz
import logging

from eopcn_staff.models import StaffLeave, LeaveReminder, LeaveReminderType
from eopcn_staff.views import send_email

# Set up logging
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Send reminder emails for staff leaves with pending reminders and create automatic reminders"

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-auto-reminders',
            action='store_true',
            help='Create automatic reminders for leaves without manual reminders',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually sending emails',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']

        if self.dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No emails will be sent'))

        # Process legacy reminders first
        self.process_legacy_reminders()

        # Create automatic reminders if requested
        if options['create_auto_reminders']:
            self.create_automatic_reminders()

        # Process new reminder system
        self.process_new_reminders()

        self.stdout.write(self.style.SUCCESS('Leave reminder processing completed.'))

    def process_legacy_reminders(self):
        """Process the original reminder system for backward compatibility"""
        mst = pytz.timezone('US/Mountain')
        now_mst = timezone.now().astimezone(mst)

        leaves = StaffLeave.objects.filter(
            reminder_datetime__isnull=False,
            reminder_sent=False
        )

        processed_count = 0
        for leave in leaves:
            if leave.is_reminder_due():
                if self.send_legacy_reminder(leave):
                    processed_count += 1

        if processed_count > 0:
            self.stdout.write(f'Processed {processed_count} legacy reminders')

    def send_legacy_reminder(self, leave):
        """Send a legacy reminder email"""
        try:
            staff = leave.staff
            subject = f"Reminder: Verify Return Date for {staff.first_name} {staff.last_name}"
            details_url = self.get_leave_details_url(leave)

            context = {
                'staff_member': staff,
                'staff_leave': leave,
                'details_url': details_url,
                'reminder_type': 'Manual Reminder',
            }

            recipients = leave.get_reminder_recipients()
            if not recipients:
                self.stdout.write(
                    self.style.WARNING(f'No recipients for leave {leave.leave_id}')
                )
                return False

            if not self.dry_run:
                send_email(subject, context, recipients, 'staff/emails/staff_leave_reminder_notification.html')
                leave.reminder_sent = True
                leave.save()

            self.stdout.write(f'Sent legacy reminder for {staff.first_name} {staff.last_name}')
            return True

        except Exception as e:
            logger.error(f"Error sending legacy reminder for leave {leave.leave_id}: {e}")
            self.stdout.write(
                self.style.ERROR(f'Failed to send legacy reminder for leave {leave.leave_id}: {e}')
            )
            return False

    def get_leave_details_url(self, leave):
        """Generate URL for leave details"""
        try:
            from django.urls import reverse
            from django.contrib.sites.models import Site
            current_site = Site.objects.get_current()
            return f"https://{current_site.domain}" + reverse('edit_staff_leave', args=[leave.leave_id])
        except Exception:
            return ''

    def create_automatic_reminders(self):
        """Create automatic reminders for leaves that should have them"""
        from datetime import date, timedelta

        # Get leaves that should have automatic reminders
        leaves_needing_reminders = StaffLeave.objects.filter(
            leave_start_date__gt=date.today(),
            return_date__isnull=False,
            reminder_datetime__isnull=True  # No manual reminder set
        ).exclude(
            reminders__isnull=False  # Don't create if reminders already exist
        )

        created_count = 0
        for leave in leaves_needing_reminders:
            if self.create_reminders_for_leave(leave):
                created_count += 1

        if created_count > 0:
            self.stdout.write(f'Created automatic reminders for {created_count} leaves')

    def create_reminders_for_leave(self, leave):
        """Create standard reminders for a leave"""
        try:
            from datetime import timedelta
            import pytz

            mst = pytz.timezone('US/Mountain')

            # Get or create default reminder types
            reminder_types = self.get_default_reminder_types()

            for reminder_type in reminder_types:
                # Calculate reminder datetime based on leave dates and offset
                if reminder_type.reminder_type == 'before_leave':
                    base_date = leave.leave_start_date
                elif reminder_type.reminder_type == 'before_return':
                    base_date = leave.return_date
                else:
                    continue  # Skip other types for now

                if not base_date:
                    continue

                # Calculate reminder date
                reminder_date = base_date + timedelta(days=reminder_type.days_offset)

                # Set time to 9 AM MST
                reminder_datetime = datetime.combine(reminder_date, datetime.min.time().replace(hour=9))
                reminder_datetime = mst.localize(reminder_datetime).astimezone(pytz.UTC)

                # Create the reminder
                if not self.dry_run:
                    LeaveReminder.objects.get_or_create(
                        staff_leave=leave,
                        reminder_type=reminder_type,
                        defaults={
                            'scheduled_datetime': reminder_datetime,
                            'status': 'pending',
                            'created_by': 'auto_system'
                        }
                    )

            return True

        except Exception as e:
            logger.error(f"Error creating reminders for leave {leave.leave_id}: {e}")
            return False

    def get_default_reminder_types(self):
        """Get or create default reminder types"""
        defaults = [
            {
                'name': 'Leave Starting Soon',
                'reminder_type': 'before_leave',
                'days_offset': -3,  # 3 days before leave starts
                'description': 'Reminder sent 3 days before leave starts'
            },
            {
                'name': 'Return Date Verification',
                'reminder_type': 'before_return',
                'days_offset': -1,  # 1 day before return
                'description': 'Reminder to verify return date'
            }
        ]

        reminder_types = []
        for default in defaults:
            reminder_type, created = LeaveReminderType.objects.get_or_create(
                name=default['name'],
                reminder_type=default['reminder_type'],
                defaults={
                    'days_offset': default['days_offset'],
                    'description': default['description'],
                    'is_active': True
                }
            )
            reminder_types.append(reminder_type)

        return reminder_types

    def process_new_reminders(self):
        """Process reminders from the new reminder system"""
        try:
            due_reminders = LeaveReminder.objects.filter(
                status='pending'
            ).select_related('staff_leave', 'reminder_type')

            sent_count = 0
            for reminder in due_reminders:
                if reminder.is_due():
                    if self.send_new_reminder(reminder):
                        sent_count += 1

            if sent_count > 0:
                self.stdout.write(f'Sent {sent_count} new system reminders')

        except Exception as e:
            # Handle case where new models don't exist yet
            logger.warning(f"New reminder system not available: {e}")

    def send_new_reminder(self, reminder):
        """Send a reminder from the new system"""
        try:
            staff = reminder.staff_leave.staff
            leave = reminder.staff_leave

            # Customize subject based on reminder type
            subject_map = {
                'before_leave': f"Upcoming Leave: {staff.first_name} {staff.last_name}",
                'before_return': f"Return Date Verification: {staff.first_name} {staff.last_name}",
                'during_leave': f"Leave Status Check: {staff.first_name} {staff.last_name}",
                'overdue_return': f"Overdue Return: {staff.first_name} {staff.last_name}",
                'custom': f"Leave Reminder: {staff.first_name} {staff.last_name}",
            }

            subject = subject_map.get(reminder.reminder_type.reminder_type,
                                    f"Leave Reminder: {staff.first_name} {staff.last_name}")

            context = {
                'staff_member': staff,
                'staff_leave': leave,
                'reminder_type': reminder.reminder_type.name,
                'details_url': self.get_leave_details_url(leave),
            }

            recipients = reminder.get_recipients()
            if not recipients:
                self.stdout.write(
                    self.style.WARNING(f'No recipients for reminder {reminder.reminder_id}')
                )
                return False

            if not self.dry_run:
                send_email(subject, context, recipients, 'staff/emails/staff_leave_reminder_notification.html')
                reminder.status = 'sent'
                reminder.sent_datetime = timezone.now()
                reminder.save()

            self.stdout.write(f'Sent {reminder.reminder_type.name} reminder for {staff.first_name} {staff.last_name}')
            return True

        except Exception as e:
            logger.error(f"Error sending reminder {reminder.reminder_id}: {e}")
            if not self.dry_run:
                reminder.status = 'failed'
                reminder.error_message = str(e)
                reminder.save()

            self.stdout.write(
                self.style.ERROR(f'Failed to send reminder {reminder.reminder_id}: {e}')
            )
            return False
