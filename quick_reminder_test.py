#!/usr/bin/env python
"""
Quick test to show current time and help you set up a test reminder
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
sys.path.append('c:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'EOPCNOpApp.settings')
django.setup()

from django.utils import timezone
import pytz

def show_test_instructions():
    """Show current time and test instructions"""
    mst = pytz.timezone('US/Mountain')
    now_mst = timezone.now().astimezone(mst)
    
    # Calculate test times
    test_time_3min = now_mst + timedelta(minutes=3)
    test_time_5min = now_mst + timedelta(minutes=5)
    
    print("🕐 CURRENT TIME INFORMATION")
    print("=" * 50)
    print(f"Current MST time: {now_mst.strftime('%Y-%m-%d %I:%M %p %Z')}")
    print(f"Current UTC time: {timezone.now().strftime('%Y-%m-%d %H:%M %Z')}")
    
    print("\n📝 HOW TO TEST:")
    print("=" * 50)
    print("1. Go to your web app (add/edit staff leave)")
    print("2. Set reminder time to one of these:")
    print(f"   • {test_time_3min.strftime('%Y-%m-%d %I:%M %p')} (3 minutes from now)")
    print(f"   • {test_time_5min.strftime('%Y-%m-%d %I:%M %p')} (5 minutes from now)")
    print("3. Check 'Send reminder to me' or select an email group")
    print("4. Save the form")
    print("5. Wait for the time to pass")
    print("6. Run: python manage.py send_leave_reminders")
    print("7. Check your email!")
    
    print("\n🔄 AUTOMATED TESTING:")
    print("=" * 50)
    print("Instead of step 6, you can run:")
    print("python test_reminder_scheduler.py")
    print("This will check every 2 minutes automatically")
    
    print("\n⚠️  IMPORTANT NOTES:")
    print("=" * 50)
    print("• The system only checks when you run the command")
    print("• Make sure to use a real email address")
    print("• The time you enter is treated as MST time")
    print("• Reminders are sent once the time has passed")
    
    # Show format for datetime-local input
    print("\n📅 DATETIME FORMAT:")
    print("=" * 50)
    print("In the web form, enter time like this:")
    print(f"Date: {test_time_3min.strftime('%Y-%m-%d')}")
    print(f"Time: {test_time_3min.strftime('%H:%M')} (24-hour format)")
    print(f"Or: {test_time_3min.strftime('%I:%M %p')} (12-hour format)")

if __name__ == "__main__":
    show_test_instructions()
