{% extends "base.html" %}
{% block content %}
<h2>Program List</h2>
{% if messages %}
        <script>
            {% for message in messages %}
                alert("{{ message }}");
            {% endfor %}
        </script>
{% endif %}
<a href="{% url 'add_service' %}" class="btn btn-secondary">+ Add New Program</a>

<table id="serviceTable" class="display">
    <thead>
        <tr>
            <th>Program Name</th>
            <th>Division</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for service in services %}
        <tr>
            <td>{{ service.service_name }}</td>
            <td>{{ service.division }}</td>
            <td>
                <a href="{% url 'edit_service' service.service_id %}" class="btn btn-primary">Edit</a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        // Initialize DataTable with FixedHeader
        const table = $('#serviceTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "pageLength": 50,
        });

        // Add FixedHeader if available
        if ($.fn.dataTable.FixedHeader) {
            new $.fn.dataTable.FixedHeader(table);
        }
    });
</script>
{% endblock %}
