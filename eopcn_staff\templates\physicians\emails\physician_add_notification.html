<!DOCTYPE html>
<html>
<head>
    <style>
        .table-container {
            width: 70%;
            table-layout: fixed;
        }
        .label-cell {
            text-align: right;
            width: 30%;
            padding: 4px;
            line-height: 1.2;
        }
        .value-cell {
            background-color: #DDEBF7;
            width: 70%;
            padding: 4px;
            line-height: 1.2;
        }
        .section-header {
            font-size: 18px;
            margin-top: 10px;
        }
    </style>
</head>
<body>

<h1>New Primary Care Provider Information</h1>

<!-- Primary Care Provider Information Section -->
<h2 class="section-header">Primary Care Provider Details</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell">Last Name:</td>
        <td class="value-cell">{{ physician.last_name }}</td>
    </tr>
    <tr>
        <td class="label-cell">First Name:</td>
        <td class="value-cell">{{ physician.first_name }}</td>
    </tr>
    <tr>
        <td class="label-cell">Title:</td>
        <td class="value-cell">{{ physician.title }}</td>
    </tr>
    <tr>
        <td class="label-cell">Gender:</td>
        <td class="value-cell">{{ physician.gender }}</td>
    </tr>
    <tr>
        <td class="label-cell">Practitioner ID:</td>
        <td class="value-cell">{{ physician.practitioner_id }}</td>
    </tr>
    <tr>
        <td class="label-cell">Primary Email:</td>
        <td class="value-cell">{{ physician.primary_email }}</td>
    </tr>
    <tr>
        <td class="label-cell">Primary Phone:</td>
        <td class="value-cell">{{ physician.primary_phone }}</td>
    </tr>
    <tr>
        <td class="label-cell">Alternate Email:</td>
        <td class="value-cell">{{ physician.alternate_email }}</td>
    </tr>
    <tr>
        <td class="label-cell">Alternate Phone:</td>
        <td class="value-cell">{{ physician.alternate_phone }}</td>
    </tr>
    <tr>
        <td class="label-cell">Email Opt-out:</td>
        <td class="value-cell">{% if physician.do_not_email %}Yes{% else %}No{% endif %}</td>
    </tr>
    <tr>
        <td class="label-cell">Date Signed EOPCN:</td>
        <td class="value-cell">{{ physician.date_signed_eopcn }}</td>
    </tr>
    {% if physician.languages.all %}
    <tr>
        <td class="label-cell">Languages Spoken:</td>
        <td class="value-cell">
            {% for language in physician.languages.all %}
                {{ language.language }}{% if not forloop.last %}, {% endif %}
            {% endfor %}
        </td>
    </tr>
    {% endif %}
</table>

<!-- Clinic Associations Section -->
<h2 class="section-header">Clinic Associations</h2>
{% if clinic_associations %}
    {% for association in clinic_associations %}
        {% if association.clinic %}
            <h3>{% if forloop.first %}Primary{% else %}Additional{% endif %} Clinic Association</h3>
            <table border="0" cellspacing="2" class="table-container">
                <tr>
                    <td class="label-cell">Clinic:</td>
                    <td class="value-cell">{{ association.clinic }}</td>
                </tr>
                <tr>
                    <td class="label-cell">Portion of Practice:</td>
                    <td class="value-cell">{{ association.portion_of_practice }}</td>
                </tr>
                <tr>
                    <td class="label-cell">Accepting Patients:</td>
                    <td class="value-cell">{{ association.accepting_patients }}</td>
                </tr>
                <tr>
                    <td class="label-cell">Include on AFAD Website:</td>
                    <td class="value-cell">{{ association.include_on_afad_website }}</td>
                </tr>
                <tr>
                    <td class="label-cell">Include on EOPCN Website:</td>
                    <td class="value-cell">{{ association.include_on_eopcn_website }}</td>
                </tr>
                <tr>
                    <td class="label-cell">Date Active in Clinic:</td>
                    <td class="value-cell">{{ association.date_active_in_clinic }}</td>
                </tr>
            </table>
        {% endif %}
    {% endfor %}
{% else %}
    <p>No clinic associations available.</p>
{% endif %}

{% if comment %}
<h2 class="section-header">Additional Comments</h2>
<table border="0" cellspacing="2" class="table-container">
    <tr>
        <td class="label-cell">Comment:</td>
        <td class="value-cell">{{ comment }}</td>
    </tr>
</table>
{% endif %}

<p>
    Submitted by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).
</p>
{% if details_url %}
<p>
    <a href="{{ details_url }}">View record in operational database</a>
</p>
{% endif %}

<p>Best regards,</p>
<p>EOPCN Automation Admin</p>

</body>
</html>
