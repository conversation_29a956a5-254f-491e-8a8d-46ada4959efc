{% extends "base.html" %}

{% block title %}Edit Staff Member{% endblock %}

{% block content %}
{% load form_extras %}
{% load static %}

<div class="form-container">
    <h2 class="subheading">Edit Staff Member's Assignment Allocations</h2>
    <form id="editAllocationForm" method="post">
        {% csrf_token %}

        <!-- Display Errors for Staff Form -->
        {% if allocation_formset.errors %}
            <div class="alert alert-danger">
                <ul>
                {% for field, errors in allocation_formset.errors.items %}
                    <li>{{ field }}: {{ errors|join:", " }}</li>
                {% endfor %}
                </ul>
            </div>
        {% endif %}
    </div>
    
    {{ allocation_formset.management_form }} <!-- Management form for nested allocations -->
    
    <div class="form-container">
        <!-- Loop through each allocation form in the formset -->
        <h3>You are editing allocations for <u>{{ assignment.staff.first_name }}</u> <u>{{ assignment.staff.last_name }}</u> for the role of <u>{{ assignment.role.role_name }}</u> who reports to <u>{{ assignment.supervisor.supervisor_name }}</u></h3>
        <div class="form-container">
            
            {% for allocation_form in allocation_formset.forms %}
                <!-- Display Individual Form Errors -->
                {% if allocation_form.errors %}
                    <div class="alert alert-danger">
                        <ul>
                        {% for field, errors in allocation_form.errors.items %}
                            <li>{{ field }}: {{ errors|join:", " }}</li>
                        {% endfor %}
                        </ul>
                    </div>
                {% endif %}
                
                <div class="allocation-form-row">
                    <!-- Centralized/Decentralized -->
                    <div class="form-group">
                        <label>{{ allocation_form.centralized_vs_ric.label }}</label><br>
                        {% for radio in allocation_form.centralized_vs_ric %}
                            <div class="form-check form-check-inline">
                                {{ radio.tag }}
                                <label class="form-check-label">{{ radio.choice_label }}</label>
                            </div>
                        {% endfor %}
                    </div>

                    <!-- Days of the week -->
                    <div class="form-group">
                        <label>Allocated days:</label><br>
                        <div class="form-check form-check-inline">
                            {{ allocation_form.monday }} {{ allocation_form.monday.label_tag }}
                        </div>
                        <div class="form-check form-check-inline">
                            {{ allocation_form.tuesday }} {{ allocation_form.tuesday.label_tag }}
                        </div>
                        <div class="form-check form-check-inline">
                            {{ allocation_form.wednesday }} {{ allocation_form.wednesday.label_tag }}
                        </div>
                        <div class="form-check form-check-inline">
                            {{ allocation_form.thursday }} {{ allocation_form.thursday.label_tag }}
                        </div>
                        <div class="form-check form-check-inline">
                            {{ allocation_form.friday }} {{ allocation_form.friday.label_tag }}
                        </div>
                    </div>

                    <!-- Clinic Field -->
                    <div class="form-group">
                        {{ allocation_form.clinic.label_tag }} 
                        {{ allocation_form.clinic }}
                    </div>

                    <!-- Assignment in Clinic Field -->
                    <div class="form-group">
                        {{ allocation_form.assignment_in_clinic.label_tag }} 
                        {{ allocation_form.assignment_in_clinic }}
                    </div>

                    <!-- Program Field -->
                    <div class="form-group">
                        {{ allocation_form.program.label_tag }} 
                        {{ allocation_form.program }}
                    </div>

                    <!-- FTE -->
                    <div class="form-group">
                        {{ allocation_form.fte.label_tag }} 
                        {{ allocation_form.fte }}
                    </div>

                    <!-- Start Date -->
                    <div class="form-group">
                        {{ allocation_form.start_date.label_tag }} 
                        {{ allocation_form.start_date }}
                    </div>

                    <!-- End Date -->
                    <div class="form-group">
                        {{ allocation_form.end_date.label_tag }} 
                        {{ allocation_form.end_date }}
                    </div>
                </div>
                <hr>
            {% endfor %}
            <button type="button" class="add-allocation btn btn-secondary">+ Add another allocation for this role</button>
        </div>
    </div>
    <div class="button-container">
    </div>
        
    <div class="save-container">
        <button type="submit" class="btn btn-primary mt-3">Save</button>
        <a href="{% url 'list_staff' %}" class="btn-secondary mt-3">Back to Staff List</a>
    </div>
    </form>



<style>
    .form-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .button-container {
        flex: 1;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border-radius: 8px;
        margin-top: 0px;
        margin-bottom: 20px;
    }

    .save-container {
        display: flex;
        justify-content: center;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .form-group {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .form-group label {
        width: 200px;
        margin-right: 10px;
        text-align: left;
    }

    .form-group input, 
    .form-group select, 
    .form-group textarea {
        flex: 1;
        padding: 5px;
        max-width: 100%;
    }

    button {
        width: auto;
        padding: 10px 20px;
    }

    a.btn-secondary {
        margin-left: 10px;
        padding: 10px 20px;
        text-decoration: none;
        background-color: #6c757d;
        color: white;
        border-radius: 4px;
    }

    .allocation-form-row {
        margin-bottom: 20px;
    }

    hr {
        margin: 10px 0;
        border: 0;
        border-top: 1px solid #ccc;
    }

    .allocation-heading {
    text-align: left; /* Centers the text horizontally */
    font-weight: bold;  /* Optional: makes the text bold */
    margin: 10px 0;     /* Adds vertical margin around the text */
    }

    .subheading {
    text-align: center; /* Center alignment */
    }

</style>

{% endblock %}