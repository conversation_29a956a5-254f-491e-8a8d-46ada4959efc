{% extends "base.html" %}

{% block title %}Edit Staff Member{% endblock %}

{% block content %}
{% load form_extras %}
{% load static %}

<div class="form-container">
    <h2 class="subheading">Edit Staff Member: {{ staff.first_name }} {{ staff.last_name }}</h2>
    
    <!-- Page Purpose and Instructions -->
    <div class="info-section">
        <h3>About This Form</h3>
        <p>This form allows you to update staff member information and manage their status within the EOPCN organization.</p>
        
        <div class="important-notes">
            <h4>Important Guidelines:</h4>
            <ul>
                <li><strong>End Date:</strong> Only update this field when a staff member has permanently left the organization. Do NOT use this for temporary leave or absence - staff on leave remain active EOPCN members.</li>
                <li><strong>Currently Active Status:</strong> Should be set to inactive only when a staff member has left the organization (has an end date).</li>
                <li><strong>Additional Required Updates:</strong> When a staff member leaves the organization (has an end date), you must also manually update:
                    <ul>
                        <li>Set the staff member as inactive</li>
                        <li>End-date all current role assignments</li>
                        <li>End-date all current allocations</li>
                        <li>Mark all roles and allocations as inactive</li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>

    <form id="editStaffForm" method="post" enctype="multipart/form-data">
        {% csrf_token %}

        <!-- Display Errors for Staff Form -->
        {% if staff_form.errors %}
            <div class="alert alert-danger">
                <ul>
                {% for field, errors in staff_form.errors.items %}
                    <li>{{ field }}: {{ errors|join:", " }}</li>
                {% endfor %}
                </ul>
            </div>
        {% endif %}
    </div>
        
    <div class="form-container">

        <!-- Staff Details Section -->
        <h3>Staff Profile</h3>
        <hr>

        <!-- Photo Upload Field -->
        <div class="form-group">
            {{ staff_form.photo.label_tag }} <!-- Add label for photo -->
            {{ staff_form.photo }}  <!-- Add input field for photo -->
        </div>

        <div class="form-group">
            {{ staff_form.first_name.label_tag }} 
            {{ staff_form.first_name }}
        </div>

        <div class="form-group">
            {{ staff_form.last_name.label_tag }} 
            {{ staff_form.last_name }}
        </div>
        
        <div class="form-group">
            {{ staff_form.start_date.label_tag }} 
            {{ staff_form.start_date }}
        </div>

        <div class="form-group">
            {{ staff_form.suggested_email.label_tag }} 
            {{ staff_form.suggested_email }}
        </div>

        <div class="form-group">
            {{ staff_form.end_date.label_tag }} 
            {{ staff_form.end_date }}
        </div>
        <div class="helper-text">
            <small class="form-text text-muted">⚠️ Only set when staff permanently leaves the organization</small>
        </div>

        <div class="form-group">
            {{ staff_form.currently_active.label_tag }} 
            {{ staff_form.currently_active }}
        </div>
        <div class="helper-text">
            <small class="form-text text-muted">⚠️ Remember to also update if end date is provided</small>
        </div>

        <div class="form-group">
            {{ staff_form.n95_mask_size.label_tag }} 
            {{ staff_form.n95_mask_size }}
        </div>

        <div class="form-group">
            {{ staff_form.office_number.label_tag }} 
            {{ staff_form.office_number }}
        </div>

        <div class="form-group">
            {{ staff_form.desk_number.label_tag }} 
            {{ staff_form.desk_number }}
        </div>

        <div class="form-group">
            {{ staff_form.phone.label_tag }} 
            {{ staff_form.phone }}
        </div>

        <div class="form-group">
            {{ staff_form.ext.label_tag }} 
            {{ staff_form.ext }}
        </div>

        <div class="form-group">
            {{ staff_form.computer_number.label_tag }} 
            {{ staff_form.computer_number }}    
        </div>
    </div>

<!-- Email Group Selection -->
<div class="form-container">
    {% with form=staff_form %}
        {% include 'staff/email_group_widget.html' %}
    {% endwith %}
</div>

<div class="form-container">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <button type="submit" class="btn btn-primary mt-3">Save</button>
        <a href="{% url 'staff_detail' staff.pk %}" class="btn-secondary mt-3">Back to Staff Details</a>
    </div>
</div>
    </form>

<script src="{% static 'eopcn_staff/js/edit_staff.js' %}"></script>


<style>
    .form-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .button-container {
        flex: 1;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border-radius: 8px;
        margin-top: 0px;
        margin-bottom: 20px;
    }

    .save-container {
        display: flex;
        justify-content: center;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        margin-bottom: 15px;
    }

    .form-group {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .form-group label {
        width: 200px;
        margin-right: 10px;
        text-align: left;
    }

    .form-group input, 
    .form-group select, 
    .form-group textarea {
        flex: 1;
        padding: 5px;
        max-width: 100%;
    }

    button {
        width: auto;
        padding: 10px 20px;
    }

    a.btn-secondary {
        margin-left: 10px;
        padding: 10px 20px;
        text-decoration: none;
        background-color: #6c757d;
        color: white;
        border-radius: 4px;
    }

    .allocation-form-row {
        margin-bottom: 20px;
    }

    hr {
        margin: 10px 0;
        border: 0;
        border-top: 1px solid #ccc;
    }

    .allocation-heading {
    text-align: left; /* Centers the text horizontally */
    font-weight: bold;  /* Optional: makes the text bold */
    margin: 10px 0;     /* Adds vertical margin around the text */
    }

    .subheading {
    text-align: center; /* Center alignment */
    }

    .info-section {
        background-color: #e8f4fd;
        border: 1px solid #b8daff;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .info-section h3 {
        color: #004085;
        margin-bottom: 10px;
    }

    .important-notes {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 12px;
        margin-top: 15px;
    }

    .important-notes h4 {
        color: #856404;
        margin-bottom: 8px;
    }

    .important-notes ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .important-notes li {
        margin-bottom: 5px;
    }

    .form-text {
        font-size: 0.875em;
        color: #6c757d;
        margin-left: 10px;
    }

    .helper-text {
        margin-left: 210px;
        margin-top: -10px;
        margin-bottom: 15px;
    }

</style>

{% endblock %}
