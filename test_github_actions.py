#!/usr/bin/env python
"""
Create a test reminder and show how to test GitHub Actions
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
sys.path.append('c:\\Users\\<USER>\\Py test\\Python-testing\\EOPCNOpApp')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'EOPCNOpApp.settings')
django.setup()

from django.utils import timezone
import pytz
from eopcn_staff.models import StaffLeave, StaffLeaveTypes, Staff

def show_current_time():
    """Show current time for reference"""
    mst = pytz.timezone('US/Mountain')
    now_mst = timezone.now().astimezone(mst)
    now_utc = timezone.now()
    
    print("🕐 CURRENT TIME")
    print("=" * 40)
    print(f"MST Time: {now_mst.strftime('%Y-%m-%d %I:%M %p %Z')}")
    print(f"UTC Time: {now_utc.strftime('%Y-%m-%d %H:%M %Z')}")
    print()

def check_existing_reminders():
    """Check what reminders are currently pending"""
    mst = pytz.timezone('US/Mountain')
    now_mst = timezone.now().astimezone(mst)
    
    pending_reminders = StaffLeave.objects.filter(
        reminder_datetime__isnull=False,
        reminder_sent=False
    )
    
    print("📋 EXISTING PENDING REMINDERS")
    print("=" * 40)
    
    if not pending_reminders:
        print("No pending reminders found")
    else:
        for leave in pending_reminders:
            reminder_mst = leave.reminder_datetime.astimezone(mst)
            is_due = reminder_mst <= now_mst
            status = "🔴 DUE NOW" if is_due else "⏰ FUTURE"
            
            print(f"{status} Leave ID: {leave.leave_id}")
            print(f"   Staff: {leave.staff.first_name} {leave.staff.last_name}")
            print(f"   Time: {reminder_mst.strftime('%Y-%m-%d %I:%M %p %Z')}")
            print(f"   Email: {leave.reminder_email_address}")
            print()

def create_test_reminder():
    """Create a test reminder that's due now"""
    try:
        staff = Staff.objects.first()
        leave_type = StaffLeaveTypes.objects.first()
        
        if not staff or not leave_type:
            print("❌ Need staff member and leave type in database")
            return None
        
        # Create reminder that's due now (1 minute ago)
        mst = pytz.timezone('US/Mountain')
        now_mst = timezone.now().astimezone(mst)
        reminder_time_mst = now_mst - timedelta(minutes=1)  # 1 minute ago
        reminder_time_utc = reminder_time_mst.astimezone(pytz.UTC)
        
        test_leave = StaffLeave.objects.create(
            staff=staff,
            leave_type=leave_type,
            leave_start_date=(now_mst + timedelta(days=7)).date(),
            return_date=(now_mst + timedelta(days=14)).date(),
            reminder_datetime=reminder_time_utc,
            reminder_sent=False,
            reminder_email_address='<EMAIL>',  # Your email
            date_created=timezone.now(),
            created_by='github_test',
            date_modified=timezone.now(),
            modified_by='github_test'
        )
        
        print("✅ CREATED TEST REMINDER")
        print("=" * 40)
        print(f"Leave ID: {test_leave.leave_id}")
        print(f"Staff: {staff.first_name} {staff.last_name}")
        print(f"Reminder Time: {reminder_time_mst.strftime('%Y-%m-%d %I:%M %p %Z')}")
        print(f"Email: <EMAIL>")
        print(f"Status: DUE NOW (1 minute ago)")
        print()
        
        return test_leave
        
    except Exception as e:
        print(f"❌ Error creating test reminder: {e}")
        return None

def show_test_instructions():
    """Show how to test GitHub Actions"""
    print("🚀 HOW TO TEST GITHUB ACTIONS")
    print("=" * 40)
    print("1. Go to your GitHub repository")
    print("2. Click 'Actions' tab")
    print("3. Click 'Daily Leave Reminders' workflow")
    print("4. Click 'Run workflow' button")
    print("5. Click 'Run workflow' to start")
    print("6. Watch the logs in real-time!")
    print()
    print("🔍 WHAT TO LOOK FOR:")
    print("- Green checkmark = Success")
    print("- 'Sent legacy reminder for...' in logs")
    print("- Email in your inbox")
    print()
    print("📧 EMAIL SHOULD GO TO: <EMAIL>")
    print()

if __name__ == "__main__":
    print("🧪 GITHUB ACTIONS TESTING SETUP")
    print("=" * 50)
    
    show_current_time()
    check_existing_reminders()
    
    print("🎯 CREATING NEW TEST REMINDER")
    print("=" * 40)
    test_leave = create_test_reminder()
    
    if test_leave:
        print("✅ Test reminder created successfully!")
        print()
        show_test_instructions()
        
        print("⚡ ALTERNATIVE LOCAL TEST:")
        print("python manage.py send_leave_reminders")
    else:
        print("❌ Failed to create test reminder")
