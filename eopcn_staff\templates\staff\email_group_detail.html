{% extends "base.html" %}

{% block content %}
<h2>{{ email_group.name }} - Group Details</h2>

{% if messages %}
    <script>
        {% for message in messages %}
            alert("{{ message }}");
        {% endfor %}
    </script>
{% endif %}

<div class="group-info">
    <p><strong>Description:</strong> {{ email_group.description|default:"No description" }}</p>
    <p><strong>Status:</strong> {% if email_group.is_active %}Active{% else %}Inactive{% endif %}</p>
    <p><strong>Created:</strong> {{ email_group.date_created|date:"M d, Y" }} by {{ email_group.created_by|default:"Unknown" }}</p>
    {% if email_group.date_modified %}
    <p><strong>Last Modified:</strong> {{ email_group.date_modified|date:"M d, Y" }} by {{ email_group.modified_by|default:"Unknown" }}</p>
    {% endif %}
</div>

<div class="actions">
    <a href="{% url 'edit_email_group' email_group.group_id %}" class="btn btn-primary">Edit Group</a>
    <a href="{% url 'email_groups_list' %}" class="btn btn-secondary">Back to Groups</a>
</div>

<hr>

<h3>Group Members ({{ recipients.count }})</h3>

<!-- Add Member Form -->
<div class="add-member-section">
    <h4>Add Member</h4>
    <form method="post" class="add-member-form">
        {% csrf_token %}
        <div class="form-inline">
            <label for="{{ membership_form.recipient.id_for_label }}">Recipient:</label>
            {{ membership_form.recipient }}
            <button type="submit" name="add_member" class="btn btn-success">Add to Group</button>
        </div>
        {% if membership_form.recipient.errors %}
            <div class="text-danger">{{ membership_form.recipient.errors }}</div>
        {% endif %}
    </form>
</div>

<!-- Current Members Table -->
<div class="members-section">
    <table id="membersTable" class="display">
        <thead>
            <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Date Added</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for recipient in recipients %}
            <tr>
                <td>{{ recipient.name }}</td>
                <td>{{ recipient.email }}</td>
                <td>
                    {% for membership in recipient.emailgroupmembership_set.all %}
                        {% if membership.group == email_group %}
                            {{ membership.date_added|date:"M d, Y" }}
                        {% endif %}
                    {% endfor %}
                </td>
                <td>
                    <form method="post" style="display: inline;">
                        {% csrf_token %}
                        <input type="hidden" name="recipient_id" value="{{ recipient.recipient_id }}">
                        <button type="submit" name="remove_member" class="btn btn-danger btn-sm" 
                                onclick="return confirm('Remove {{ recipient.name }} from this group?')">
                            Remove
                        </button>
                    </form>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="4">No members in this group.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<style>
.group-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.actions {
    margin-bottom: 20px;
}

.add-member-section {
    background-color: #e7f3ff;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.form-inline {
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-inline label {
    font-weight: bold;
}

.members-section {
    margin-top: 20px;
}

table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 16px;
}

th, td {
    border: 1px solid #dddddd;
    text-align: left;
    padding: 8px;
}

tr:nth-child(even) {
    background-color: #f2f2f2;
}

.btn {
    padding: 8px 12px;
    margin-right: 5px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

.btn:hover {
    opacity: 0.8;
}

.text-danger {
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
}

.form-control {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}
</style>

<!-- Include jQuery and DataTables -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>

<script>
$(document).ready(function() {
    $('#membersTable').DataTable({
        "pageLength": 25,
        "order": [[ 0, "asc" ]]
    });
});
</script>

{% endblock %}
