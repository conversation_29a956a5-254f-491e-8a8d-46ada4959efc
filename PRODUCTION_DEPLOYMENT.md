# Production Deployment Checklist

## Environment Variables Required

### Azure App Service Configuration
Set the following environment variables in your Azure App Service:

1. **Django Configuration**
   - `SECRET_KEY`: A secure secret key for Django
   - `DJANGO_ENVIRONMENT`: Set to `production`

2. **Azure AD Authentication**
   - `AZURE_CLIENT_ID`: Your Azure AD application client ID
   - `AZURE_CLIENT_SECRET`: Your Azure AD application client secret
   - `AZURE_TENANT_ID`: Your Azure AD tenant ID

3. **Database Configuration**
   - `DB_NAME`: Database name (default: PCNQITeamSQLDB)
   - `DB_HOST`: Database host (default: pcnqisqlsvr.database.windows.net)
   - `DB_PORT`: Database port (default: 1433)
   - `DB_USER`: Database username (optional if using Managed Identity)
   - `DB_PASSWORD`: Database password (optional if using Managed Identity)

4. **Email Configuration**
   - `EMAIL_HOST_PASSWORD`: Password for the email account

5. **Azure Storage**
   - `AZURE_STORAGE_KEY`: Azure Storage account key

## Azure App Service Settings

### Application Settings
1. Go to Azure Portal → App Services → Your App
2. Navigate to Configuration → Application settings
3. Add all the environment variables listed above

### Connection Strings (Alternative to DB environment variables)
If you prefer using connection strings:
1. Go to Configuration → Connection strings
2. Add a connection string named `DefaultConnection` with type `SQLAzure`

### Managed Identity (Recommended for Database)
1. Go to Identity → System assigned
2. Turn on the system assigned managed identity
3. Grant the managed identity access to your SQL database
4. Remove `DB_USER` and `DB_PASSWORD` environment variables

## Deployment Steps

1. **Prepare your code**
   - Ensure all sensitive data is removed from settings.py
   - Test locally with environment variables

2. **Set environment variables**
   - Configure all required environment variables in Azure App Service

3. **Deploy your application**
   - Use GitHub Actions or Azure DevOps
   - Or deploy directly from VS Code

4. **Run migrations**
   - SSH into your App Service or use the console
   - Run: `python manage.py migrate`

5. **Collect static files**
   - Run: `python manage.py collectstatic --noinput`

6. **Test the deployment**
   - Check that the application loads
   - Test authentication
   - Test database connectivity
   - Test email functionality

## Troubleshooting

### Common Issues

1. **500 Internal Server Error**
   - Check Application Insights or Log Stream for detailed errors
   - Verify all environment variables are set correctly
   - Check database connectivity

2. **Authentication Issues**
   - Verify Azure AD redirect URIs include your production URL
   - Check Azure AD application configuration
   - Ensure AZURE_CLIENT_SECRET is correct

3. **Database Connection Issues**
   - If using Managed Identity, ensure it's enabled and has database permissions
   - If using username/password, verify credentials
   - Check firewall rules allow Azure services

4. **Static Files Not Loading**
   - Run `python manage.py collectstatic`
   - Check STATIC_ROOT and STATIC_URL settings

### Monitoring
- Enable Application Insights for detailed monitoring
- Use Log Stream for real-time log viewing
- Set up alerts for critical errors

## Security Considerations

1. **Never commit sensitive data to version control**
2. **Use Managed Identity when possible**
3. **Regularly rotate secrets and keys**
4. **Monitor access logs**
5. **Keep dependencies updated**

## Performance Optimization

1. **Enable compression in Azure App Service**
2. **Use Azure CDN for static files**
3. **Configure appropriate App Service plan**
4. **Monitor performance metrics**
