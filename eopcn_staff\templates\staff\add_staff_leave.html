{% extends 'base.html' %}
{% load static %}

{% block content %}
<!-- Link to the external stylesheet -->
<link rel="stylesheet" type="text/css" href="{% static 'eopcn_staff/css/staff_form.css' %}">

<div class="form-container">
    <h2 class="subheading">Add Leave for Staff Member</h2>
    
    <!-- Page Purpose and Instructions -->
    <div class="info-section">
        <h3>About This Form</h3>
        <p>This form manages extended leave periods for staff members who will be away from work for an extended period and require coverage.</p>
        
        <div class="important-notes">
            <h4>Important Guidelines:</h4>
            <ul>
                <li><strong>Leave Dates:</strong> Set start and anticipated return dates for the staff member's extended leave.</li>
                <li><strong>Coverage Management:</strong> Multiple staff can cover a single leave, as coverage needs may change if covering staff also go on leave or leave the organization.</li>
                <li><strong>Coverage Flexibility:</strong> Different staff can cover different aspects of the same leave period.</li>
                <li><strong>Critical Reminder:</strong> When the staff member's return date changes or they actually return to work, supervisors MUST update:
                    <ul>
                        <li>The leave return date</li>
                        <li>All covering staff end dates to match</li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>

    <form method="post">
        {% csrf_token %}
        
        <!-- Leave Fields -->
        <div class="form-group">
            {{ form.leave_type.label_tag }}
            {{ form.leave_type }}
            {% if form.leave_type.errors %}
                <div class="alert alert-danger">
                    {% for error in form.leave_type.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="form-group">
            {{ form.leave_start_date.label_tag }}
            {{ form.leave_start_date }}
            {% if form.leave_start_date.errors %}
                <div class="alert alert-danger">
                    {% for error in form.leave_start_date.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="form-group">
            {{ form.return_date.label_tag }}
            {{ form.return_date }}
            {% if form.return_date.errors %}
                <div class="alert alert-danger">
                    {% for error in form.return_date.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        <div class="helper-text">
            <small class="form-text text-muted">⚠️ Remember to update this date and all coverage end dates if return plans change</small>
        </div>

        {% comment %} <div class="form-group">
            {{ form.reminder_datetime.label_tag }}
            {{ form.reminder_datetime }}
            {% if form.reminder_datetime.errors %}
                <div class="alert alert-danger">
                    {% for error in form.reminder_datetime.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
            <small class="text-muted">{{ form.reminder_datetime.help_text }}</small>
        </div>
        <div class="form-group">
            {{ form.send_reminder_to_self }} {{ form.send_reminder_to_self.label_tag }}
        </div>
        <div class="form-group">
            {{ form.reminder_email_group.label_tag }}
            {{ form.reminder_email_group }}
            {% if form.reminder_email_group.errors %}
                <div class="alert alert-danger">
                    {% for error in form.reminder_email_group.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
            <small class="text-muted">{{ form.reminder_email_group.help_text }}</small>
        </div> {% endcomment %}
</div>

<div class="form-container">
    <!-- Staff Coverage Section -->
    <h3 class="subheading">Staff Coverage</h3>
    {{ coverage_formset.management_form }}
    {% for coverage_form in coverage_formset %}
        <div class="formset-item">
            {# Include hidden fields first #}
            {% for hidden_field in coverage_form.hidden_fields %}
                {{ hidden_field }}
            {% endfor %}
            
            {# Render only visible fields that aren't email-related or DELETE #}
            {% for field in coverage_form.visible_fields %}
                {% if field.name != 'email_leadership' and field.name != 'email_group' and field.name != 'comment' and field.name != 'DELETE' %}
                <div class="form-group">
                    {{ field.label_tag }}
                    {{ field }}
                    {% if field.help_text %}
                        <small>{{ field.help_text }}</small>
                    {% endif %}
                    {% if field.errors %}
                        <div class="alert alert-danger">
                            {% for error in field.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                {% if field.name == 'covering_staff' %}
                <div class="helper-text">
                    <small class="form-text text-muted">ℹ️ Missing a covering staff member? <a href="{% url 'list_staff' %}" target="_blank">Check staff list here</a></small>
                </div>
                {% endif %}
                {% endif %}
            {% endfor %}
        </div>
    {% endfor %}
</div>

<!-- Email Group Widget -->
<div class="form-container">
    {% with form=form %}
        {% include 'staff/email_group_widget.html' %}
    {% endwith %}
</div>

<!-- Save Button -->
<div class="form-container">
    <div style="text-align: center;">
        <button type="submit" class="btn btn-primary">Save</button>
    </div>
</div>
</form>

<style>
    .info-section {
        background-color: #e8f4fd;
        border: 1px solid #b8daff;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .info-section h3 {
        color: #004085;
        margin-bottom: 10px;
    }

    .important-notes {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 12px;
        margin-top: 15px;
    }

    .important-notes h4 {
        color: #856404;
        margin-bottom: 8px;
    }

    .important-notes ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .important-notes li {
        margin-bottom: 5px;
    }

    .form-text {
        font-size: 0.875em;
        color: #6c757d;
        margin-left: 10px;
    }

    .helper-text {
        margin-left: 210px;
        margin-top: -10px;
        margin-bottom: 15px;
    }
</style>
{% endblock %}