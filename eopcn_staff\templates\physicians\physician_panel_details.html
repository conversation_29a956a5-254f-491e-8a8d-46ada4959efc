{% extends "base.html" %}

{% block title %}Primary Care Provider Panel Details{% endblock %}

{% block content %}
{% load custom_filters %}

<!DOCTYPE html>
<html>
<head>
<style>
body {
  font-family: arial, sans-serif;
  margin: 0;
  padding: 0;
}

.column {
  float: left;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
}

.row::after {
  content: "";
  clear: both;
  display: table;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

th, td {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 8px;
}

tr:nth-child(even) {
  background-color: #dddddd;
}

h2 {
  text-align: center;
}

.button {
  border: none;
  color: white;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 4px;
}

.button1 {
    background-color: #008CBA;
}

.button:hover {
    background-color: #006e92;
}

.link-button {
    display: inline-block;
    padding: 5px 10px;
    font-size: 12px;
    background-color: #73b3f8;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    margin-right: 5px;
}

.link-button:hover {
    background-color: #0056b3;
}

.dataTables_filter, .dataTables_length {
    padding-bottom: 10px;
}

.dataTables_filter {
    float: left !important;
    text-align: left !important;
}

.dataTables_length {
    float: right !important;
    text-align: right !important;
}

@media screen and (max-width: 650px) {
  .column {
    width: 100%;
    display: block;
  }
}

.green-button {
  background-color: #5dbea3;
  color: white;
  padding: 10px 15px;
  border-radius: 5px;
  text-decoration: none;
  display: inline-block;
  margin-bottom: 10px;
}

.green-button:hover {
  background-color: darkgreen;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 15px;
  border: 1px solid #c3e6cb;
  border-radius: 5px;
  margin-bottom: 20px;
  text-align: center;
  font-weight: bold;
}
</style>
</head>
<body>

<h2>Primary Care Provider Panel Details</h2>

<!-- Process messages as JavaScript alerts instead of inline HTML -->
{% if messages %}
    <script>
        {% for message in messages %}
            alert("{{ message }}");
        {% endfor %}
    </script>
{% endif %}

<!-- Move the import panel button to the right side and update styling -->
<div style="text-align: right; margin-bottom: 15px;">
    <a href="{% url 'physician_panel_import' %}" class="green-button">Import Primary Care Provider Panel Data</a>
</div>

<div class="row">
  <div class="column">
    <table id="pcndatatable" class="display">
      <thead>
        <tr>
            <th>Primary Care Provider Name</th>
            <th>Clinic Name</th>
            <th>Panel Size</th>
            <th>Report Year</th>
            <th>Report Month</th>
        </tr>
      </thead>
      <tbody>
        {% for physician in physicians %}
          <tr>
            <td>
                {% if physician.physician_id %}
                    <a href="{% url 'physician_detail' physician.physician_id %}">{{ physician.physician_name }}</a>
                {% else %}
                    {{ physician.physician_name }}
                {% endif %}
            </td>
            <td>
                {% if physician.clinic_id %}
                    <a href="{% url 'clinic_detail' physician.clinic_id %}">{{ physician.clinic_name }}</a>
                {% else %}
                    {{ physician.clinic_name }}
                {% endif %}
            </td>
            <td>{{ physician.panel_size_oct_2024|default_if_none:"" }}</td>
            <td>{{ physician.panel_size_report_year|default_if_none:"" }}</td>
            <td>{{ physician.panel_size_report_month|default_if_none:"" }}</td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

{% load static %}
<script src="{% static 'eopcn_staff/js/datatables.js' %}"></script>

</body>
</html>
{% endblock %}
