{% extends 'base.html' %}
{% load static %}
{% load tz %}

{% block content %}
<!-- Link to the external stylesheet -->
<link rel="stylesheet" type="text/css" href="{% static 'eopcn_staff/css/staff_form.css' %}">

<div class="form-container">
    <h2 class="subheading">Edit Leave for {{ staff.last_name }}, {{ staff.first_name }}</h2>
    
    <!-- Page Purpose and Instructions -->
    <div class="info-section">
        <h3>About This Form</h3>
        <p>This form manages extended leave periods for staff members and their coverage arrangements.</p>
        
        <div class="important-notes">
            <h4>Important Guidelines:</h4>
            <ul>
                <li><strong>Return Date Updates:</strong> When staff return dates change or they actually return to work, you MUST also update all covering staff end dates to match.</li>
                <li><strong>Coverage Changes:</strong> Coverage can be modified as needed - staff may leave during coverage periods requiring new coverage arrangements.</li>
                <li><strong>Multiple Coverage:</strong> Different staff can cover different aspects of the same leave period.</li>
                <li><strong>Coordination Required:</strong> Always ensure coverage dates align with the actual leave period.</li>
            </ul>
        </div>
    </div>
    
    {% if messages %}
    <div class="messages">
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }}">{{ message }}</div>
        {% endfor %}
    </div>
    {% endif %}
    
    <form method="post">
        {% csrf_token %}
        {{ form.non_field_errors }}
        
        <!-- Leave Details Section -->
        <h3 class="subheading">Leave Details</h3>
        
        <div class="form-group">
            {{ form.leave_type.label_tag }}
            {{ form.leave_type }}
            {% if form.leave_type.errors %}
                <div class="alert alert-danger">
                    {% for error in form.leave_type.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="form-group">
            {{ form.leave_start_date.label_tag }}
            {{ form.leave_start_date }}
            {% if form.leave_start_date.errors %}
                <div class="alert alert-danger">
                    {% for error in form.leave_start_date.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="form-group">
            {{ form.return_date.label_tag }}
            {{ form.return_date }}
            {% if form.return_date.errors %}
                <div class="alert alert-danger">
                    {% for error in form.return_date.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        <div class="helper-text">
            <small class="form-text text-muted">⚠️ Remember to update all coverage end dates when this changes</small>
        </div>

        {% comment %} <div class="form-group">
            {{ form.reminder_datetime.label_tag }}
            {{ form.reminder_datetime }}
            {% if form.reminder_datetime.errors %}
                <div class="alert alert-danger">
                    {% for error in form.reminder_datetime.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
            <small class="text-muted">{{ form.reminder_datetime.help_text }}</small>
            {% if staff_leave.reminder_datetime %}
                <div class="text-info">
                    Existing reminder: {{ staff_leave.reminder_datetime|timezone:"US/Mountain"|date:"Y-m-d H:i" }} MST
                </div>
            {% endif %}
        </div>
        <div class="form-group">
            {{ form.send_reminder_to_self }} {{ form.send_reminder_to_self.label_tag }}
        </div>
        <div class="form-group">
            {{ form.reminder_email_group.label_tag }}
            {{ form.reminder_email_group }}
            {% if form.reminder_email_group.errors %}
                <div class="alert alert-danger">
                    {% for error in form.reminder_email_group.errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}
            <small class="text-muted">{{ form.reminder_email_group.help_text }}</small>
        </div> {% endcomment %}
</div>

<div class="form-container">
    <!-- Staff Coverage Section -->
    <h4 class="subheading">Staff Coverage</h4>
    <div class="coverage-info">
        <small class="text-info">ℹ️ Update coverage end dates when the return date changes above</small>
    </div>
    
    <!-- Very important: Include the management form -->
    {{ coverage_formset.management_form }}
    {{ coverage_formset.non_form_errors }}
    
    {% for coverage_form in coverage_formset %}
        <div class="formset-item">
            <h5 class="subheading">Coverage {{ forloop.counter }}</h5>
            
            {# Important: Include all hidden fields first #}
            {% for hidden_field in coverage_form.hidden_fields %}
                {{ hidden_field }}
            {% endfor %}
            
            {# Render visible fields (excluding email fields and DELETE) #}
            {% for field in coverage_form.visible_fields %}
                {% if field.name != "DELETE" and field.name != 'email_leadership' and field.name != 'email_group' and field.name != 'comment' %}
                <div class="form-group">
                    {{ field.label_tag }}
                    {{ field }}
                    {% if field.help_text %}
                        <small class="text-muted">{{ field.help_text }}</small>
                    {% endif %}
                    {% if field.errors %}
                        <div class="alert alert-danger">
                            {% for error in field.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                {% if field.name == 'covering_staff' %}
                <div class="helper-text">
                    <small class="form-text text-muted">ℹ️ Missing a covering staff member? <a href="{% url 'list_staff' %}" target="_blank">Check staff list here</a></small>
                </div>
                {% endif %}
                {% endif %}
            {% endfor %}
            
            {# Handle DELETE field as the last field #}
            {% if coverage_form.DELETE %}
                <div class="form-group delete-checkbox">
                    {{ coverage_form.DELETE.label_tag }}
                    {{ coverage_form.DELETE }}
                    <small class="text-muted">Check to remove this coverage</small>
                </div>
            {% endif %}
        </div>
        {% if not forloop.last %}<hr>{% endif %}
    {% endfor %}
</div>
          
<!-- Email Group Widget -->
<div class="form-container">
    {% with form=form %}
        {% include 'staff/email_group_widget.html' %}
    {% endwith %}
</div>

<!-- Actions Section -->
<div class="form-container">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <button type="submit" class="btn btn-primary" name="update" value="update">Update Leave Record</button>
        <button type="submit" class="btn btn-danger" name="delete" value="delete" onclick="return confirm('Are you sure you want to delete this entire leave record? This action cannot be undone.');">Delete Entire Record</button>
    </div>
</div>
</form>

<style>
    .nested-form-section {
        margin: 20px 0;
        padding: 15px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
    }
    
    .formset-item {
        margin-bottom: 15px;
        padding: 10px;
        background-color: white;
        border: 1px solid #e9ecef;
        border-radius: 3px;
    }
    
    .formset-item h5 {
        margin-top: 0;
        margin-bottom: 15px;
        color: #495057;
        font-size: 1.1em;
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .checkbox-group label {
        margin: 0;
        font-weight: normal;
    }
    
    .button-container {
        margin-top: 20px;
        display: flex;
        gap: 15px;
        align-items: center;
    }
    
    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }
    
    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }
    
    .text-muted {
        color: #6c757d;
        font-size: 0.875em;
        font-style: italic;
    }
    
    .delete-checkbox {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        padding: 10px;
        border-radius: 3px;
        margin-bottom: 10px;
    }

    .info-section {
        background-color: #e8f4fd;
        border: 1px solid #b8daff;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .info-section h3 {
        color: #004085;
        margin-bottom: 10px;
    }

    .important-notes {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 12px;
        margin-top: 15px;
    }

    .important-notes h4 {
        color: #856404;
        margin-bottom: 8px;
    }

    .important-notes ul {
        margin-bottom: 0;
        padding-left: 20px;
    }

    .important-notes li {
        margin-bottom: 5px;
    }

    .form-text {
        font-size: 0.875em;
        color: #6c757d;
        margin-left: 10px;
    }

    .helper-text {
        margin-left: 210px;
        margin-top: -10px;
        margin-bottom: 15px;
    }

    .coverage-info {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 15px;
    }

    .text-info {
        color: #0c5460;
        font-size: 0.875em;
    }
</style>
{% endblock %}
