name: Daily Leave Reminders
# Updated workflow with enhanced debugging

on:
  schedule:
    # Run daily at 9:00 AM MST (15:00 UTC - adjust for your timezone)
    # MST is UTC-7 in summer (MDT) and UTC-6 in winter
    # 9 AM MST = 15:00 UTC (summer) or 16:00 UTC (winter)
    - cron: '0 15 * * *'  # 9 AM MST (summer time)
    - cron: '0 16 * * *'  # 9 AM MST (winter time)
  
  # Allow manual triggering for testing
  workflow_dispatch:

jobs:
  send-reminders:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
    
    - name: Install system dependencies
      run: |
        # Install Microsoft ODBC Driver for SQL Server
        curl https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -
        curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list | sudo tee /etc/apt/sources.list.d/msprod.list
        sudo apt-get update
        sudo ACCEPT_EULA=Y apt-get install -y msodbcsql18
        # Install unixODBC development headers
        sudo apt-get install -y unixodbc-dev

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Set up environment variables
      env:
        # Database connection settings
        DB_NAME: ${{ secrets.DB_NAME }}
        DB_USER: ${{ secrets.DB_USER }}
        DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
        DB_HOST: ${{ secrets.DB_HOST }}
        DB_PORT: ${{ secrets.DB_PORT }}
        # Email settings
        EMAIL_HOST: ${{ secrets.EMAIL_HOST }}
        EMAIL_PORT: ${{ secrets.EMAIL_PORT || '587' }}
        EMAIL_HOST_USER: ${{ secrets.EMAIL_HOST_USER }}
        EMAIL_HOST_PASSWORD: ${{ secrets.EMAIL_HOST_PASSWORD }}
        EMAIL_USE_TLS: ${{ secrets.EMAIL_USE_TLS }}
        # Django settings
        DJANGO_SECRET_KEY: ${{ secrets.DJANGO_SECRET_KEY }}
      run: |
        echo "Environment variables set"
        echo "DB_HOST: ${DB_HOST:-pcnqisqlsvr.database.windows.net}"
        echo "DB_PORT: ${DB_PORT:-1433}"
        echo "DB_NAME: ${DB_NAME:-PCNQITeamSQLDB}"
        echo "DB_USER: ${DB_USER:0:3}***" # Show only first 3 chars for security

    - name: Test database connectivity
      env:
        DB_NAME: ${{ secrets.DB_NAME }}
        DB_USER: ${{ secrets.DB_USER }}
        DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
        DB_HOST: ${{ secrets.DB_HOST }}
        DB_PORT: ${{ secrets.DB_PORT }}
        DJANGO_SECRET_KEY: ${{ secrets.DJANGO_SECRET_KEY }}
      run: |
        echo "Testing database connectivity..."
        # Test if we can reach the SQL Server port
        timeout 10 bash -c "</dev/tcp/${DB_HOST:-pcnqisqlsvr.database.windows.net}/${DB_PORT:-1433}" && echo "Port is reachable" || echo "Port is not reachable"


    - name: Test Django configuration
      env:
        # Database connection settings
        DB_NAME: ${{ secrets.DB_NAME }}
        DB_USER: ${{ secrets.DB_USER }}
        DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
        DB_HOST: ${{ secrets.DB_HOST }}
        DB_PORT: ${{ secrets.DB_PORT }}
        # Email settings (required by Django settings validation)
        EMAIL_HOST: ${{ secrets.EMAIL_HOST }}
        EMAIL_PORT: ${{ secrets.EMAIL_PORT || '587' }}
        EMAIL_HOST_USER: ${{ secrets.EMAIL_HOST_USER }}
        EMAIL_HOST_PASSWORD: ${{ secrets.EMAIL_HOST_PASSWORD }}
        EMAIL_USE_TLS: ${{ secrets.EMAIL_USE_TLS }}
        # Django settings
        DJANGO_SECRET_KEY: ${{ secrets.DJANGO_SECRET_KEY }}
      run: |
        echo "Testing Django configuration..."
        python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'EOPCNOpApp.settings')
try:
    import django
    django.setup()
    print('Django setup successful!')
    from django.conf import settings
    print(f'Database engine: {settings.DATABASES[\"default\"][\"ENGINE\"]}')
    print(f'Database host: {settings.DATABASES[\"default\"][\"HOST\"]}')
    print(f'Database name: {settings.DATABASES[\"default\"][\"NAME\"]}')
    print(f'Database user: {settings.DATABASES[\"default\"][\"USER\"][:5]}***')
except Exception as e:
    print(f'Django setup failed: {e}')
    import traceback
    traceback.print_exc()
    exit(1)
"

    - name: Run database migrations (if needed)
      env:
        # Database connection settings
        DB_NAME: ${{ secrets.DB_NAME }}
        DB_USER: ${{ secrets.DB_USER }}
        DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
        DB_HOST: ${{ secrets.DB_HOST }}
        DB_PORT: ${{ secrets.DB_PORT }}
        # Email settings (required by Django settings validation)
        EMAIL_HOST: ${{ secrets.EMAIL_HOST }}
        EMAIL_PORT: ${{ secrets.EMAIL_PORT || '587' }}
        EMAIL_HOST_USER: ${{ secrets.EMAIL_HOST_USER }}
        EMAIL_HOST_PASSWORD: ${{ secrets.EMAIL_HOST_PASSWORD }}
        EMAIL_USE_TLS: ${{ secrets.EMAIL_USE_TLS }}
        # Django settings
        DJANGO_SECRET_KEY: ${{ secrets.DJANGO_SECRET_KEY }}
      run: |
        echo "Running Django system check..."
        python manage.py check --verbosity=2
        echo "Running migration check..."
        python manage.py migrate --check --verbosity=2
    
    - name: Send leave reminders
      env:
        # Database connection settings
        DB_NAME: ${{ secrets.DB_NAME }}
        DB_USER: ${{ secrets.DB_USER }}
        DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
        DB_HOST: ${{ secrets.DB_HOST }}
        DB_PORT: ${{ secrets.DB_PORT }}
        # Email settings
        EMAIL_HOST: ${{ secrets.EMAIL_HOST }}
        EMAIL_PORT: ${{ secrets.EMAIL_PORT || '587' }}
        EMAIL_HOST_USER: ${{ secrets.EMAIL_HOST_USER }}
        EMAIL_HOST_PASSWORD: ${{ secrets.EMAIL_HOST_PASSWORD }}
        EMAIL_USE_TLS: ${{ secrets.EMAIL_USE_TLS }}
        # Django settings
        DJANGO_SECRET_KEY: ${{ secrets.DJANGO_SECRET_KEY }}
      run: |
        echo "Checking for leave reminders..."
        python manage.py send_leave_reminders
        echo "Reminder check completed!"
    
    - name: Create automatic reminders (optional)
      env:
        # Database connection settings
        DB_NAME: ${{ secrets.DB_NAME }}
        DB_USER: ${{ secrets.DB_USER }}
        DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
        DB_HOST: ${{ secrets.DB_HOST }}
        DB_PORT: ${{ secrets.DB_PORT }}
        # Email settings (required by Django settings validation)
        EMAIL_HOST: ${{ secrets.EMAIL_HOST }}
        EMAIL_PORT: ${{ secrets.EMAIL_PORT || '587' }}
        EMAIL_HOST_USER: ${{ secrets.EMAIL_HOST_USER }}
        EMAIL_HOST_PASSWORD: ${{ secrets.EMAIL_HOST_PASSWORD }}
        EMAIL_USE_TLS: ${{ secrets.EMAIL_USE_TLS }}
        # Django settings
        DJANGO_SECRET_KEY: ${{ secrets.DJANGO_SECRET_KEY }}
      run: |
        echo "Creating automatic reminders for new leaves..."
        python manage.py send_leave_reminders --create-auto-reminders
        echo "Automatic reminder creation completed!"
      continue-on-error: true  # Don't fail if new system isn't available
    
    - name: Upload logs (if any errors)
      if: failure()
      uses: actions/upload-artifact@v4
      with:
        name: reminder-logs
        path: logs/
        retention-days: 7
