{% extends "base.html" %}

{% block content %}
<h2>Add Email Group</h2>

{% if messages %}
    <script>
        {% for message in messages %}
            alert("{{ message }}");
        {% endfor %}
    </script>
{% endif %}

<form method="post">
    {% csrf_token %}
    
    <div class="form-group">
        <label for="{{ form.name.id_for_label }}">Group Name:</label>
        {{ form.name }}
        {% if form.name.errors %}
            <div class="text-danger">{{ form.name.errors }}</div>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label for="{{ form.description.id_for_label }}">Description:</label>
        {{ form.description }}
        {% if form.description.errors %}
            <div class="text-danger">{{ form.description.errors }}</div>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label>
            {{ form.is_active }}
            Active
        </label>
        {% if form.is_active.errors %}
            <div class="text-danger">{{ form.is_active.errors }}</div>
        {% endif %}
    </div>

    <!-- Email Group Widget -->
    {% include 'staff/email_group_widget.html' %}

    <button type="submit" class="btn btn-primary">Save Email Group</button>
    <a href="{% url 'email_groups_list' %}" class="btn btn-secondary">Cancel</a>
</form>

<style>
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.btn {
    padding: 10px 15px;
    margin-right: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn:hover {
    opacity: 0.8;
}

.text-danger {
    color: #dc3545;
    font-size: 14px;
}
</style>

{% endblock %}
